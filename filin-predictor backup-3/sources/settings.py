from pathlib import Path
from typing import Literal

import yaml
import torch
from torch import dtype as TorchDtype
from pydantic import BaseModel, Field, model_validator, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict


def locate_folder_path(folder_name: str = 'logs') -> Path | None:
    """Метод поиска папки относительно пути нахождения"""
    abspath = Path(__file__).absolute()
    for parent in abspath.parents:
        target_path = parent.joinpath(folder_name)
        if target_path.exists() and target_path.is_dir():
            return target_path
    return None

# Найдем папку с ресурсами и проверим нахождение папки "data", при отсутствии создадим
resources_folder_path = locate_folder_path('resources')
if not resources_folder_path:
    raise FileNotFoundError(f'Не удалось обнаружить папку "resources" в проекте')
data_folder_path = Path(resources_folder_path / 'data')
if not data_folder_path.exists():
    data_folder_path.mkdir(parents=True, exist_ok=True)

class PathProjectSettings(BaseModel):
    """Настройки путей проекта"""
    local_repo_path: Path = Field(default=Path(data_folder_path / 'filin-gatling-test'), description="Локальный путь для клонирования репозитория")
    code_results_path: Path = Field(default=Path(data_folder_path / 'filin-gatling-test/results/code'), description="Путь для сохранения результатов анализа кода")
    chat_results_path: Path = Field(default=Path(data_folder_path / 'filin-gatling-test/results/graphql'), description="Путь для сохранения результатов анализа GraphQL")
    training_output_dir: Path = Field(default=Path(resources_folder_path / 'models'), description="Директория для сохранения моделей")
    training_results_dir: Path = Field(default=Path(resources_folder_path / 'results'), description="Директория для сохранения результатов обучения")

    @model_validator(mode="after")
    def resolve_paths(self) -> "PathProjectSettings":
        """Делает пути абсолютными и строит вложенные пути, если нужно."""
        if not self.local_repo_path.is_absolute():
            self.local_repo_path = data_folder_path / self.local_repo_path

        # Автостроим code/chat_results_path, если они не абсолютные
        if not self.code_results_path.is_absolute():
            self.code_results_path = self.local_repo_path / self.code_results_path
        if not self.chat_results_path.is_absolute():
            self.chat_results_path = self.local_repo_path / self.chat_results_path
        if not self.training_output_dir.is_absolute():
            self.training_output_dir = self.resources_folder_path / self.training_output_dir
        if not self.training_results_dir.is_absolute():
            self.training_results_dir = self.resources_folder_path / self.training_results_dir

        return self

class DataSettings(BaseModel):
    """Настройки для работы с данными."""
    seed: int = Field(default=42, description="Seed для воспроизводимости результатов")
    keep_results_subdirs: list[str] = Field(default_factory=list, description="Список поддиректорий результатов для сохранения")
    test_split_ratio: float = Field(default=0.2, ge=0.0, le=1.0, description="Доля данных для тестовой выборки (от 0 до 1)")
    min_target_time: float = Field(default=0, ge=0.0, description="Минимальное время отклика для фильтрации данных (секунды)")
    target_transformation: bool = Field(default=True, description="Применять ли трансформацию к целевой переменной")
    include_input_length: bool = Field(default=True, description="Включать ли длину входных данных как признак")
    include_concurrent_requests: bool = Field(default=True, description="Включать ли количество параллельных запросов как признак")
    include_model_instances: bool = Field(default=True, description="Включать ли количество экземпляров модели как признак")
    outlier_removal_iqr_multiplier: float = Field(default=3.0, ge=0.0, description="Множитель IQR для удаления выбросов (0 - отключить)")

class QuantizationSettings(BaseModel):
    """Настройки квантизации модели для уменьшения потребления памяти."""
    model_config = {"arbitrary_types_allowed": True}

    load_in_4bit: bool = Field(default=True, description="Загружать модель в 4-битной квантизации")
    load_in_8bit: bool = Field(default=False, description="Загружать модель в 8-битной квантизации")
    bnb_4bit_quant_type: Literal["nf4", "fp4"] = Field(default="nf4", description="Тип 4-битной квантизации BitsAndBytes")
    bnb_4bit_compute_dtype: TorchDtype = Field(default=torch.bfloat16, description="Тип данных для вычислений при 4-битной квантизации")
    bnb_4bit_use_double_quant: bool = Field(default=True, description="Использовать двойную квантизацию для 4-бит")

    @field_validator("bnb_4bit_compute_dtype", mode="before")
    def parse_dtype(cls, v):
        if isinstance(v, torch.dtype):
            return v
        raw = v.replace('torch.', '') if isinstance(v, str) else v
        if raw == 'bfloat16':
            return torch.bfloat16 if torch.cuda.is_bf16_supported() else torch.float16
        try:
            return getattr(torch, raw)
        except AttributeError:
            raise ValueError(f"Неверный dtype: {v}")

    @model_validator(mode='after')
    def validate_quantization(self):
        """Проверка, что не включены одновременно 4-бит и 8-бит квантизация."""
        if self.load_in_4bit and self.load_in_8bit:
            raise ValueError("Нельзя одновременно использовать 4-битную и 8-битную квантизацию")
        return self

class ModelSettings(BaseModel):
    """Настройки модели эмбеддера и головы предсказания."""
    base_embedder_name: str = Field(default="intfloat/multilingual-e5-large-instruct", description="Название базовой модели эмбеддера из HuggingFace")
    quantization: QuantizationSettings = Field(default_factory=QuantizationSettings, description="Настройки квантизации")
    flash_attention_2: bool = Field(default=True, description="Использовать Flash Attention 2 для ускорения")
    torch_compile: bool = Field(default=True, description="Использовать torch.compile для оптимизации")
    compile_mode: Literal["default", "reduce-overhead", "max-autotune"] = Field(default="default", description="Режим компиляции PyTorch")

class EarlyStoppingSettings(BaseModel):
    """Настройки ранней остановки обучения."""
    enabled: bool = Field(default=True, description="Включить раннюю остановку")
    patience: int = Field(default=5, gt=0, description="Количество эпох без улучшения до остановки")
    metric: Literal["eval_loss", "eval_mae", "eval_rmse"] = Field(default="eval_mae", description="Метрика для отслеживания улучшения")
    mode: Literal["min", "max"] = Field(default="min", description="Режим оптимизации метрики (min для loss/mae/rmse)")

class TrainingSettings(BaseModel):
    """Настройки процесса обучения модели."""
    gradient_accumulation_steps: int = Field(default=8, gt=0, description="Шаги накопления градиентов")
    lr_scheduler_type: Literal["cosine", "linear", "reduceonplateau", "none"] = Field(default="cosine", description="Тип планировщика скорости обучения")
    warmup_ratio: float = Field(default=0.1, ge=0.0, le=1.0, description="Доля шагов для прогрева планировщика")
    weight_decay: float = Field(default=0.01, ge=0.0, description="Коэффициент L2-регуляризации")
    optimizer: Literal["AdamW", "SGD"] = Field(default="AdamW", description="Оптимизатор для обучения")
    loss_function: Literal["HuberLoss", "MSELoss", "L1Loss"] = Field(default="HuberLoss", description="Функция потерь для регрессии")
    early_stopping: EarlyStoppingSettings = Field(default_factory=EarlyStoppingSettings, description="Настройки ранней остановки")
    save_strategy: Literal["epoch", "steps", "no"] = Field(default="epoch", description="Стратегия сохранения чекпоинтов")
    save_total_limit: int | None = Field(default=1, ge=1, description="Максимальное число сохраняемых чекпоинтов (None - без лимита)")
    evaluation_strategy: Literal["epoch", "steps", "no"] = Field(default="epoch", description="Стратегия оценки модели")
    logging_steps: int = Field(default=10, gt=0, description="Частота логирования метрик в TensorBoard")
    log_histograms_freq: int = Field(default=0, ge=0, description="Частота логирования гистограмм (эпохи, 0 - отключить)")
    log_scatter_freq: int = Field(default=1, ge=0, description="Частота логирования графика рассеяния (эпохи, 0 - отключить)")
    resume_from_checkpoint: bool = Field(default=True, description="Возобновить обучение с чекпоинта")
    resume_mode: Literal["latest", "best"] = Field(default="latest", description="Какой чекпоинт использовать при возобновлении")

class PeftSettings(BaseModel):
    """Настройки PEFT (Parameter-Efficient Fine-Tuning) для эффективного дообучения."""
    enabled: bool = Field(default=True, description="Включить PEFT для данной задачи")
    r: int = Field(default=64, gt=0, description="Ранг матриц LoRA (размерность низкоранговой декомпозиции)")
    lora_alpha: int = Field(default=64, gt=0, description="Коэффициент масштабирования LoRA (рекомендуется равный или 2×r)")
    lora_dropout: float = Field(default=0.1, ge=0.0, le=1.0, description="Вероятность dropout для слоев LoRA")
    auto_find_target_modules: bool = Field(default=True, description="Автоматически найти целевые модули для LoRA")
    target_module_keywords: list[str] | None = Field(default=None, description="Ключевые слова для поиска целевых модулей (если auto_find=True)")
    use_dora: bool = Field(default=True, description="Использовать DoRA (Weight-Decomposed Low-Rank Adaptation)")
    use_rslora: bool = Field(default=True, description="Использовать RsLoRA (Rank-Stabilized LoRA, alpha будет равен r)")


class PredictionHeadSettings(BaseModel):
    """Настройки головы предсказания для регрессии времени отклика."""
    categorical_embedding_dim: int = Field(default=64, gt=0, description="Размерность эмбеддингов для категориальных признаков")
    hidden_layers: list[int] = Field(default=[2048, 4096, 1024, 128], min_length=1, description="Размеры скрытых слоев нейронной сети")
    dropout: float = Field(default=0.15, ge=0.0, le=1.0, description="Вероятность dropout между слоями")
    use_layernorm: bool = Field(default=True, description="Использовать Layer Normalization между слоями")

    @field_validator('hidden_layers')
    def validate_hidden_layers(cls, v: list[int]) -> list[int]:
        """Проверка, что все размеры слоев положительные."""
        if any(size <= 0 for size in v):
            raise ValueError("Все размеры скрытых слоев должны быть положительными")
        return v

class TrainingHeadSettings(BaseModel):
    """Отдельные настройки обучения каждой модели"""
    num_train_epochs: int = Field(default=5, gt=0, description="Количество эпох обучения")
    batch_size: int = Field(default=4, gt=0, description="Размер батча для обучения")
    learning_rate: float = Field(gt=0, default=1e-4, description="Начальная скорость обучения")

class CodeModelSettings(BaseModel):
    """Настройки для модели Code"""
    peft: PeftSettings = Field(default_factory=PeftSettings, description="Настройки PEFT")
    prediction_head: PredictionHeadSettings = Field(default_factory=PredictionHeadSettings,description="Настройки головы предсказания")
    training: TrainingHeadSettings = Field(default_factory=TrainingHeadSettings, description="Отдельные настройки обучения")

class ChatModelSettings(BaseModel):
    """Настройки для модели Code"""
    peft: PeftSettings = Field(default_factory=PeftSettings, description="Настройки PEFT")
    prediction_head: PredictionHeadSettings = Field(default_factory=PredictionHeadSettings,description="Настройки головы предсказания")
    training: TrainingHeadSettings = Field(default_factory=TrainingHeadSettings, description="Отдельные настройки обучения")

class ApiSettings(BaseModel):
    """Настройки API сервера для инференса."""
    host: str = Field(default="0.0.0.0", description="Хост для запуска API сервера")
    port: int = Field(default=8008, gt=0, le=65535, description="Порт для API сервера")
    workers: int = Field(default=1, ge=1, description="Количество рабочих процессов")
    swagger_url: str = Field(default="/api/docs", description="URL для Swagger документации")
    api_title: str = Field(default="LLM Response Time Predictor API", description="Название API в документации")
    api_version: str = Field(default="1.0", description="Версия API")

class GitSettings(BaseModel):
    repo_url: str | None = Field(default=None, description="SSH URL репозитория с тестовыми данными")

class Settings(BaseSettings):
    """Главный класс настроек приложения LLM Time Predictor."""

    model_config = SettingsConfigDict(
        env_file=Path(__file__).resolve().parent.parent / '.env',
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore",
        env_nested_delimiter="__"
    )

    path_project: PathProjectSettings = Field(default_factory=PathProjectSettings, description="Настройки путей проекта")
    data: DataSettings = Field(default_factory=DataSettings, description="Настройки работы с данными")
    model: ModelSettings = Field(default_factory=ModelSettings, description="Базовые настройки модели")
    training: TrainingSettings = Field(default_factory=TrainingSettings, description="Базовые настройки обучения")
    code: CodeModelSettings = Field(default_factory=CodeModelSettings, description="Настройки для модели Code")
    chat: ChatModelSettings = Field(default_factory=ChatModelSettings, description="Настройки для модели Chat")
    api: ApiSettings = Field(default_factory=ApiSettings, description="Настройки API сервера")
    git_settings: GitSettings = Field(default_factory=GitSettings, description="Настройки репозитория")

# Функция для загрузки настроек из файла
def load_settings(config_path: Path = Path(locate_folder_path('config')/'config.yaml')) -> Settings:
    """
    Загрузить настройки из YAML файла

    :param config_path: путь до файла настроек в формате yaml
    :return: класс настроек проекта
    """
    if not config_path.exists():
        raise FileNotFoundError(f"Файл конфигурации не найден: {config_path}")

    with open(config_path, "r", encoding="utf-8") as f:
        config_data = yaml.safe_load(f)

    return Settings(**config_data)

config = load_settings(Path(locate_folder_path('config')/'config.yaml'))

if __name__ == '__main__':
    settings = load_settings(Path(locate_folder_path('config')/'config.yaml'))
    print(settings.model.torch_compile)

