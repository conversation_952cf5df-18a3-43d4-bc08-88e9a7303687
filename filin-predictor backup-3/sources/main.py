import logging
import subprocess

import uvicorn

from settings import config

logger = logging.getLogger(__name__)

# Запуск приложения
if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)

    # Конфигурация сервера
    server_config = ["--host", str(config.api.host), "--port", str(config.api.port), "--workers",
                     str(config.api.workers),  "--loop", "uvloop", "--log-level", "info"]

    # uvicorn.run() - блокирующий вызов, ошибки не выбрасываются (нельзя обработать в try/except),
    # поэтому используем subprocess для запуска
    try:
        logger.info("Запускаем сервер на ONNX моделях")
        subprocess.run(["uvicorn", "api.app_onnx:app", *server_config], check=True)
    except subprocess.CalledProcessError as e:
        logger.error(f'Ошибка при запуске на ONNX моделях: {e}. Запускаем сервер на PyTorch')
        subprocess.run(["uvicorn", "api.app_torch:app", *server_config])