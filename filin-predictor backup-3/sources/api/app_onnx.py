import logging
from contextlib import asynccontextmanager

from litestar import Litestar, post
from litestar.datastructures import State
from litestar.exceptions import HTTPException
from litestar.status_codes import HTTP_400_BAD_REQUEST, HTTP_500_INTERNAL_SERVER_ERROR
from pydantic import BaseModel, Field, field_validator

from core.onnx_predictor import ONNXRuntimePredictor
from core.utils import get_device

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


# Pydantic модели для валидации
class PredictionRequest(BaseModel):
    """Модель запроса для предсказания времени выполнения"""
    text_input: str = Field(..., min_length=1, description="Входной текст (код или сообщение чата)")
    hardware: str = Field(..., description="Тип железа (например, '3xH100')")
    model_name: str = Field(..., description="Название модели")
    input_length: int | None = Field(None, ge=0, description="Длина входной последовательности")
    concurrent_requests: int | None = Field(None, ge=1, description="Количество параллельных запросов")
    model_instances: int | None = Field(None, ge=1, description="Количество экземпляров модели")

    @field_validator('text_input')
    def validate_text_input(cls, v):
        if not v.strip():
            raise ValueError("text_input не может быть пустым")
        return v

    class Config:
        schema_extra = {
            "example": {
                "text_input": "def fibonacci(n): return n if n <= 1 else fibonacci(n-1) + fibonacci(n-2)",
                "hardware": "3xH100",
                "model_name": "meta-llama/Llama-2-7b-hf",
                "input_length": 73,
                "concurrent_requests": 8,
                "model_instances": 1
            }
        }


class PredictionResponse(BaseModel):
    """Модель ответа с предсказанием"""
    predicted_time: float = Field(..., description="Предсказанное время выполнения в секундах")
    task_type: str = Field(..., description="Тип задачи (code/chat)")
    device: str = Field(..., description="Устройство, использованное для инференса")

    class Config:
        schema_extra = {
            "example": {
                "predicted_time": 1.7265,
                "task_type": "code",
                "device": "cuda"
            }
        }


class ErrorResponse(BaseModel):
    """Модель ответа при ошибке"""
    detail: str
    error_type: str



@asynccontextmanager
async def lifespan(app: Litestar):
    """Жизненный цикл приложения для инициализации и очистки ресурсов"""
    logger.info("Запуск приложения...")

    # Инициализация предикторов
    try:
        device = get_device()
        logger.info(f"Используется устройство: {device}")

        app.state.predictors = {}  # Инициализируем хранилище
        # Создаем предикторы для обоих типов задач
        for task_type in ['code', 'chat']:
            logger.info(f"Инициализация ONNX предиктора для задачи '{task_type}'...")
            predictor = ONNXRuntimePredictor(task_type=task_type)

            # Выполняем warmup
            logger.info(f"Выполнение warmup для '{task_type}'...")
            predictor._warmup(num_runs=3)

            app.state.predictors[task_type] = predictor
            logger.info(f"Предиктор '{task_type}' успешно инициализирован")

        app.state.device = str(device)

        logger.info("Все предикторы успешно загружены и готовы к работе")

    except Exception as e:
        logger.exception(f"Ошибка при инициализации предикторов: {e}")
        raise

    yield

    # Очистка ресурсов при завершении
    logger.info("Завершение работы приложения...")
    app.state.predictors.clear()


@post(
    "/predict/{task_type:str}",
    response_model=PredictionResponse,
    responses={
        400: {"model": ErrorResponse},
        500: {"model": ErrorResponse}
    },
    summary="Предсказание времени выполнения",
    description="Предсказывает время выполнения кода или генерации ответа чата"
)
async def predict_time(
        task_type: str,
        data: PredictionRequest,
        state: State
) -> PredictionResponse:
    """
    Endpoint для предсказания времени выполнения.

    Args:
        task_type: Тип задачи ('code' или 'chat')
        data: Данные запроса с параметрами для предсказания
        state: State приложения с предикторами

    Returns:
        PredictionResponse с предсказанным временем

    Raises:
        HTTPException: При неверном типе задачи или ошибке предсказания
    """
    # Валидация task_type
    if task_type not in ['code', 'chat']:
        raise HTTPException(
            status_code=HTTP_400_BAD_REQUEST,
            detail=f"Неверный тип задачи: '{task_type}'. Допустимые значения: 'code', 'chat'",
            extra={"error_type": "validation_error"}
        )

    # Получаем нужный предиктор
    predictor = state.predictors.get(task_type)
    if not predictor:
        raise HTTPException(
            status_code=HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Предиктор для задачи '{task_type}' не инициализирован",
            extra={"error_type": "initialization_error"}
        )

    try:
        # Подготавливаем параметры для predict
        predict_params = data.model_dump(exclude_none=True)

        # Выполняем предсказание
        logger.info(f"Выполнение предсказания для task_type='{task_type}'")
        predicted_time = await predictor.predict_async(**predict_params)
        # predicted_time = predictor.predict(**predict_params)

        return PredictionResponse(
            predicted_time=predicted_time,
            task_type=task_type,
            device=state.device
        )

    except ValueError as e:
        # Ошибки валидации данных
        logger.warning(f"Ошибка валидации: {e}")
        raise HTTPException(
            status_code=HTTP_400_BAD_REQUEST,
            detail=str(e),
            extra={"error_type": "validation_error"}
        )
    except Exception as e:
        # Неожиданные ошибки
        logger.exception(f"Ошибка при выполнении предсказания: {e}")
        raise HTTPException(
            status_code=HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Внутренняя ошибка сервера: {str(e)}",
            extra={"error_type": "prediction_error"}
        )

# Создание приложения
app = Litestar(
    route_handlers=[predict_time],
    lifespan=[lifespan]
)