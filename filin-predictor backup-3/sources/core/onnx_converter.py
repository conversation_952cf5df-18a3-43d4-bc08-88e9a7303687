import logging
import warnings
from typing import Literal

import joblib
import torch
import onnx
import onnxruntime as ort
import numpy as np
import torch.nn as nn
from transformers import AutoTokenizer, AutoConfig, AutoModel
from peft import PeftModel

from core.utils import find_model_directory, find_closest_power_of_2, get_device, find_target_modules
from sources.settings import config

logger = logging.getLogger(__name__)


class ModelToONNXConverter:
    """Конвертер PyTorch модели в ONNX формат"""

    def __init__(self, task_type: Literal['code', 'chat']):
        self.task_type = task_type
        self.device = torch.device('cpu')  # ONNX экспорт должен быть на CPU

        # Загружаем компоненты модели
        self.model_dir = find_model_directory(config.path_project.training_output_dir, task_type)
        if not self.model_dir:
            raise FileNotFoundError(f"Директория модели не найдена для {task_type}")

        # Загружаем маппинги и размеры словарей
        self.feature_maps, self.vocab_sizes = self._load_feature_maps_and_vocab_sizes()
        self.scalers = self._load_scalers()

        # Загружаем токенизатор
        self.tokenizer = AutoTokenizer.from_pretrained(config.model.base_embedder_name)
        model_config = AutoConfig.from_pretrained(config.model.base_embedder_name, trust_remote_code=True)
        model_max_len = getattr(model_config, 'max_position_embeddings', 512)
        self.max_seq_len = find_closest_power_of_2(model_max_len, min_val=64)

        # Создаем модель БЕЗ квантизации для ONNX
        logger.info(f"Создаем модель для ONNX конвертации: {task_type}")
        self.model = self._create_model_for_onnx()
        self.model.eval()

    def _create_model_for_onnx(self):
        """Создает модель без квантизации для экспорта в ONNX"""
        # Создаем простую обертку для модели
        model = ONNXCompatibleModel(self.task_type, self.vocab_sizes, self.model_dir)

        # Загружаем веса
        state_dict_path = self.model_dir / "model.safetensors"
        if not state_dict_path.exists():
            state_dict_path = self.model_dir / "pytorch_model.bin"

        if state_dict_path.exists():
            logger.info(f"Загружаем веса из {state_dict_path}")
            if state_dict_path.suffix == ".safetensors":
                from safetensors.torch import load_file
                state_dict = load_file(state_dict_path, device='cpu')
            else:
                state_dict = torch.load(state_dict_path, map_location='cpu')

            # Фильтруем ключи для загрузки
            filtered_state_dict = {}
            for key, value in state_dict.items():
                # Пропускаем ключи с _orig_mod
                if '_orig_mod' in key:
                    new_key = key.replace('._orig_mod', '')
                    filtered_state_dict[new_key] = value
                # Пропускаем квантизованные веса
                elif 'weight_format' not in key and 'quant_state' not in key:
                    filtered_state_dict[key] = value

            # Загружаем отфильтрованные веса
            missing, unexpected = model.load_state_dict(filtered_state_dict, strict=False)
            if missing:
                logger.warning(f"Отсутствующие ключи: {missing[:5]}...")
            if unexpected:
                logger.warning(f"Неожиданные ключи: {unexpected[:5]}...")

        return model

    def _load_feature_maps_and_vocab_sizes(self):
        """Загружаем маппинги и размеры словарей"""
        fm_path = self.model_dir / "feature_maps.pth"
        vs_path = self.model_dir / "vocab_sizes.pth"

        feature_maps = torch.load(fm_path, map_location='cpu')

        if vs_path.exists():
            vocab_sizes = torch.load(vs_path, map_location='cpu')
        else:
            vocab_sizes = {k: len(v) for k, v in feature_maps.items()}

        return feature_maps, vocab_sizes

    def _load_scalers(self):
        """Загружаем скейлеры"""
        scalers = {}
        feature_config = {
            "input_length": config.data.include_input_length,
            "concurrent_requests": config.data.include_concurrent_requests,
            "model_instances": config.data.include_model_instances,
            "target_time": config.data.target_transformation
        }

        for col, enabled in feature_config.items():
            if enabled:
                scaler_path = self.model_dir / f"{col}_scaler.joblib"
                if scaler_path.exists():
                    scalers[col] = joblib.load(scaler_path)

        return scalers

    def prepare_dummy_inputs(self) -> dict[str, torch.Tensor]:
        """Подготовка примера входных данных для трассировки"""

        # Пример текста
        dummy_text = "def example(): return 42" if self.task_type == 'code' else "Hello world"

        # Токенизация
        encoding = self.tokenizer.encode_plus(
            dummy_text,
            add_special_tokens=True,
            max_length=self.max_seq_len,
            padding='max_length',
            truncation=True,
            return_attention_mask=True,
            return_tensors='pt',
        )

        # Базовые входы на CPU
        dummy_inputs = {
            'input_ids': encoding['input_ids'],
            'attention_mask': encoding['attention_mask'],
        }

        # Категориальные признаки
        dummy_inputs['task_type'] = torch.tensor([0], dtype=torch.long)
        dummy_inputs['hardware'] = torch.tensor([0], dtype=torch.long)
        dummy_inputs['full_model_name'] = torch.tensor([0], dtype=torch.long)

        # Числовые признаки (если включены)
        if config.data.include_input_length:
            if 'input_length' in self.scalers:
                scaled_value = self.scalers['input_length'].transform([[100]])[0, 0]
                dummy_inputs['input_length'] = torch.tensor([[scaled_value]], dtype=torch.float32)
            else:
                dummy_inputs['input_length'] = torch.tensor([[100.0]], dtype=torch.float32)

        if config.data.include_concurrent_requests:
            if 'concurrent_requests' in self.scalers:
                scaled_value = self.scalers['concurrent_requests'].transform([[1]])[0, 0]
                dummy_inputs['concurrent_requests'] = torch.tensor([[scaled_value]], dtype=torch.float32)
            else:
                dummy_inputs['concurrent_requests'] = torch.tensor([[1.0]], dtype=torch.float32)

        if config.data.include_model_instances:
            if 'model_instances' in self.scalers:
                scaled_value = self.scalers['model_instances'].transform([[1]])[0, 0]
                dummy_inputs['model_instances'] = torch.tensor([[scaled_value]], dtype=torch.float32)
            else:
                dummy_inputs['model_instances'] = torch.tensor([[1.0]], dtype=torch.float32)

        return dummy_inputs

    def convert_to_onnx(self, output_path: str, opset_version: int = 14):
        """Конвертация модели в ONNX"""

        logger.info(f"Начинаем конвертацию модели {self.task_type} в ONNX...")

        # Убеждаемся что модель на CPU и в eval режиме
        self.model.eval()
        self.model.to('cpu')

        # Подготавливаем dummy inputs
        dummy_inputs = self.prepare_dummy_inputs()

        # Создаем tuple из входов в правильном порядке
        input_names = list(dummy_inputs.keys())
        input_values = tuple(dummy_inputs.values())

        # Динамические оси для батчей
        dynamic_axes = {}
        for name in input_names:
            if name in ['input_ids', 'attention_mask']:
                dynamic_axes[name] = {0: 'batch_size', 1: 'sequence_length'}
            else:
                dynamic_axes[name] = {0: 'batch_size'}
        dynamic_axes['output'] = {0: 'batch_size'}

        try:
            # Экспортируем модель
            with warnings.catch_warnings():
                warnings.filterwarnings("ignore", category=UserWarning)
                torch.onnx.export(
                    self.model,
                    input_values,
                    output_path,
                    export_params=True,
                    opset_version=opset_version,
                    do_constant_folding=True,
                    input_names=input_names,
                    output_names=['output'],
                    dynamic_axes=dynamic_axes,
                    verbose=False
                )

            logger.info(f"Модель успешно конвертирована в {output_path}")

            # Проверяем модель
            try:
                onnx.checker.check_model(output_path)
                logger.info("ONNX модель прошла валидацию")
            except Exception as e:
                logger.error(f"Ошибка при проверке ONNX модели: {e}")

        except Exception as e:
            logger.error(f"Ошибка при конвертации: {e}")
            raise

    def compare_outputs(self, onnx_path: str, tolerance: float = 1e-3) -> bool:
        """Сравнение выходов PyTorch и ONNX моделей"""

        logger.info("Сравнение выходов PyTorch и ONNX...")

        # Подготавливаем входы
        dummy_inputs = self.prepare_dummy_inputs()

        # PyTorch inference
        self.model.eval()
        with torch.no_grad():
            pytorch_output = self.model(**dummy_inputs)

        # ONNX inference
        ort_session = ort.InferenceSession(onnx_path, providers=['CPUExecutionProvider'])

        # Конвертируем входы в numpy
        ort_inputs = {}
        for name, tensor in dummy_inputs.items():
            ort_inputs[name] = tensor.numpy()

        onnx_output = ort_session.run(None, ort_inputs)[0]

        # Сравниваем
        pytorch_output_np = pytorch_output.numpy()
        diff = np.abs(pytorch_output_np - onnx_output).max()

        logger.info(f"Максимальная разница: {diff}")

        if diff < tolerance:
            logger.info("✓ Выходы совпадают!")
            return True
        else:
            logger.warning(f"✗ Выходы различаются больше чем на {tolerance}")
            return False

class ONNXCompatibleModel(nn.Module):
    """Обертка для модели"""
    def __init__(self, task_type, vocab_sizes, model_dir):
        super().__init__()
        self.task_type = task_type
        self.vocab_sizes = vocab_sizes
        # Выбор настройки
        self.perf_config = config.chat.peft if self.task_type == 'chat' else config.code.peft
        self.head_cfg = config.chat.prediction_head if self.task_type == 'chat' else config.code.prediction_head

        # Загружаем базовую модель БЕЗ квантизации
        logger.info("Загружаем базовую модель без квантизации для ONNX...")
        self.base_embedder = AutoModel.from_pretrained(
            config.model.base_embedder_name,
            torch_dtype=torch.float32,  # Используем FP32 для ONNX
            trust_remote_code=True
        )

        # Загружаем PEFT адаптер если нужно
        adapter_config_path = model_dir / "adapter_config.json"
        if self.perf_config.enabled and adapter_config_path.is_file():
            logger.info("Загружаем PEFT адаптер...")
            self.base_embedder = PeftModel.from_pretrained(
                self.base_embedder,
                str(model_dir),
                is_trainable=False
            )
            # Объединяем веса адаптера с базовой моделью
            self.base_embedder = self.base_embedder.merge_and_unload()

        # Замораживаем параметры
        for p in self.base_embedder.parameters():
            p.requires_grad = False

        # Создаем эмбеддинги
        self.task_emb = nn.Embedding(vocab_sizes.get("task_type", 1), self.head_cfg.categorical_embedding_dim)
        self.hw_emb = nn.Embedding(vocab_sizes.get("hardware", 1), self.head_cfg.categorical_embedding_dim)
        self.model_emb = nn.Embedding(vocab_sizes.get("full_model_name", 1), self.head_cfg.categorical_embedding_dim)

        # Создаем prediction head
        txt_dim = self.base_embedder.config.hidden_size
        mlp_in = txt_dim + self.head_cfg.categorical_embedding_dim * 3
        mlp_in += sum([config.data.include_input_length,
                       config.data.include_concurrent_requests,
                       config.data.include_model_instances])

        # MLP layers
        layers = []
        if self.head_cfg.use_layernorm:
            layers.append(nn.LayerNorm(mlp_in))

        in_dim = mlp_in
        for h in self.head_cfg.hidden_layers:
            layers.extend([nn.Linear(in_dim, h), nn.ReLU(), nn.Dropout(self.head_cfg.dropout)])
            in_dim = h

        layers.append(nn.Linear(in_dim, 1))
        self.prediction_head = nn.Sequential(*layers)

    def forward(self, input_ids, attention_mask, task_type, hardware, full_model_name,
                input_length=None, concurrent_requests=None, model_instances=None):
        # Извлекаем CLS токен
        cls = self.base_embedder(input_ids=input_ids, attention_mask=attention_mask).last_hidden_state[:, 0]

        # Категориальные эмбеддинги
        task_e = self.task_emb(task_type)
        hw_e = self.hw_emb(hardware)
        model_e = self.model_emb(full_model_name)

        # Собираем признаки
        all_feats = [cls, task_e, hw_e, model_e]

        # Добавляем числовые признаки
        if input_length is not None:
            all_feats.append(input_length)
        if concurrent_requests is not None:
            all_feats.append(concurrent_requests)
        if model_instances is not None:
            all_feats.append(model_instances)

        # Конкатенация
        x = torch.cat(all_feats, dim=1)

        # Предсказание
        pred = self.prediction_head(x)
        return pred.squeeze(-1)



def convert_models_to_onnx():
    """Конвертация всех моделей в ONNX"""

    output_dir = config.path_project.training_output_dir / "onnx"
    output_dir.mkdir(parents=True, exist_ok=True)

    for task_type in ['code', 'chat']:
        try:
            logger.info(f"\n{'= ' *50}")
            logger.info(f"Конвертация модели для {task_type}")
            logger.info(f"{'= ' *50}")

            # Создаем конвертер
            converter = ModelToONNXConverter(task_type)

            # Пути для сохранения
            onnx_path = output_dir / f"{task_type}_model.onnx"

            # Конвертация
            converter.convert_to_onnx(str(onnx_path))

            # Проверка
            converter.compare_outputs(str(onnx_path))

            logger.info(f"✓ Модель {task_type} успешно конвертирована!")

        except Exception as e:
            logger.error(f"Ошибка при конвертации {task_type}: {e}")
            import traceback
            traceback.print_exc()

    logger.info("\n✓ Конвертация завершена!")


if __name__ == '__main__':
    logging.basicConfig(level=logging.INFO)
    convert_models_to_onnx()