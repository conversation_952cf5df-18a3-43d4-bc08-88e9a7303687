import asyncio
import logging
import time
from typing import Literal
import warnings

import joblib
import torch
import numpy as np
from sklearn.preprocessing import StandardScaler
from transformers import AutoTokenizer, AutoConfig, PreTrainedTokenizerBase

from core.model import LLMTimePredictor
from core.utils import find_model_directory, find_closest_power_of_2, get_device
from settings import config

logger = logging.getLogger(__name__)

class Predictor:
    def __init__(self, task_type: Literal['code', 'chat']):
        self.task_type = task_type
        logger.info(f"Инициализация предиктора для задачи: '{task_type}'")

        # Определяем устройство для вычислений (GPU если доступно, иначе CPU)
        self.device = get_device()
        logger.info(f"Устройство для инференса: {self.device}")

        self.model_dir = find_model_directory(config.path_project.training_output_dir, self.task_type)
        if not self.model_dir:
            raise FileNotFoundError(f"Директория модели не найдена, параметры: {config.path_project.training_output_dir} / {self.task_type}")

        self.feature_maps, self.vocab_sizes = self.download_feature_maps_and_vocab_sizes()
        self.scalers = self.download_scalers()

        self.tokenizer, self.max_seq_len = self.download_tokenizer()

        # Загружаем обученную модель с полной реконструкцией архитектуры
        try:
            self.model = LLMTimePredictor(task_type=self.task_type, vocab_sizes=self.vocab_sizes, is_trainable=False)
        except Exception as e:
            logger.exception(f"Критическая ошибка загрузки модели: {e}")
            raise

        # Использование CUDA streams для параллелизма
        if self.device.type == 'cuda':
            self.stream = torch.cuda.Stream()
        else:
            self.stream = None

    def download_feature_maps_and_vocab_sizes(self) -> tuple[dict[str, dict[str, int]], dict[str, int]]:
        """Загружаем маппинги категориальных признаков и размеры словарей для создания embedding слоев"""
        fm_path = self.model_dir / "feature_maps.pth"
        vs_path = self.model_dir / "vocab_sizes.pth"

        try:
            # Загружаем маппинги категориальных признаков (строка -> индекс)
            if fm_path.exists():
                feature_maps: dict[str, dict[str, int]] = torch.load(fm_path, map_location='cpu')
                logger.info(f"Маппинги признаков загружены из {fm_path}")
            else:
                raise FileNotFoundError(f"Файл feature_maps.pth не найден в {self.model_dir}")

            # Загружаем размеры словарей для создания embedding слоев
            if vs_path.exists():
                vocab_sizes: dict[str, int] = torch.load(vs_path, map_location='cpu')
                logger.info(f"Размеры словарей загружены из {vs_path}")
            else:
                # Резервный вариант: восстанавливаем размеры из маппингов
                if feature_maps:
                    vocab_sizes: dict[str, int] = {k: len(v) for k, v in feature_maps.items()}
                    logger.warning(f"Файл vocab_sizes.pth не найден, размеры восстановлены.")
                else:
                    raise FileNotFoundError(f"Файл vocab_sizes.pth не найден в {self.model_dir}")
            return feature_maps, vocab_sizes
        except Exception as e:
            logger.error(f"Ошибка загрузки маппингов признаков / размеров словарей: {e}")
            raise

    def download_scalers(self) -> dict[str, StandardScaler]:
        """Загружаем скейлеры для числовых признаков на основе конфигурации"""
        scalers: dict[str, StandardScaler] = {}

        feature_config = {
            "input_length": config.data.include_input_length,
            "concurrent_requests": config.data.include_concurrent_requests,
            "model_instances": config.data.include_model_instances,
            "target_time": config.data.target_transformation
        }
        # Фильтруем только включенные признаки
        numerical_cols = [col for col, enabled in feature_config.items() if enabled]
        if not numerical_cols:
            logger.warning("Нет числовых признаков для загрузки скейлеров")
            return {}

        # Загружаем каждый необходимый скейлер
        for col in numerical_cols:
            scaler_path = self.model_dir / f"{col}_scaler.joblib"
            if not scaler_path.exists():
                raise FileNotFoundError(f"Обязательный скейлер для '{col}' не найден: {scaler_path}")
            try:
                scalers[col] = joblib.load(scaler_path)
                logger.info(f"Скейлер для '{col}' загружен")
            except Exception as e:
                logger.error(f"Не удалось загрузить скейлер '{col}': {e}")
                raise
        logger.info(f"Загружено {len(scalers)} скейлеров: {list(scalers.keys())}")
        return scalers

    def download_tokenizer(self) -> tuple[PreTrainedTokenizerBase, int]:
        """Загружаем токенизатор и определяем максимальную длину последовательности"""
        try:
            tokenizer = AutoTokenizer.from_pretrained(config.model.base_embedder_name)
            model_config_tok = AutoConfig.from_pretrained(config.model.base_embedder_name, trust_remote_code=True)
            model_max_len = getattr(model_config_tok, 'max_position_embeddings', 512)
            max_seq_len = find_closest_power_of_2(model_max_len, min_val=64)
            if max_seq_len != model_max_len:
                logger.warning(f"Инференс: Исходная max_len ({model_max_len}) изменена на {max_seq_len}.")
            else:
                logger.info(f"Инференс: Используется max_seq_len = {max_seq_len}.")
            return tokenizer, max_seq_len
        except Exception as e:
            logger.error(f"Ошибка загрузки токенизатора/конфигурации '{config.model.base_embedder_name}': {e}")
            raise

    def scale_feature(self, name: str, value: int | float | None) -> torch.Tensor | None:
        """Масштабирование числового признака с использованием загруженного StandardScaler"""
        if value is None:
            logger.warning(f"Значение для обязательного признака '{name}' не предоставлено.")
            return None

        # Получаем скейлер для данного признака
        if not (scaler := self.scalers.get(name)):
            raise ValueError(f"Скейлер для '{name}' не был загружен.")

        try:
            value_array = np.array([[float(value)]])

            # Применяем трансформацию с подавлением предупреждений
            with warnings.catch_warnings():
                warnings.filterwarnings("ignore", category=UserWarning, message="X does not have valid feature names")
                scaled_value = scaler.transform(value_array)[0, 0]

            # Создаём тензор напрямую на нужном устройстве
            return torch.tensor([scaled_value], dtype=torch.float, device=self.device)

        except (ValueError, TypeError) as e:
            logger.warning(f"Ошибка масштабирования '{name}'={value}: {e}")
            return None

    @torch.no_grad()
    def predict(self,
                text_input: str,
                hardware: str,
                model_name: str,
                input_length: int | None = None,
                concurrent_requests: int | None = None,
                model_instances: int | None = None
                ) -> float:
        """Выполнение полного пайплайна предсказания с пред- и постобработкой"""
        t_start = time.perf_counter()

        # --- Этап 1: Токенизация ---
        t0 = time.perf_counter()
        # Токенизируем входной текст с согласованными параметрами padding и truncation
        encoding = self.tokenizer.encode_plus(
            text_input,
            add_special_tokens=True,  # Добавляем специальные токены (CLS, SEP)
            max_length=self.max_seq_len,  # Ограничиваем длину
            padding='max_length',  # Дополняем до максимальной длины
            truncation=True,  # Обрезаем если превышает max_length
            return_attention_mask=True,  # Возвращаем маску внимания
            return_tensors='pt',  # Возвращаем PyTorch тензоры
        )

        # Перемещаем тензоры на устройство для вычислений
        input_ids = encoding['input_ids'].to(self.device)
        attention_mask = encoding['attention_mask'].to(self.device)
        t1 = time.perf_counter()

        # --- Этап 2: Кодирование признаков ---
        # Используем предоставленную длину или вычисляем из текста для согласованности признаков
        actual_input_length = len(text_input) if input_length is None else input_length
        unk_index = self.feature_maps.get("UNK", 0)  # Индекс для неизвестных значений

        # Получаем индексы для каждого категориального признака
        task_type_idx = self.feature_maps['task_type'].get(self.task_type, unk_index)
        hardware_idx = self.feature_maps['hardware'].get(hardware, unk_index)
        model_name_idx = self.feature_maps['full_model_name'].get(model_name, unk_index)

        # Логируем предупреждения для неизвестных категориальных значений для отладки
        if hardware_idx == unk_index and hardware != "UNK":
            logger.warning(f"Неизвестное значение 'hardware': {hardware}.")
        if model_name_idx == unk_index and model_name != "UNK":
            logger.warning(f"Неизвестное значение 'model_name': {model_name}.")

        # Преобразуем категориальные индексы в тензоры
        task_type_tensor = torch.tensor([task_type_idx], dtype=torch.long, device=self.device)
        hardware_tensor = torch.tensor([hardware_idx], dtype=torch.long, device=self.device)
        model_name_tensor = torch.tensor([model_name_idx], dtype=torch.long, device=self.device)

        # Подготавливаем базовый вход модели с обязательными признаками
        model_input: dict[str, torch.Tensor] = {
            'input_ids': input_ids,
            'attention_mask': attention_mask,
            'task_type': task_type_tensor,
            'hardware': hardware_tensor,
            'full_model_name': model_name_tensor,
        }

        features_map = {
            'input_length': (actual_input_length, config.data.include_input_length),
            'concurrent_requests': (concurrent_requests, config.data.include_concurrent_requests),
            'model_instances': (model_instances, config.data.include_model_instances)
        }

        # Обрабатываем только включенные признаки
        for feature_name, (value, is_enabled) in features_map.items():
            if not is_enabled:
                continue

            scaled_value = self.scale_feature(feature_name, value)
            if scaled_value is None:
                raise ValueError(f"Не удалось масштабировать обязательный признак '{feature_name}'.")
            model_input[feature_name] = scaled_value
        t2 = time.perf_counter()

        # --- Этап 3: Инференс ---
        prediction_scaled = self.model(**model_input)
        t3 = time.perf_counter()

        # --- Этап 4: Обратное преобразование ---
        predicted_value_scaled = prediction_scaled.item()
        predicted_time_original = predicted_value_scaled
        if config.data.target_transformation:
            target_scaler = self.scalers.get('target_time')
            if target_scaler:
                try:
                    # Выполняем обратное масштабирование и обратное логарифмическое преобразование
                    predicted_value_reshaped = np.array([[predicted_value_scaled]])
                    # Обратное масштабирование (StandardScaler)
                    predicted_log_time = target_scaler.inverse_transform(predicted_value_reshaped).flatten()[0]
                    # Обратное логарифмическое преобразование (обратное к log1p)
                    predicted_time_original = np.expm1(predicted_log_time)
                except Exception as e:
                    logger.error(f"Ошибка обратного преобразования: {e}.")
            else:
                logger.error("Трансформация включена, но скейлер целевой переменной не найден!")
        # Обеспечиваем неотрицательность предсказания (время не может быть отрицательным)
        predicted_time_final = max(0.0, predicted_time_original)
        t4 = time.perf_counter()

        # Логируем время выполнения и результат
        logger.info(
            f"[Тайминг] токенизация: {t1 - t0:.4f}s | "
            f"преобразование: {t2 - t1:.4f}s | "
            f"инференс: {t3 - t2:.4f}s | "
            f"обратная трансформация: {t4 - t3:.4f}s | "
            f"ВСЁГО: {t4 - t_start:.4f}s"
        )

        return predicted_time_final

    def _warmup(self, num_runs: int = 3):
        """
        Прогрев модели для оптимизации производительности.

        Args:
            num_runs: Количество прогревочных запусков
        """
        logger.info(f"Начинаю warmup модели ({num_runs} итераций)...")

        # Подготавливаем тестовые данные для warmup
        warmup_texts = [
            "def hello(): pass" if self.task_type == 'code' else "Hello",
            "for i in range(10): print(i)" if self.task_type == 'code' else "How are you?",
            "class Test: pass" if self.task_type == 'code' else "What's the weather?"
        ]

        # Берем первое валидное значение из маппингов
        hardware_example = next(iter(self.feature_maps['hardware'].keys()))
        model_name_example = next(iter(self.feature_maps['full_model_name'].keys()))

        warmup_times = []

        for i in range(num_runs):
            text = warmup_texts[i % len(warmup_texts)]
            start_time = time.perf_counter()

            try:
                _ = self.predict(
                    text_input=text,
                    hardware=hardware_example,
                    model_name=model_name_example,
                    input_length=len(text) if config.data.include_input_length else None,
                    concurrent_requests=1 if config.data.include_concurrent_requests else None,
                    model_instances=1 if config.data.include_model_instances else None
                )

                warmup_time = time.perf_counter() - start_time
                warmup_times.append(warmup_time)
                logger.info(f"Warmup итерация {i + 1}/{num_runs}: {warmup_time:.3f}s")

            except Exception as e:
                logger.warning(f"Ошибка во время warmup итерации {i + 1}: {e}")

        if warmup_times:
            avg_time = sum(warmup_times) / len(warmup_times)
            logger.info(f"Warmup завершен. Среднее время: {avg_time:.3f}s")

            # Если первый запуск значительно медленнее остальных, это нормально
            if len(warmup_times) > 1 and warmup_times[0] > 2 * avg_time:
                logger.info("Первый запуск был медленнее из-за инициализации CUDA/компиляции")

    def _predict_with_stream(self, **kwargs) -> float:
        """Запуск инференса на cuda.stream"""
        with torch.cuda.stream(self.stream):
            result = self.predict(**kwargs)
        return result

    async def predict_async(self, **kwargs) -> float:
        """Запуск инференса в асинхронном режиме"""
        if self.stream:
            return await asyncio.to_thread(self._predict_with_stream, **kwargs)
        else:
            return await asyncio.to_thread(self.predict, **kwargs)


if __name__ == '__main__':

    def sync_test():
        logging.basicConfig(level=logging.INFO)
        for task in ['code', 'chat']:
            try:
                print(f"\n--- Тестирование предиктора для задачи: {task} ---")
                # Создаем предиктор
                predictor = Predictor(task_type=task)
                print(f"Предиктор для '{task}' загружен из {predictor.model_dir}.")
                print('Начинаю прогрев модели')
                predictor._warmup()
                print('Закончил прогрев модели')

                # Используем название модели из конфигурации или находим валидный пример из маппингов
                model_name_example = config.model.base_embedder_name

                # Подготавливаем тестовый вход в зависимости от типа задачи
                test_input = {
                    "text_input": "def fib(n):" if task == 'code' else "Расскажи мне шутку.",
                    "hardware": "3xH100",
                    "model_name": model_name_example,
                    "input_length": 15 if config.data.include_input_length else None,
                    "concurrent_requests": 8 if config.data.include_concurrent_requests else None,
                    "model_instances": 1 if config.data.include_model_instances else None,
                }

                # Убираем None значения из входных данных
                test_input_clean = {k: v for k, v in test_input.items() if v is not None}

                # Выводим тестовый запрос
                print(f"\nТестовый запрос ({task}):")
                [print(f"  {k}: {v}") for k, v in test_input_clean.items()]

                # Выполняем предсказание
                predicted_time = predictor.predict(**test_input_clean)
                print(f"\nПредсказанное время ({task}): {predicted_time:.4f} секунд")
            except Exception as e:
                print(f"Неожиданная ошибка ({task}): {e}")
                import traceback
                traceback.print_exc()

    async def async_test():
        for task in ['code', 'chat']:
            try:
                print(f"\n--- Тестирование предиктора для задачи: {task} ---")
                # Создаем предиктор
                predictor = Predictor(task_type=task)
                print(f"Предиктор для '{task}' загружен из {predictor.model_dir}.")
                print('Начинаю прогрев модели')
                predictor._warmup()
                print('Закончил прогрев модели')

                # Используем название модели из конфигурации или находим валидный пример из маппингов
                model_name_example = config.model.base_embedder_name

                # Подготавливаем тестовый вход в зависимости от типа задачи
                test_input = {
                    "text_input": "def fib(n):" if task == 'code' else "Расскажи мне шутку.",
                    "hardware": "3xH100",
                    "model_name": model_name_example,
                    "input_length": 15 if config.data.include_input_length else None,
                    "concurrent_requests": 8 if config.data.include_concurrent_requests else None,
                    "model_instances": 1 if config.data.include_model_instances else None,
                }

                # Убираем None значения из входных данных
                test_input_clean = {k: v for k, v in test_input.items() if v is not None}

                # Выводим тестовый запрос
                print(f"\nТестовый запрос ({task}):")
                [print(f"  {k}: {v}") for k, v in test_input_clean.items()]

                # Выполняем предсказание
                predicted_time = await predictor.predict_async(**test_input_clean)
                print(f"\nПредсказанное время ({task}): {predicted_time:.4f} секунд")
            except Exception as e:
                print(f"Неожиданная ошибка ({task}): {e}")
                import traceback
                traceback.print_exc()

    asyncio.run(async_test())