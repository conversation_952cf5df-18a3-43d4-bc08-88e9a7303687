import logging
from pathlib import Path

import torch
from typing import Literal

logger = logging.getLogger(__name__)

def get_device() -> torch.device:
    """
    Определение оптимального доступного устройства для PyTorch операций.

    Логика выбора устройства:
    1. Приоритет CUDA GPU если доступна - максимальная производительность
    2. Fallback на CPU с информативным логированием
    3. Консистентное использование во всем приложении

    Применение:
    - Размещение моделей на оптимальном устройстве
    - Перенос батчей данных на устройство обучения
    - Конфигурирование операций во время инференса
    - Оптимизация memory management

    Возвращает:
    torch.device: Объект устройства для размещения тензоров и моделей
    """
    if torch.cuda.is_available():
        # Использование CUDA для максимальной производительности
        return torch.device("cuda")
    else:
        # Информирование о fallback на CPU
        logger.info("CUDA недоступна, используется CPU.")
        return torch.device("cpu")


def find_target_modules(model: torch.nn.Module, keywords: list[str] | None = None) -> list[str]:
    """
    Автоматическое обнаружение целевых модулей для LoRA fine-tuning.

     Назначение и важность:
    - Поиск linear слоев с именами, содержащими ключевые слова attention/MLP
    - Критически важно для настройки PEFT (Parameter Efficient Fine-Tuning)
    - Автоматизация когда target_modules не указаны явно в конфигурации
    - Поддержка различных архитектур трансформеров

    Поддерживаемые типы слоев:
    - Стандартные PyTorch Linear слои
    - Квантованные BitsAndBytes слои (Linear4bit, Linear8bitLt)
    - Различные именования в популярных архитектурах

    Стратегия поиска:
    1. Определение ключевых слов для поиска (по умолчанию или пользовательские)
    2. Итерация по всем модулям модели с проверкой типа
    3. Сопоставление имен модулей с ключевыми словами
    4. Исключение PEFT внутренних модулей во избежание конфликтов
    5. Специальная обработка 'dense' слоев с добавлением контекста

    Архитектурные паттерны:
    - Attention механизмы: query, key, value, attention
    - MLP компоненты: dense, proj, linear, fc1, fc2
    - Projection слои: out_proj, in_proj

    Логика обработки:
    1. Инициализация ключевых слов и типов linear слоев
    2. Расширение поиска на квантованные слои (если доступны)
    3. Сканирование всех именованных модулей модели
    4. Фильтрация по типу слоя и ключевым словам
    5. Исключение внутренних модулей PEFT
    6. Специальная обработка для disambiguation

    Args:
        model: PyTorch модель для анализа
        keywords: Список ключевых слов для поиска (None = использовать стандартные)

    Returns:
        Список уникальных имен модулей для применения LoRA
    """
    # Константы вынесены для удобства изменения
    DEFAULT_KEYWORDS = frozenset([
        "query", "key", "value",  # Attention компоненты
        "dense", "proj", "linear",  # Общие linear слои
        "attention", "mlp",  # Высокоуровневые компоненты
        "fc1", "fc2",  # Feed-forward слои
        "out_proj", "in_proj"  # Projection слои
    ])

    PEFT_INTERNAL_MODULES = frozenset(['lora_A', 'lora_B', 'base_layer'])
    DENSE_PARENT_KEYWORDS = frozenset(['attention', 'mlp', 'intermediate', 'output'])

    keywords_set = frozenset(keywords) if keywords else DEFAULT_KEYWORDS
    logger.info(f"Поиск target_modules с ключевыми словами: {list(keywords_set)}")

    # Определение linear классов
    linear_classes = [torch.nn.Linear]

    try:
        import bitsandbytes as bnb
        if hasattr(bnb.nn, 'Linear4bit'):
            linear_classes.append(bnb.nn.Linear4bit)
        if hasattr(bnb.nn, 'Linear8bitLt'):
            linear_classes.append(bnb.nn.Linear8bitLt)
        logger.debug("Квантованные слои bitsandbytes добавлены в поиск")
    except ImportError:
        logger.debug("bitsandbytes не установлен, используются только стандартные Linear слои")

    linear_classes = tuple(linear_classes)
    target_modules = set()

    # Итерация по всем именованным модулям модели
    for name, module in model.named_modules():
        # Быстрая проверка типа
        if not isinstance(module, linear_classes):
            continue

        # Парсинг имени один раз
        name_lower = name.lower()
        module_name_parts = name.split('.')
        potential_target = module_name_parts[-1]
        potential_target_lower = potential_target.lower()

        # Пропуск внутренних PEFT модулей
        if potential_target in PEFT_INTERNAL_MODULES:
            continue

        # Проверка ключевых слов (оптимизировано для скорости)
        if not any(kw in potential_target_lower for kw in keywords_set):
            # Fallback на полный путь только если не найдено в имени модуля
            if not any(kw in name_lower for kw in keywords_set):
                continue

        # Добавление найденного модуля
        target_modules.add(potential_target)
        logger.debug(f"Найден target_module: '{potential_target}' (путь: '{name}')")

        # Специальная обработка dense слоев
        if potential_target == 'dense' and len(module_name_parts) > 1:
            parent_name = module_name_parts[-2].lower()
            if any(kw in parent_name for kw in DENSE_PARENT_KEYWORDS):
                qualified_name = f"{module_name_parts[-2]}.{potential_target}"
                target_modules.add(qualified_name)
                logger.debug(f"Добавлено уточненное имя: '{qualified_name}'")

    # Возвращаем отсортированный список для консистентности
    return sorted(target_modules)

def find_model_directory(base_dir: Path, task_type: Literal["chat", "code"]) -> Path | None:
    """Находит директорию с моделью, проверяя сначала best_model, затем last_checkpoint."""
    output_dir = base_dir / task_type
    for model_dir_name in ("best_model", "last_checkpoint"):
        model_dir = output_dir / model_dir_name
        if model_dir.exists() and model_dir.is_dir():
            return model_dir
    return None

def find_closest_power_of_2(n: int, min_val: int = 64) -> int:
    """
    Поиск ближайшей степени двойки, которая меньше или равна n.

    Назначение оптимизации:
    - Степени двойки обеспечивают оптимальную производительность на GPU архитектурах
    - Улучшают выравнивание памяти и эффективность операций CUDA
    - Оптимизируют batch sizes и распределение памяти в нейронных сетях

    Логика вычисления:
    1. Проверка на некорректные входные значения
    2. Использование битовых операций для эффективного поиска степени двойки
    3. Применение минимального значения для предотвращения слишком малых размеров

    Применение:
    - Оптимизация размеров батчей для обучения
    - Настройка максимальной длины последовательностей
    - Выбор размеров эмбеддингов и скрытых слоев
    - Конфигурация буферов памяти GPU
    """
    # Валидация входного значения
    if n <= 0:
        return min_val

    # Эффективное вычисление следующей степени двойки через битовые операции
    # bit_length() возвращает количество битов, необходимых для представления числа
    power = 1 << (n - 1).bit_length()  # Левый сдвиг эквивалентен умножению на 2

    # Если полученная степень больше исходного числа, берем предыдущую степень
    if power > n:
        power //= 2

    # Применение минимального ограничения для предотвращения слишком малых значений
    return max(min_val, power)
