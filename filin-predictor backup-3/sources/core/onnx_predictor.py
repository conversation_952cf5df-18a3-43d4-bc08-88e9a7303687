import asyncio
import logging
import time
import warnings
from typing import Literal, Optional

import joblib
import torch
import onnxruntime as ort
import numpy as np
from transformers import AutoTokenizer, AutoConfig

from core.utils import find_model_directory, find_closest_power_of_2
from sources.settings import config

logger = logging.getLogger(__name__)


class ONNXRuntimePredictor:
    """Оптимизированный предиктор на ONNX Runtime"""

    def __init__(self, task_type: Literal['code', 'chat'], device: str = 'cuda'):

        self.task_type = task_type
        self.onnx_path = config.path_project.training_output_dir / f"onnx/{task_type}_model.onnx"

        # Загружаем компоненты
        self.model_dir = find_model_directory(config.path_project.training_output_dir, task_type)
        if not self.model_dir:
            raise FileNotFoundError(f"Директория модели не найдена для {task_type}")

        # Загружаем маппинги и скейлеры
        self.feature_maps, self.vocab_sizes = self._load_feature_maps_and_vocab_sizes()
        self.scalers = self._load_scalers()

        # Загружаем токенизатор
        self.tokenizer = AutoTokenizer.from_pretrained(config.model.base_embedder_name)
        model_config = AutoConfig.from_pretrained(config.model.base_embedder_name, trust_remote_code=True)
        model_max_len = getattr(model_config, 'max_position_embeddings', 512)
        self.max_seq_len = find_closest_power_of_2(model_max_len, min_val=64)

        # Определяем провайдеры для ONNX Runtime
        providers = self._get_providers(device)

        # Создаем сессию ONNX Runtime
        self.session = ort.InferenceSession(self.onnx_path, providers=providers)

        # Получаем информацию о входах
        self.input_names = [inp.name for inp in self.session.get_inputs()]
        self.output_names = [out.name for out in self.session.get_outputs()]

        logger.info(f"ONNX Runtime инициализирован с провайдерами: {providers}")
        logger.info(f"Входы модели: {self.input_names}")

    def _get_providers(self, device: str) -> list[str]:
        """Определение провайдеров для ONNX Runtime"""
        try:
            available_providers = ort.get_available_providers()
            logger.info(f"Доступные ONNX Runtime провайдеры: {available_providers}")
        except AttributeError:
            logger.warning("onnxruntime не поддерживает get_available_providers(). Используем CPU по умолчанию.")
            return ['CPUExecutionProvider']

        if device.startswith("cuda"):
            if 'CUDAExecutionProvider' in available_providers:
                return ['CUDAExecutionProvider', 'CPUExecutionProvider']
            else:
                return ['CPUExecutionProvider']

        logger.warning(f"GPU провайдеры недоступны или не подходят для устройства '{device}', используем CPU.")
        return ['CPUExecutionProvider']

    def _load_feature_maps_and_vocab_sizes(self):
        """Загружаем маппинги и размеры словарей"""
        fm_path = self.model_dir / "feature_maps.pth"
        vs_path = self.model_dir / "vocab_sizes.pth"

        feature_maps = torch.load(fm_path, map_location='cpu')

        if vs_path.exists():
            vocab_sizes = torch.load(vs_path, map_location='cpu')
        else:
            vocab_sizes = {k: len(v) for k, v in feature_maps.items()}

        return feature_maps, vocab_sizes

    def _load_scalers(self):
        """Загружаем скейлеры"""
        scalers = {}
        feature_config = {
            "input_length": config.data.include_input_length,
            "concurrent_requests": config.data.include_concurrent_requests,
            "model_instances": config.data.include_model_instances,
            "target_time": config.data.target_transformation
        }

        for col, enabled in feature_config.items():
            if enabled:
                scaler_path = self.model_dir / f"{col}_scaler.joblib"
                if scaler_path.exists():
                    scalers[col] = joblib.load(scaler_path)
                    logger.info(f"Скейлер для '{col}' загружен")

        return scalers

    def scale_feature(self, name: str, value: float | None) -> Optional[np.ndarray]:
        """Масштабирование числового признака"""
        if value is None:
            return None

        scaler = self.scalers.get(name)
        if not scaler:
            logger.warning(f"Скейлер для '{name}' не найден")
            return np.array([[value]], dtype=np.float32)

        try:
            value_array = np.array([[float(value)]])
            with warnings.catch_warnings():
                warnings.filterwarnings("ignore", category=UserWarning)
                scaled_value = scaler.transform(value_array)
            return scaled_value.astype(np.float32)
        except Exception as e:
            logger.error(f"Ошибка масштабирования '{name}': {e}")
            return None

    def predict(self,
                text_input: str,
                hardware: str,
                model_name: str,
                input_length: int | None = None,
                concurrent_requests: int | None = None,
                model_instances: int | None = None) -> float:
        """Предсказание с использованием ONNX Runtime"""

        t_start = time.perf_counter()

        # --- Этап 1: Токенизация ---
        t0 = time.perf_counter()
        encoding = self.tokenizer.encode_plus(
            text_input,
            add_special_tokens=True,
            max_length=self.max_seq_len,
            padding='max_length',
            truncation=True,
            return_attention_mask=True,
            return_tensors='np',  # Важно: возвращаем numpy arrays для ONNX
        )
        t1 = time.perf_counter()

        # --- Этап 2: Подготовка признаков ---
        ort_inputs = self._prepare_onnx_inputs(
            encoding, text_input, hardware, model_name,
            input_length, concurrent_requests, model_instances
        )
        t2 = time.perf_counter()

        # --- Этап 3: Инференс ONNX ---
        outputs = self.session.run(None, ort_inputs)
        prediction_scaled = outputs[0]
        t3 = time.perf_counter()

        # --- Этап 4: Постобработка ---
        predicted_time_final = self._postprocess(prediction_scaled[0])
        t4 = time.perf_counter()

        logger.info(
            f"[ONNX Timing] токенизация: {t1 -t0:.4f}s | "
            f"подготовка: {t2 -t1:.4f}s | "
            f"инференс: {t3 -t2:.4f}s | "
            f"постобработка: {t4 -t3:.4f}s | "
            f"ВСЕГО: {t4 -t_start:.4f}s"
        )

        return predicted_time_final

    def _prepare_onnx_inputs(self, encoding, text_input, hardware, model_name,
                             input_length, concurrent_requests, model_instances) -> dict[str, np.ndarray]:
        """Подготовка входов для ONNX Runtime"""

        ort_inputs = {
            'input_ids': encoding['input_ids'],
            'attention_mask': encoding['attention_mask'],
        }

        # Категориальные признаки
        unk_index = self.feature_maps.get("UNK", 0)

        task_type_idx = self.feature_maps['task_type'].get(self.task_type, unk_index)
        hardware_idx = self.feature_maps['hardware'].get(hardware, unk_index)
        model_name_idx = self.feature_maps['full_model_name'].get(model_name, unk_index)

        # Логируем предупреждения для неизвестных значений
        if hardware_idx == unk_index and hardware != "UNK":
            logger.warning(f"Неизвестное значение 'hardware': {hardware}")
        if model_name_idx == unk_index and model_name != "UNK":
            logger.warning(f"Неизвестное значение 'model_name': {model_name}")

        ort_inputs['task_type'] = np.array([task_type_idx], dtype=np.int64)
        ort_inputs['hardware'] = np.array([hardware_idx], dtype=np.int64)
        ort_inputs['full_model_name'] = np.array([model_name_idx], dtype=np.int64)

        # Числовые признаки
        actual_input_length = len(text_input) if input_length is None else input_length

        if 'input_length' in self.input_names and config.data.include_input_length:
            scaled_value = self.scale_feature('input_length', actual_input_length)
            if scaled_value is not None:
                ort_inputs['input_length'] = scaled_value

        if 'concurrent_requests' in self.input_names and config.data.include_concurrent_requests:
            if concurrent_requests is not None:
                scaled_value = self.scale_feature('concurrent_requests', concurrent_requests)
                if scaled_value is not None:
                    ort_inputs['concurrent_requests'] = scaled_value

        if 'model_instances' in self.input_names and config.data.include_model_instances:
            if model_instances is not None:
                scaled_value = self.scale_feature('model_instances', model_instances)
                if scaled_value is not None:
                    ort_inputs['model_instances'] = scaled_value

        return ort_inputs

    def _postprocess(self, predicted_value: float) -> float:
        """Постобработка предсказания"""
        predicted_time_original = predicted_value

        if config.data.target_transformation:
            target_scaler = self.scalers.get('target_time')
            if target_scaler:
                try:
                    predicted_value_reshaped = np.array([[predicted_value]])
                    predicted_log_time = target_scaler.inverse_transform(predicted_value_reshaped).flatten()[0]
                    predicted_time_original = np.expm1(predicted_log_time)
                except Exception as e:
                    logger.error(f"Ошибка обратного преобразования: {e}")
            else:
                logger.error("Трансформация включена, но скейлер целевой переменной не найден!")

        return max(0.0, predicted_time_original)

    def _warmup(self, num_runs: int = 3):
        """
        Прогрев модели для оптимизации производительности.

        Args:
            num_runs: Количество прогревочных запусков
        """
        logger.info(f"Начинаю warmup модели ({num_runs} итераций)...")

        # Подготавливаем тестовые данные для warmup
        warmup_texts = [
            "def hello(): pass" if self.task_type == 'code' else "Hello",
            "for i in range(10): print(i)" if self.task_type == 'code' else "How are you?",
            "class Test: pass" if self.task_type == 'code' else "What's the weather?"
        ]

        # Берем первое валидное значение из маппингов
        hardware_example = next(iter(self.feature_maps['hardware'].keys()))
        model_name_example = next(iter(self.feature_maps['full_model_name'].keys()))

        warmup_times = []

        for i in range(num_runs):
            text = warmup_texts[i % len(warmup_texts)]
            start_time = time.perf_counter()

            try:
                _ = self.predict(
                    text_input=text,
                    hardware=hardware_example,
                    model_name=model_name_example,
                    input_length=len(text) if config.data.include_input_length else None,
                    concurrent_requests=1 if config.data.include_concurrent_requests else None,
                    model_instances=1 if config.data.include_model_instances else None
                )

                warmup_time = time.perf_counter() - start_time
                warmup_times.append(warmup_time)
                logger.info(f"Warmup итерация {i + 1}/{num_runs}: {warmup_time:.3f}s")

            except Exception as e:
                logger.warning(f"Ошибка во время warmup итерации {i + 1}: {e}")

        if warmup_times:
            avg_time = sum(warmup_times) / len(warmup_times)
            logger.info(f"Warmup завершен. Среднее время: {avg_time:.3f}s")

            # Если первый запуск значительно медленнее остальных, это нормально
            if len(warmup_times) > 1 and warmup_times[0] > 2 * avg_time:
                logger.info("Первый запуск был медленнее из-за инициализации CUDA/компиляции")

    async def predict_async(self, **kwargs) -> float:
        """Запуск инференса в асинхронном режиме"""
        return await asyncio.to_thread(self.predict, **kwargs)

if __name__ == '__main__':
    def sync_test():
        logging.basicConfig(level=logging.INFO)
        for task in ['code', 'chat']:
            try:
                print(f"\n--- Тестирование предиктора для задачи: {task} ---")
                # Создаем предиктор
                predictor = ONNXRuntimePredictor(task_type=task)
                print(f"Предиктор для '{task}' загружен из {predictor.onnx_path}.")
                print('Начинаю прогрев модели')
                predictor._warmup()
                print('Закончил прогрев модели')

                # Используем название модели из конфигурации или находим валидный пример из маппингов
                model_name_example = config.model.base_embedder_name

                # Подготавливаем тестовый вход в зависимости от типа задачи
                test_input = {
                    "text_input": "def fib(n):" if task == 'code' else "Расскажи мне шутку.",
                    "hardware": "3xH100",
                    "model_name": model_name_example,
                    "input_length": 15 if config.data.include_input_length else None,
                    "concurrent_requests": 8 if config.data.include_concurrent_requests else None,
                    "model_instances": 1 if config.data.include_model_instances else None,
                }

                # Убираем None значения из входных данных
                test_input_clean = {k: v for k, v in test_input.items() if v is not None}

                # Выводим тестовый запрос
                print(f"\nТестовый запрос ({task}):")
                [print(f"  {k}: {v}") for k, v in test_input_clean.items()]

                # Выполняем предсказание
                predicted_time = predictor.predict(**test_input_clean)
                print(f"\nПредсказанное время ({task}): {predicted_time:.4f} секунд")
            except Exception as e:
                print(f"Неожиданная ошибка ({task}): {e}")
                import traceback
                traceback.print_exc()

    sync_test()