import logging
from typing import Literal

import torch
import torch.nn as nn
from transformers import AutoConfig, AutoModel, BitsAndBytesConfig
from peft import PeftModel, LoraConfig, TaskType, get_peft_model, prepare_model_for_kbit_training
from safetensors.torch import load_file

from core.utils import get_device, find_target_modules, find_model_directory
from sources.settings import config

logger = logging.getLogger(__name__)


class LLMTimePredictor(nn.Module):
    def __init__(self, task_type: Literal['code', 'chat'], vocab_sizes: dict[str, int], is_trainable: bool = False):
        super().__init__()
        self.task_type = task_type
        self.vocab_sizes = vocab_sizes
        self.is_trainable = is_trainable

        # Выбор настройки
        self.perf_config = config.chat.peft if self.task_type == 'chat' else config.code.peft
        self.head_cfg = config.chat.prediction_head if self.task_type == 'chat' else config.code.prediction_head

        # Определим оптимальное доступное устройство для PyTorch
        self.device = get_device()

        # Для инференса загружаем базовую модель без PEFT
        if not self.is_trainable:
            self.base_embedder = self.configure_base_embedder(apply_peft=False)
        else:
            self.base_embedder = self.configure_base_embedder(apply_peft=True)

        # Создаем слои эмбеддингов для категориальных признаков
        self.task_emb = nn.Embedding(vocab_sizes.get("task_type", 1), self.head_cfg.categorical_embedding_dim, padding_idx=0)
        self.hw_emb = nn.Embedding(vocab_sizes.get("hardware", 1), self.head_cfg.categorical_embedding_dim, padding_idx=0)
        self.model_emb = nn.Embedding(vocab_sizes.get("full_model_name", 1), self.head_cfg.categorical_embedding_dim, padding_idx=0)

        self.prediction_head = self.assembling_the_layers(self.base_embedder)

        # Переносим модель на нужное устройство после загрузки весов
        self.to(self.device)

        # Настройка режима модели и размещения на устройстве в зависимости от предназначения
        if not self.is_trainable:
            # Проведем загрузку обученной модели
            self.load_trained_model()
            self.eval()
            logger.info("Модель переведена в режим оценки (eval).")
        else:
            self.train()
            logger.info("Модель оставлена в режиме обучения (train).")


    def configure_base_embedder(self, apply_peft: bool = True):
        """Настройка базовой модели"""
        # Настроим конфигурацию BitsAndBytes
        bnb_cfg = self.configure_bnb_quantization()
        # Определим механизм влияния
        attn_impl = self.get_attention_implementation()

        # Загружаем предобученную базовую модель с настройками квантизации и внимания
        base_embedder = AutoModel.from_pretrained(
            config.model.base_embedder_name,
            quantization_config=bnb_cfg,
            torch_dtype=config.model.quantization.bnb_4bit_compute_dtype,
            device_map=self.device if bnb_cfg else None,
            attn_implementation=attn_impl,
            trust_remote_code=True)

        if not bnb_cfg:
            base_embedder.to(self.device)

        # Применяем PEFT только если указано
        if self.perf_config.enabled and apply_peft:
            # Настроим параметры LoRA
            lora = self.configure_lora_config(base_embedder)
            if lora:
                # Подготавливаем модель для k-bit обучения и применяем PEFT
                base_embedder = prepare_model_for_kbit_training(base_embedder, use_gradient_checkpointing=False)
                base_embedder = get_peft_model(base_embedder, lora)
        else:
            for p in base_embedder.parameters():
                p.requires_grad = False

        return base_embedder


    def assembling_the_layers(self, base_embedder) -> nn.Sequential:
        """Вычисляем входную размерность для головы предсказания и собираем слои MLP"""
        # Определяем размерность текстовых эмбеддингов из конфигурации базовой модели
        try:
            txt_dim = base_embedder.config.hidden_size
        except Exception:
            txt_dim = 1024  # Резервная размерность, если конфигурация недоступна

        # Вычисляем входную размерность для головы предсказания
        mlp_in = txt_dim + self.head_cfg.categorical_embedding_dim * 3
        # Добавляем размерности для опциональных числовых признаков
        mlp_in += sum([config.data.include_input_length, config.data.include_concurrent_requests,
                       config.data.include_model_instances])

        # Собираем слои MLP головы
        layers: list[nn.Module] = []
        if self.head_cfg.use_layernorm:
            layers.append(nn.LayerNorm(mlp_in))

        # Строим скрытые слои с активациями и dropout
        in_dim = mlp_in
        for h in self.head_cfg.hidden_layers:
            layers.extend([nn.Linear(in_dim, h), nn.ReLU(), nn.Dropout(self.head_cfg.dropout)])
            in_dim = h

        # Финальный выходной слой (предсказание одного значения - времени)
        layers.append(nn.Linear(in_dim, 1))
        prediction_head = nn.Sequential(*layers)

        # Применяем оптимизацию torch.compile, если настроена и доступна
        if config.model.torch_compile and hasattr(torch, "compile"):
            try:
                prediction_head = torch.compile(prediction_head, mode=config.model.compile_mode)
            except Exception as e:
                logger.warning(f'Оптимизация torch.compile не применена, ошибка: {e}')

        return prediction_head


    @staticmethod
    def configure_bnb_quantization() -> BitsAndBytesConfig | None:
        """Настраиваем конфигурацию BitsAndBytes для квантизации"""
        if config.model.quantization.load_in_4bit:
            return BitsAndBytesConfig(
                load_in_4bit=True,
                bnb_4bit_quant_type=config.model.quantization.bnb_4bit_quant_type,  # nf4 - normalized float 4
                bnb_4bit_compute_dtype=config.model.quantization.bnb_4bit_compute_dtype,
                bnb_4bit_use_double_quant=config.model.quantization.bnb_4bit_use_double_quant  # двойная квантизация
            )
        elif config.model.quantization.load_in_8bit:
            return BitsAndBytesConfig(load_in_8bit=True)
        return None

    @staticmethod
    def get_attention_implementation() -> str:
        """Определяет оптимальную реализацию механизма внимания (attn_impl)."""
        if not config.model.flash_attention_2:
            logger.info('Flash Attention 2 отключён в конфигурации. Использую "eager"')
            return "eager"

        try:
            arch_cfg = AutoConfig.from_pretrained(config.model.base_embedder_name)
            supports_flash2 = getattr(arch_cfg, "_flash_attn_2_enabled", False)
            if supports_flash2 and torch.cuda.is_available():
                logger.info('Используется механизм внимания "flash_attention_2"')
                return "flash_attention_2"
        except Exception as e:
            logger.warning(f'Ошибка при проверке поддержки Flash Attention 2: {e}')

        logger.info('Используется механизм внимания "eager"')
        return "eager"

    def configure_lora_config(self, base_embedder) -> LoraConfig | None:
        """Настраиваем параметры LoRA с поддержкой продвинутых техник"""
        # Автоматически находим или используем настроенные целевые модули для LoRA
        target_modules = self.perf_config.target_module_keywords
        if self.perf_config.auto_find_target_modules:
            target_modules = find_target_modules(base_embedder, self.perf_config.target_module_keywords)

        if not target_modules:
            logger.warning("Целевые модули для LoRA не найдены")
            return None

        current_lora_alpha = self.perf_config.r if self.perf_config.use_rslora else self.perf_config.lora_alpha

        # Создаем и применяем конфигурацию LoRA
        lora = LoraConfig(
            r=self.perf_config.r,
            lora_alpha=current_lora_alpha,
            target_modules=target_modules,
            lora_dropout=self.perf_config.lora_dropout,
            bias="none",
            task_type=TaskType.FEATURE_EXTRACTION,
            use_dora=self.perf_config.use_dora,
            use_rslora=self.perf_config.use_rslora
        )
        return lora

    def load_trained_model(self):
        """Загрузка обученной модели из чекпоинта с поддержкой PEFT адаптера и квантизации"""
        # Загрузка PEFT адаптера если доступен и сконфигурирован
        model_dir = find_model_directory(config.path_project.training_output_dir, self.task_type)
        if not model_dir:
            raise FileNotFoundError(f"Директория модели не найдена, параметры: {config.path_project.training_output_dir} / {self.task_type}")

        adapter_config_path = model_dir / "adapter_config.json"
        if self.perf_config.enabled and adapter_config_path.is_file():
            self.base_embedder = PeftModel.from_pretrained(self.base_embedder, str(model_dir), is_trainable=self.is_trainable)
        else:
            logger.info("PEFT отключен в конфигурации или сохраненный адаптер не найден.")

        # Загрузка state dict модели с поддержкой как PyTorch так и SafeTensors форматов
        if (state_dict_path := model_dir / "model.safetensors").exists():
            state_dict = load_file(state_dict_path, device=str(self.device))
        elif (state_dict_path := model_dir / "pytorch_model.bin").exists():
            state_dict = torch.load(state_dict_path, map_location=str(self.device))
        else:
            raise FileNotFoundError(f"Файл state dict не найден в {model_dir}")

        # Загрузка state dict с graceful обработкой missing/unexpected keys
        missing_keys, unexpected_keys = self.load_state_dict(state_dict, strict=False)
        # Фильтрация ключей base model которые обрабатываются отдельно
        relevant_missing = [k for k in missing_keys if not k.startswith("base_embedder.base_model.")]
        relevant_unexpected = [k for k in unexpected_keys if not k.startswith("base_embedder.base_model.")]

        if relevant_missing:
            logger.warning(f"Отсутствующие ключи при загрузке state_dict: {relevant_missing[:10]}...")
        if relevant_unexpected:
            logger.warning(f"Неожиданные ключи при загрузке state_dict: {relevant_unexpected[:10]}...")
        logger.info("State_dict загружен.")


    def forward(
            self,
            input_ids: torch.Tensor,
            attention_mask: torch.Tensor,
            task_type: torch.Tensor,
            hardware: torch.Tensor,
            full_model_name: torch.Tensor,
            input_length: torch.Tensor | None = None,
            concurrent_requests: torch.Tensor | None = None,
            model_instances: torch.Tensor | None = None,
    ) -> torch.Tensor:
        """
        Прямой проход, объединяющий текстовые эмбеддинги с категориальными и числовыми признаками.

        Извлекает представление CLS токена из базовой модели, объединяет с категориальными
        эмбеддингами и опциональными числовыми признаками, затем пропускает через голову
        предсказания для получения предсказания времени.

        Args:
            input_ids (torch.Tensor): Токенизированный текст входа, shape: (batch_size, seq_len)
            attention_mask (torch.Tensor): Маска внимания для padding токенов, shape: (batch_size, seq_len)
            task_type (torch.Tensor): ID типа задачи, shape: (batch_size,)
            hardware (torch.Tensor): ID типа железа, shape: (batch_size,)
            full_model_name (torch.Tensor): ID названия модели, shape: (batch_size,)
            input_length (Optional[torch.Tensor]): Длина входной последовательности, shape: (batch_size,) или (batch_size, 1)
            concurrent_requests (Optional[torch.Tensor]): Количество параллельных запросов, shape: (batch_size,) или (batch_size, 1)
            model_instances (Optional[torch.Tensor]): Количество экземпляров модели, shape: (batch_size,) или (batch_size, 1)

        Returns:
            torch.Tensor: Предсказанное время выполнения для каждого примера в батче, shape: (batch_size,)

        Note:
            Модель использует CLS токен (первый токен) как агрегированное представление
            всей входной последовательности. Это стандартный подход для задач классификации
            и регрессии с использованием трансформеров.
        """
        # Извлекаем CLS токен представление из базовой языковой модели
        # CLS токен (первый токен) содержит агрегированную информацию о всей последовательности
        cls = self.base_embedder(input_ids=input_ids, attention_mask=attention_mask).last_hidden_state[:, 0]

        # Генерируем категориальные эмбеддинги для метаданных задачи
        # Каждый категориальный признак преобразуется в плотный вектор фиксированной размерности
        task_e = self.task_emb(task_type)  # эмбеддинг типа задачи
        hw_e = self.hw_emb(hardware)  # эмбеддинг типа железа
        model_e = self.model_emb(full_model_name)  # эмбеддинг названия модели

        # Собираем опциональные числовые признаки на основе конфигурации
        num_feats: list[torch.Tensor] = []

        # Обрабатываем длину входной последовательности
        if config.data.include_input_length and input_length is not None:
            # Приводим к shape (batch_size, 1) если необходимо
            if input_length.dim() == 1:
                input_length = input_length.unsqueeze(1)
            num_feats.append(input_length)

        # Обрабатываем количество параллельных запросов
        if config.data.include_concurrent_requests and concurrent_requests is not None:
            # Приводим к shape (batch_size, 1) если необходимо
            if concurrent_requests.dim() == 1:
                concurrent_requests = concurrent_requests.unsqueeze(1)
            num_feats.append(concurrent_requests)

        # Обрабатываем количество экземпляров модели
        if config.data.include_model_instances and model_instances is not None:
            # Приводим к shape (batch_size, 1) если необходимо
            if model_instances.dim() == 1:
                model_instances = model_instances.unsqueeze(1)
            num_feats.append(model_instances)

        # Объединяем все признаки для входа в голову предсказания
        # Порядок: [текстовые эмбеддинги, тип задачи, железо, модель, числовые признаки]
        all_feats = [cls, task_e, hw_e, model_e] + num_feats
        x = torch.cat(all_feats, dim=1)  # конкатенация по размерности признаков

        # Генерируем финальное предсказание через MLP голову
        pred = self.prediction_head(x)

        # Убираем последнюю размерность для получения скалярного выхода
        # shape: (batch_size, 1) -> (batch_size,)
        return pred.squeeze(-1)
