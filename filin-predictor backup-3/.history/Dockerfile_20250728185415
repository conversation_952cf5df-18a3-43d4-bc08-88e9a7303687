# Используем официальный образ Python 3.13 на базе Ubuntu
FROM python:3.13-slim-bookworm

# Устанавливаем переменные окружения
ENV DEBIAN_FRONTEND=noninteractive \
    PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1

# Устанавливаем системные зависимости и CUDA runtime
RUN apt-get update && apt-get install -y \
    # Системные инструменты
    git \
    curl \
    wget \
    build-essential \
    libssl-dev \
    libffi-dev \
    openssh-client \
    # Библиотеки для PyTorch и научных вычислений
    libopenblas-dev \
    liblapack-dev \
    libgfortran5 \
    # Зависимости для компиляции flash-attn
    ninja-build \
    cmake \
    # Дополнительные библиотеки
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    && rm -rf /var/lib/apt/lists/*

# Обновляем pip
RUN python -m pip install --upgrade pip

# Создаем рабочую директорию
WORKDIR /app

# Копируем файл зависимостей первым для лучшего кеширования слоев
COPY requirements.txt .

# Обновляем pip и устанавливаем базовые инструменты
RUN pip install --upgrade pip setuptools wheel

# Устанавливаем PyTorch с поддержкой CUDA 12.8
RUN pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu128

# Устанавливаем зависимости из requirements.txt
RUN pip install -r requirements.txt

# Пытаемся установить Flash Attention 2 (может не установиться на некоторых системах)
RUN pip install flash-attn --no-build-isolation || echo "Flash Attention 2 installation failed, continuing without it"

# Устанавливаем дополнительные оптимизации для Litestar
RUN pip install uvloop httptools ujson

# Копируем исходный код
COPY . .

# Устанавливаем PYTHONPATH для поиска модулей
ENV PYTHONPATH="/app/sources:/app"

# Экспонируем порт для API
EXPOSE 8007

# Команда по умолчанию - запуск API в dev режиме
CMD ["python", "sources/main.py"]