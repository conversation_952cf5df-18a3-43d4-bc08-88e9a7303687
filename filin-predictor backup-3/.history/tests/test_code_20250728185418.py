#!/usr/bin/env python3
"""
Simple code prediction test
"""
import requests
import json
import sys


def main():
    """Test code prediction with simple example"""
    data = {
        "text_input": "def hello_world():",
        "model_name": "microsoft/CodeBERT-base",
        "hardware": "gpu",
        "input_length": 100,
        "concurrent_requests": 1,
        "model_instances": 1,
    }

    try:
        response = requests.post(
            "http://localhost:8007/predict/code", json=data, timeout=30
        )
        print("Status Code:", response.status_code)
        print("Response:")
        print(json.dumps(response.json(), indent=2))

        if response.status_code == 201:
            sys.exit(0)
        else:
            sys.exit(1)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
