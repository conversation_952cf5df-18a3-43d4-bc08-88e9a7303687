#!/usr/bin/env python3
"""
Simple chat prediction test
"""
import requests
import json
import sys


def main():
    """Test chat prediction with simple example"""
    data = {
        "text_input": "Hello, how are you?",
        "model_name": "chat_instruct",
        "hardware": "gpu",
        "input_length": 50,
        "concurrent_requests": 1,
        "model_instances": 1,
    }

    try:
        response = requests.post(
            "http://localhost:8007/predict/chat", json=data, timeout=30
        )
        print("Status Code:", response.status_code)
        print("Response:")
        print(json.dumps(response.json(), indent=2))

        if response.status_code == 201:
            sys.exit(0)
        else:
            sys.exit(1)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
