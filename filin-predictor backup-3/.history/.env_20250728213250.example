# Filin Predictor Environment Variables

# HuggingFace Token (required for model access)
HF_TOKEN=your_huggingface_token_here

# Container Configuration
CONTAINER_NAME=filin-predictor
IMAGE_NAME=filin/predictor:latest
NETWORK_NAME=filin-predictor-net

# API Configuration
API_PORT=8007

# Volume Paths (can be absolute or relative)
DATA_PATH=./resources/data
MODELS_PATH=./resources/models
RESULTS_PATH=./resources/results
CONFIG_PATH=./config
SOURCES_PATH=./sources

# HuggingFace Cache
HUGGINGFACE_CACHE=${HOME}/.cache/huggingface

# Optional: Custom model settings
# MODEL_NAME=your-custom-model
# CUDA_VISIBLE_DEVICES=0
