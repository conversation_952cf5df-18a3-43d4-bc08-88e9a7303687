#!/bin/bash

# Filin Predictor Docker Management Script

CONTAINER_NAME="filin-predictor"
IMAGE_NAME="filin/predictor:latest"
NETWORK_NAME="filin-predictor-net"
RESOURCES_PATH="$(pwd)/resources"
CONFIG_PATH="$(pwd)/config"
SOURCES_PATH="$(pwd)/sources"
HUGGINGFACE_CACHE="${HOME}/.cache/huggingface"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_info() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# Function to check if container exists
container_exists() {
    docker ps -a --format "table {{.Names}}" | grep -q "^${CONTAINER_NAME}$"
}

# Function to check if container is running
container_running() {
    docker ps --format "table {{.Names}}" | grep -q "^${CONTAINER_NAME}$"
}

# Function to check if network exists
network_exists() {
    docker network ls --format "table {{.Name}}" | grep -q "^${NETWORK_NAME}$"
}

# Function to create necessary directories
create_directories() {
    print_status "Creating necessary directories..."
    mkdir -p "${RESOURCES_PATH}/data" "${RESOURCES_PATH}/models" "${CONFIG_PATH}" "${HUGGINGFACE_CACHE}"
    print_status "Directories created: resources/data, resources/models, config, huggingface cache"
}

# Build function
build() {
    local NO_CACHE=""
    if [ "$1" = "--no-cache" ]; then
        NO_CACHE="--no-cache"
        print_status "Building Docker image with --no-cache: ${IMAGE_NAME}..."
    else
        print_status "Building Docker image: ${IMAGE_NAME}..."
    fi
    
    DOCKER_BUILDKIT=1 docker build ${NO_CACHE} -t ${IMAGE_NAME} .
    if [ $? -eq 0 ]; then
        print_status "Build completed successfully!"
    else
        print_error "Build failed!"
        exit 1
    fi
}

# Up function
up() {
    create_directories
    
    # Load environment variables from .env file if it exists
    if [ -f ".env" ]; then
        print_status "Loading environment variables from .env file..."
        export $(grep -v '^#' .env | xargs)
    fi
    
    # Create network if it doesn't exist
    if ! network_exists; then
        print_status "Creating Docker network: ${NETWORK_NAME}"
        docker network create ${NETWORK_NAME}
    fi

    # Check if container already exists
    if container_exists; then
        if container_running; then
            print_warning "Container ${CONTAINER_NAME} is already running!"
            print_status "API available at: http://localhost:8007"
            return 0
        else
            print_status "Starting existing container..."
            docker start ${CONTAINER_NAME}
            sleep 3
            print_status "Container started!"
            print_status "API available at: http://localhost:8007"
            return 0
        fi
    fi

    print_status "Starting new container..."
    
    # Get GPU settings
    GPU_FLAG="--gpus all --runtime=nvidia"
    if ! command -v nvidia-smi &> /dev/null; then
        print_warning "NVIDIA GPU not detected, running without GPU support"
        GPU_FLAG=""
    else
        # Check if nvidia runtime is available
        if ! docker info | grep -q "nvidia"; then
            print_warning "NVIDIA Docker runtime not available, using default GPU settings"
            GPU_FLAG="--gpus all"
        fi
    fi
    
    docker run -d \
        --network ${NETWORK_NAME} \
        ${GPU_FLAG} \
        -p 8007:8007 \
        --name ${CONTAINER_NAME} \
        -v "${RESOURCES_PATH}:/app/resources" \
        -v "${CONFIG_PATH}:/app/config" \
        -v "${SOURCES_PATH}:/app/sources" \
        -v "$(pwd)/tests:/app/tests" \
        -v "${HUGGINGFACE_CACHE}:/root/.cache/huggingface" \
        -e HF_HOME="/root/.cache/huggingface" \
        -e TRANSFORMERS_CACHE="/root/.cache/huggingface" \
        -e HF_TOKEN="${HF_TOKEN}" \
        --restart unless-stopped \
        ${IMAGE_NAME}

    if [ $? -eq 0 ]; then
        print_status "Container started successfully!"
        sleep 5
        print_status "API available at: http://localhost:8007"
        print_info "Use './run.sh logs' to see container logs"
        print_info "Use './run.sh health' to check API health"
    else
        print_error "Failed to start container!"
        exit 1
    fi
}

# Down function
down() {
    if container_running; then
        print_status "Stopping container..."
        docker stop ${CONTAINER_NAME}
        print_status "Container stopped!"
    else
        print_warning "Container is not running!"
    fi
}

# Restart function
restart() {
    print_status "Restarting container..."
    down
    sleep 3
    up
}

# Rebuild function - complete rebuild from scratch
rebuild() {
    print_status "Starting complete rebuild process..."
    print_status "This will: stop container -> remove everything -> rebuild image -> start fresh"
    
    # Stop container if running
    if container_running; then
        print_status "Step 1/4: Stopping container..."
        down
        sleep 2
    else
        print_status "Step 1/4: Container not running, skipping stop"
    fi
    
    # Remove everything
    print_status "Step 2/4: Removing container, network, and image..."
    remove
    sleep 2
    
    # Build fresh image
    print_status "Step 3/4: Building fresh Docker image..."
    build
    
    # Start container
    print_status "Step 4/4: Starting fresh container..."
    up
    
    print_status "Rebuild completed successfully!"
}

# Remove function
remove() {
    # Stop container if running
    if container_running; then
        print_status "Stopping container..."
        docker stop ${CONTAINER_NAME}
    fi

    # Remove container if exists
    if container_exists; then
        print_status "Removing container..."
        docker rm ${CONTAINER_NAME}
    fi

    # Remove network if exists and no other containers are using it
    if network_exists; then
        local network_in_use=$(docker network inspect ${NETWORK_NAME} --format '{{len .Containers}}' 2>/dev/null || echo "0")
        if [ "$network_in_use" -eq 0 ]; then
            print_status "Removing network..."
            docker network rm ${NETWORK_NAME}
        else
            print_warning "Network ${NETWORK_NAME} is still in use by other containers"
        fi
    fi

    # Always remove Docker image
    if docker images | grep -q "${IMAGE_NAME}"; then
        print_status "Removing Docker image..."
        docker rmi ${IMAGE_NAME}
    fi

    print_status "Cleanup completed!"
}

# Status function
status() {
    print_status "Container status:"
    if container_exists; then
        if container_running; then
            echo -e "  Status: ${GREEN}Running${NC}"
            echo "  Ports: $(docker port ${CONTAINER_NAME} 2>/dev/null || echo 'N/A')"
            echo "  Started: $(docker inspect ${CONTAINER_NAME} --format '{{.State.StartedAt}}' 2>/dev/null)"
        else
            echo -e "  Status: ${YELLOW}Stopped${NC}"
        fi
        echo "  Image: $(docker inspect ${CONTAINER_NAME} --format '{{.Config.Image}}' 2>/dev/null)"
        echo "  Container ID: $(docker inspect ${CONTAINER_NAME} --format '{{.Id}}' 2>/dev/null | cut -c1-12)"
    else
        echo -e "  Status: ${RED}Not found${NC}"
    fi
    
    print_status "Network status:"
    if network_exists; then
        echo -e "  Network: ${GREEN}${NETWORK_NAME} exists${NC}"
    else
        echo -e "  Network: ${YELLOW}${NETWORK_NAME} not found${NC}"
    fi
    
    print_status "Volume status:"
    echo "  Resources: ${RESOURCES_PATH} $([ -d "${RESOURCES_PATH}" ] && echo "(exists)" || echo "(missing)")"
    echo "  Config: ${CONFIG_PATH} $([ -d "${CONFIG_PATH}" ] && echo "(exists)" || echo "(missing)")"
    echo "  Sources: ${SOURCES_PATH} $([ -d "${SOURCES_PATH}" ] && echo "(exists)" || echo "(missing)")"
    echo "  Tests: $(pwd)/tests $([ -d "$(pwd)/tests" ] && echo "(exists)" || echo "(missing)")"
    echo "  HuggingFace Cache: ${HUGGINGFACE_CACHE} $([ -d "${HUGGINGFACE_CACHE}" ] && echo "(exists)" || echo "(missing)")"
}

# Logs function
logs() {
    if container_exists; then
        local FOLLOW_MODE=""
        if [ "$2" = "-f" ] || [ "$2" = "--follow" ]; then
            FOLLOW_MODE="-f"
            print_status "Showing container logs in follow mode (press Ctrl+C to exit):"
        else
            print_status "Showing last 50 lines of container logs:"
            FOLLOW_MODE="--tail=50"
        fi
        docker logs ${FOLLOW_MODE} ${CONTAINER_NAME}
    else
        print_error "Container does not exist!"
        exit 1
    fi
}

# Check models function
check_models() {
    if ! container_running; then
        print_error "Container is not running! Use './run.sh up' first"
        exit 1
    fi
    
    print_status "Checking available models..."
    
    # Check trained models
    print_info "Checking trained models in container..."
    docker exec ${CONTAINER_NAME} find /app/resources/models -name "*.pth" -o -name "*.safetensors" -o -name "*.joblib" | head -10
    
    # Check HuggingFace cache
    print_info "Checking HuggingFace cache in container..."
    docker exec ${CONTAINER_NAME} ls -la /root/.cache/huggingface/ 2>/dev/null || print_warning "HuggingFace cache directory not found in container"
    
    # Test model loading
    print_info "Testing model access..."
    docker exec ${CONTAINER_NAME} python -c "
import sys
sys.path.append('/app/sources')
try:
    from settings import config
    print(f'Base embedder model: {config.model.base_embedder_name}')
    print(f'Training output dir: {config.path_project.training_output_dir}')
    print('Settings loaded successfully!')
except Exception as e:
    print(f'Error loading settings: {e}')
"
}
health() {
    if ! container_running; then
        print_error "Container is not running!"
        exit 1
    fi
    
    HEALTH_URL="http://localhost:8007/health"
    
    print_status "Checking API health..."
    
    # Check if port is accessible
    if ! nc -z localhost 8007 2>/dev/null; then
        print_error "API port 8007 is not accessible!"
        exit 1
    fi
    
    # Check health endpoint
    if command -v curl &> /dev/null; then
        response=$(curl -s -w "%{http_code}" -o /tmp/health_response.json ${HEALTH_URL} 2>/dev/null)
        http_code=${response: -3}
        
        if [ "$http_code" = "200" ] || [ "$http_code" = "503" ]; then
            print_status "API is responding!"
            echo "Health check result:"
            cat /tmp/health_response.json 2>/dev/null | python3 -m json.tool 2>/dev/null || cat /tmp/health_response.json
            rm -f /tmp/health_response.json
        else
            print_error "API health check failed! HTTP code: ${http_code}"
            exit 1
        fi
    else
        print_warning "curl not found, checking basic connectivity only"
        print_status "API port 8007 is accessible"
    fi
}

# Prediction function
predict() {
    if ! container_running; then
        print_error "Container is not running! Use './run.sh up' first"
        exit 1
    fi
    
    local TYPE=${2:-"code"}
    print_status "Making prediction for ${TYPE} task..."
    
    case "$TYPE" in
        code)
            print_status "Running code prediction example..."
            if [ -f "tests/test_code.py" ]; then
                docker exec -it ${CONTAINER_NAME} python /app/tests/test_code.py
            else
                print_error "Test file tests/test_code.py not found!"
                exit 1
            fi
            ;;
        chat)
            print_status "Running chat prediction example..."
            if [ -f "tests/test_chat.py" ]; then
                docker exec -it ${CONTAINER_NAME} python /app/tests/test_chat.py
            else
                print_error "Test file tests/test_chat.py not found!"
                exit 1
            fi
            ;;
        *)
            print_error "Unknown prediction type: ${TYPE}"
            echo "Available types: code, chat"
            exit 1
            ;;
    esac
}

# Prediction functions
# Test function - comprehensive testing
test() {
    if ! container_running; then
        print_error "Container is not running! Use './run.sh up' first"
        exit 1
    fi
    
    print_status "Running comprehensive prediction tests..."
    
    if [ -f "tests/test_predictions.py" ]; then
        docker exec -it ${CONTAINER_NAME} python /app/tests/test_predictions.py
    else
        print_error "Test file tests/test_predictions.py not found!"
        exit 1
    fi
}

# ONNX conversion function
convert_onnx() {
    if ! container_running; then
        print_error "Container is not running! Use './run.sh up' first"
        exit 1
    fi
    
    # Load environment variables from .env file if it exists
    local HF_TOKEN_VALUE=""
    if [ -f ".env" ]; then
        print_status "Loading environment variables from .env file..."
        export $(grep -v '^#' .env | xargs)
        HF_TOKEN_VALUE="${HF_TOKEN}"
    fi
    
    # Use default token if not found in .env
    if [ -z "$HF_TOKEN_VALUE" ]; then
        print_warning "HF_TOKEN not found in .env file!"
        print_error "Please add HF_TOKEN to .env file or set it as environment variable"
        exit 1
    fi
    
    print_status "Converting PyTorch models to ONNX format..."
    docker exec ${CONTAINER_NAME} bash -c "cd /app && PYTHONPATH=/app/sources:/app HF_TOKEN=${HF_TOKEN_VALUE} python3 -m sources.core.onnx_converter"
    
    if [ $? -eq 0 ]; then
        print_status "ONNX conversion completed successfully!"
        print_info "ONNX models should be available in resources/models/onnx/"
        print_info "Files created:"
        docker exec ${CONTAINER_NAME} ls -la /app/resources/models/onnx/*.onnx 2>/dev/null || print_warning "Could not list ONNX files"
    else
        print_error "ONNX conversion failed!"
        print_error "Check logs with './run.sh logs' for more details"
        exit 1
    fi
}

# Sync function - copy sources to container and restart
sync() {
    if ! container_running; then
        print_error "Container is not running! Use './run.sh up' first"
        exit 1
    fi
    
    print_status "Syncing sources to container..."
    
    # Copy sources to container
    if [ -d "${SOURCES_PATH}" ]; then
        print_info "Copying sources directory..."
        docker cp "${SOURCES_PATH}/." "${CONTAINER_NAME}:/app/sources/"
        if [ $? -eq 0 ]; then
            print_status "Sources copied successfully!"
        else
            print_error "Failed to copy sources!"
            exit 1
        fi
    else
        print_error "Sources directory ${SOURCES_PATH} not found!"
        exit 1
    fi
    
    # Copy tests to container if they exist
    if [ -d "$(pwd)/tests" ]; then
        print_info "Copying tests directory..."
        docker cp "$(pwd)/tests/." "${CONTAINER_NAME}:/app/tests/"
        if [ $? -eq 0 ]; then
            print_status "Tests copied successfully!"
        else
            print_warning "Failed to copy tests, but continuing..."
        fi
    fi
    
    # Copy config if it exists
    if [ -d "${CONFIG_PATH}" ]; then
        print_info "Copying config directory..."
        docker cp "${CONFIG_PATH}/." "${CONTAINER_NAME}:/app/config/"
        if [ $? -eq 0 ]; then
            print_status "Config copied successfully!"
        else
            print_warning "Failed to copy config, but continuing..."
        fi
    fi
    
    print_status "Restarting container to apply changes..."
    docker restart ${CONTAINER_NAME}
    
    if [ $? -eq 0 ]; then
        print_status "Container restarted successfully!"
        sleep 5
        print_status "API should be available at: http://localhost:8007"
        print_info "Use './run.sh logs' to see container logs"
        print_info "Use './run.sh health' to check API health"
    else
        print_error "Failed to restart container!"
        exit 1
    fi
}

# Shell access
shell() {
    if ! container_running; then
        print_error "Container is not running! Use './run.sh up' first"
        exit 1
    fi
    
    print_status "Opening shell in container..."
    docker exec -it ${CONTAINER_NAME} /bin/bash
}

# Help function
show_help() {
    echo "Filin Predictor Docker Management Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Container Management:"
    echo "  up                  Start the container (create if doesn't exist)"
    echo "  down                Stop the container"
    echo "  restart             Restart the container"
    echo "  rebuild             Complete rebuild: down -> remove -> build -> up"
    echo "  remove              Stop and remove container, network, and image"
    echo "  status              Show container and system status"
    echo "  logs                Show last 50 lines of container logs"
    echo "  logs -f             Show container logs in follow mode"
    echo "  health              Check API health"
    echo "  check-models        Check available models and model access"
    echo ""
    echo "Prediction Operations:"
    echo "  predict [type]      Make a prediction (type: code or chat, default: code)"
    echo "  test                Run comprehensive prediction tests"
    echo "  convert-onnx        Convert PyTorch models to ONNX format"
    echo ""
    echo "Utilities:"
    echo "  sync                Copy sources to container and restart (fast development)"
    echo "  shell               Open interactive shell in container"
    echo "  help                Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 build && $0 up        # Build and start"
    echo "  $0 build --no-cache      # Force rebuild without Docker cache"
    echo "  $0 rebuild               # Complete rebuild from scratch"
    echo "  $0 health                # Check if everything is working"
    echo "  $0 predict code          # Make a code prediction"
    echo "  $0 predict chat          # Make a chat prediction"
    echo "  $0 test                  # Run comprehensive prediction tests"
    echo "  $0 sync                  # Copy sources to container and restart (fast)"
    echo "  $0 convert-onnx          # Convert models to ONNX format"
    echo "  $0 restart               # Restart container"
    echo "  $0 remove                # Clean up everything"
    echo ""
    echo "Environment:"
    echo "  Data volumes: ./resources, ./config, ./sources, ~/.cache/huggingface"
    echo "  API will be available at http://localhost:8007"
}

# Main script logic
case "$1" in
    build)
        build "$2"
        ;;
    up)
        up
        ;;
    down)
        down
        ;;
    restart)
        restart
        ;;
    rebuild)
        rebuild
        ;;
    remove)
        remove
        ;;
    status)
        status
        ;;
    logs)
        logs
        ;;
    health)
        health
        ;;
    check-models)
        check_models
        ;;
    predict)
        predict "$@"
        ;;
    test)
        test
        ;;
    sync)
        sync
        ;;
    convert-onnx)
        convert_onnx
        ;;
    shell)
        shell
        ;;
    help|--help|-h)
        show_help
        ;;
    "")
        print_error "No command specified!"
        echo ""
        show_help
        exit 1
        ;;
    *)
        print_error "Unknown command: $1"
        echo ""
        show_help
        exit 1
        ;;
esac
