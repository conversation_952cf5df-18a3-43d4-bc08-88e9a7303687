accelerate==1.9.0
annotated-types==0.7.0
anyio==4.9.0
bitsandbytes==0.46.1
certifi==2025.7.14
charset-normalizer==3.4.2
click==8.2.1
coloredlogs==15.0.1
Faker==37.4.2
filelock==3.13.1
flatbuffers==25.2.10
fsspec==2024.6.1
h11==0.16.0
hf-xet==1.1.5
httpcore==1.0.9
httpx==0.28.1
huggingface-hub==0.33.4
humanfriendly==10.0
idna==3.10
Jinja2==3.1.4
joblib==1.5.1
litestar==2.16.0
litestar-htmx==0.5.0
markdown-it-py==3.0.0
MarkupSafe==3.0.2
mdurl==0.1.2
mpmath==1.3.0
msgspec==0.19.0
multidict==6.6.3
multipart==1.2.1
networkx==3.3
numpy==2.1.2
nvidia-cublas-cu12==*********
nvidia-cuda-cupti-cu12==12.8.57
nvidia-cuda-nvrtc-cu12==12.8.61
nvidia-cuda-runtime-cu12==12.8.57
nvidia-cudnn-cu12==********
nvidia-cufft-cu12==*********
nvidia-cufile-cu12==*********
nvidia-curand-cu12==*********
nvidia-cusolver-cu12==*********
nvidia-cusparse-cu12==*********
nvidia-cusparselt-cu12==0.6.3
nvidia-nccl-cu12==2.26.2
nvidia-nvjitlink-cu12==12.8.61
nvidia-nvtx-cu12==12.8.55
onnx==1.18.0
onnxruntime-gpu==1.22.0
packaging==25.0
peft==0.16.0
pillow==11.0.0
polyfactory==2.22.1
protobuf==6.31.1
psutil==7.0.0
pydantic==2.11.7
pydantic-settings==2.10.1
pydantic_core==2.33.2
Pygments==2.19.2
python-dotenv==1.1.1
PyYAML==6.0.2
regex==2024.11.6
requests==2.32.4
rich==14.0.0
rich-click==1.8.9
safetensors==0.5.3
scikit-learn==1.6.1
scipy==1.16.0
setuptools==70.2.0
sniffio==1.3.1
sympy==1.13.3
threadpoolctl==3.6.0
tokenizers==0.21.2
torch==2.7.1+cu128
torchaudio==2.7.1+cu128
torchvision==0.22.1+cu128
tqdm==4.67.1
transformers==4.53.2
triton==3.3.1
typing-inspection==0.4.1
typing_extensions==4.12.2
tzdata==2025.2
urllib3==2.5.0
uvicorn==0.35.0
uvloop==0.21.0
