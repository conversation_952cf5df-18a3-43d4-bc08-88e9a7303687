import time

import numpy as np

from settings import config
from core.onnx_predictor import ONNXRuntimePredictor
from core.inference import Predictor

def benchmark_onnx_vs_pytorch():
    """Сравнение производительности"""

    # Импортируем оригинальный Predictor


    test_data = {
        "text_input": "def fibonacci(n): return n if n <= 1 else fi<PERSON><PERSON><PERSON>(n-1) + <PERSON><PERSON><PERSON><PERSON>(n-2)",
        "hardware": "gpu_a100",
        "model_name": config.model.base_embedder_name,
        "input_length": 100,
        "concurrent_requests": 1,
        "model_instances": 1
    }

    for task_type in ['code', 'chat']:
        print(f"\n{'= ' *50}")
        print(f"Бенчмарк для {task_type}")
        print(f"{'= ' *50}")

        try:
            # PyTorch predictor
            pytorch_predictor = Predictor(task_type=task_type)

            # ONNX predictor
            onnx_predictor = ONNXRuntimePredictor(task_type=task_type)

            # Прогрев
            print("Прогрев моделей...")
            for _ in range(3):
                pytorch_predictor.predict(**test_data)
                onnx_predictor.predict(**test_data)

            # Бенчмарк
            n_runs = 10

            # PyTorch
            pytorch_times = []
            for _ in range(n_runs):
                start = time.perf_counter()
                res = pytorch_predictor.predict(**test_data)
                print(f'PyTorch - {res}')
                pytorch_times.append(time.perf_counter() - start)

            # ONNX
            onnx_times = []
            for _ in range(n_runs):
                start = time.perf_counter()
                res = onnx_predictor.predict(**test_data)
                print(f'ONNX - {res}')
                onnx_times.append(time.perf_counter() - start)

            # Результаты
            pytorch_avg = np.mean(pytorch_times)
            onnx_avg = np.mean(onnx_times)

            print(f"\nРезультаты (среднее из {n_runs} запусков):")
            print(f"PyTorch: {pytorch_avg * 1000:.2f} ms")
            print(f"ONNX Runtime: {onnx_avg * 1000:.2f} ms")
            print(f"Ускорение: {pytorch_avg / onnx_avg:.2f}x")

        except Exception as e:
            print(f"Ошибка в бенчмарке {task_type}: {e}")


if __name__ == '__main__':
    benchmark_onnx_vs_pytorch()