# Инструкция по запуску сервиса

## Вариант 1: Запуск через Docker (Рекомендуется)

### 1. Создайте файл `.env` с HuggingFace токеном
```bash
# API Configuration
API__HOST="127.0.0.1"
API__PORT=8007

# HuggingFace Configuration  
HF_TOKEN="your_hf_token_here"
```

### 2. Скачайте модели из MiniO для chat и code: [ссылка](https://console.codefine.io/browser/filin-datasets/cHJlZGljdG9yX21vZGVscy8=)

### 3. Разархивируйте модели в папку resources/models
```bash
└── resources/
    └── models/
        ├── chat/
        └── code/
```

### 4. Соберите и запустите Docker контейнер
```bash
./run.sh build && ./run.sh up
```

### 5. Конвертируйте модели в ONNX (опционально)
```bash
./run.sh convert-onnx
```

### 6. Проверьте работу сервиса
```bash
./run.sh health
./run.sh predict code
```

## Вариант 2: Запуск локально

## 1. Установите Python версии 3.13 и активируйте виртуальную среду

## 2. Установите PyTorch из официального источника
```bash
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu128
```
## 3. Установите зависимости из requirements.txt
```bash
pip install -r requirements.txt
```

## 4. Скачайте модели из MiniO для chat и code: [ссылка](https://console.codefine.io/browser/filin-datasets/cHJlZGljdG9yX21vZGVscy8=)

## 5. Разархивируйте модели в папку resources/models, по структуре
```bash
└── resources/
    └── models/
        ├── chat/
        └── code/
```

## 6. Добавьте файл `.env` если хотите переопределить базовые настройки
```bash
API__HOST="127.0.0.1"
HF_TOKEN="your_hf_token_here"
```

## 7. Запустите сервис на PyTorch, чтобы корректно подгрузились веса на видеокарту
- Можно запустить тестовый скрипт в `sources/core/inference.py` с прогревом модели
- Либо запустить сервер в `sources/main.py`

## 8. Конвертируйте PyTorch модель в ONNX
Запустите скрипт:
```bash
PYTHONPATH=sources HF_TOKEN=your_token python3 -m sources.core.onnx_converter
```

## 9. Запустите тестовый скрипт для ONNX
Скрипт находится в `sources/core/onnx_predictor.py`

## 10. Запустите полностью сервис `sources/main.py`