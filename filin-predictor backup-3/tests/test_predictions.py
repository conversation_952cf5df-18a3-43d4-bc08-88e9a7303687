#!/usr/bin/env python3
"""
Comprehensive prediction tests for Filin Predictor API
"""
import requests
import json
import time
import sys


def test_prediction(endpoint, data, description):
    """Test a single prediction endpoint"""
    print(f"\n=== {description} ===")
    try:
        start_time = time.time()
        response = requests.post(
            f"http://localhost:8007/predict/{endpoint}", json=data, timeout=30
        )
        end_time = time.time()

        print(f"Status Code: {response.status_code}")
        print(f"Request time: {end_time - start_time:.2f}s")
        print("Response:", json.dumps(response.json(), indent=2))
        return response.status_code == 201
    except Exception as e:
        print(f"Error: {e}")
        return False


def main():
    """Run all prediction tests"""
    print("Starting comprehensive prediction tests...")
    print("Model configuration:")
    print("  Code tasks: chat_coder")
    print("  Chat tasks: chat_instruct")
    print("")

    # Test cases
    tests = [
        # Code predictions
        {
            "endpoint": "code",
            "data": {
                "text_input": "def fibonacci(n):",
                "model_name": "chat_coder",
                "hardware": "gpu",
                "input_length": 50,
                "concurrent_requests": 1,
                "model_instances": 1,
            },
            "description": "Code prediction - small input",
        },
        {
            "endpoint": "code",
            "data": {
                "text_input": "class DatabaseConnection: def __init__(self, host, port):",
                "model_name": "chat_coder",
                "hardware": "gpu",
                "input_length": 200,
                "concurrent_requests": 2,
                "model_instances": 1,
            },
            "description": "Code prediction - medium input with concurrency",
        },
        {
            "endpoint": "code",
            "data": {
                "text_input": "import tensorflow as tf\nfrom sklearn.model_selection import train_test_split\ndef create_neural_network():",
                "model_name": "chat_coder",
                "hardware": "gpu",
                "input_length": 1000,
                "concurrent_requests": 1,
                "model_instances": 2,
            },
            "description": "Code prediction - large input with multiple instances",
        },
        # Chat predictions
        {
            "endpoint": "chat",
            "data": {
                "text_input": "Hello, how can I help you today?",
                "model_name": "chat_instruct",
                "hardware": "gpu",
                "input_length": 30,
                "concurrent_requests": 1,
                "model_instances": 1,
            },
            "description": "Chat prediction - greeting",
        },
        {
            "endpoint": "chat",
            "data": {
                "text_input": "I need help with understanding machine learning concepts. Can you explain neural networks?",
                "model_name": "chat_instruct",
                "hardware": "gpu",
                "input_length": 100,
                "concurrent_requests": 3,
                "model_instances": 1,
            },
            "description": "Chat prediction - complex query with high concurrency",
        },
    ]

    successful_tests = 0
    total_tests = len(tests)

    for test_case in tests:
        success = test_prediction(
            test_case["endpoint"], test_case["data"], test_case["description"]
        )
        if success:
            successful_tests += 1
        time.sleep(1)  # Small delay between tests

    print(f"\n=== TEST SUMMARY ===")
    print(f"Successful tests: {successful_tests}/{total_tests}")
    print(f"Success rate: {(successful_tests/total_tests)*100:.1f}%")

    # Exit with appropriate code
    if successful_tests == total_tests:
        print("All tests passed!")
        sys.exit(0)
    else:
        print("Some tests failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
