#!/usr/bin/env python3
from pathlib import Path
from typing import Any, Dict, List, Optional
import json
import sys

from llm_router.logging_utils import get_logger

sys.path.insert(0, str(Path(__file__).parent.parent.parent))
logger = get_logger(__name__)


def test_litellm_compatible_setup() -> bool:
    """Test that our setup is compatible with LiteLLM's approach.

    Returns:
        True if test passes, False otherwise
    """
    logger.info("TEST: Starting LiteLLM compatibility test")
    try:
        import litellm
        from llm_router.router.custom_router import LLMTimePredictorRoutingStrategy

        # Используем константы вместо test_settings
        from llm_router.constants import LOAD_BALANCING_WEIGHT

        logger.debug(
            f"TEST: using constants - LOAD_BALANCING_WEIGHT={LOAD_BALANCING_WEIGHT}"
        )

        # Создаем mock router объект с cache для тестирования
        from unittest.mock import Mock

        mock_router = Mock()

        # Настраиваем cache
        mock_router.cache = Mock()
        mock_router.cache.async_get_cache = Mock(return_value=None)
        mock_router.cache.async_set_cache = Mock()
        mock_router.cache.delete_cache = Mock()

        # Настраиваем другие атрибуты роутера для работы с инициализацией
        mock_router.model_list = []
        mock_router.leastbusy_logger = None
        mock_router.routing_strategy_args = {}

        strategy = LLMTimePredictorRoutingStrategy(router=mock_router)
        logger.info("TEST: strategy created successfully")
        input_callback_valid = isinstance(litellm.input_callback, list)
        callbacks_valid = isinstance(litellm.callbacks, list)
        if input_callback_valid:
            logger.debug(
                f"TEST: input_callback has {len(litellm.input_callback)} items"
            )
            for i, cb in enumerate(litellm.input_callback):
                logger.debug(f"TEST: input_callback[{i}]: {type(cb).__name__}")
        if callbacks_valid:
            logger.debug(f"TEST: callbacks has {len(litellm.callbacks)} items")
            for i, cb in enumerate(litellm.callbacks):
                logger.debug(f"TEST: callbacks[{i}]: {type(cb).__name__}")
        success = input_callback_valid and callbacks_valid
        logger.info(
            f"TEST: LiteLLM compatibility test {('PASSED' if success else 'FAILED')}"
        )
        return success
    except Exception as e:
        logger.error(f"TEST: LiteLLM compatibility test failed: {e}")
        return False


def test_cache_key_format() -> bool:
    """Test that we use the correct cache key format.

    Returns:
        True if test passes, False otherwise
    """
    logger.info("TEST: starting cache key format test")
    try:
        test_deployment = {
            "model_info": {"id": "deployment-123"},
            "model_name": "gpt-4",
        }
        model_name = test_deployment["model_name"]
        expected_key = f"{model_name}_request_count"
        logger.debug(f"TEST: model name: {model_name}")
        logger.debug(f"TEST: expected cache key: {expected_key}")
        deployment_id = test_deployment["model_info"]["id"]
        if isinstance(deployment_id, int):
            deployment_id = str(deployment_id)
        logger.debug(f"TEST: deployment ID as string: {deployment_id}")
        expected_cache_data = {deployment_id: 2}
        logger.debug(f"TEST: expected cache data format: {expected_cache_data}")
        key_valid = expected_key == f"{model_name}_request_count"
        id_valid = isinstance(deployment_id, str)
        data_valid = isinstance(expected_cache_data, dict)
        success = key_valid and id_valid and data_valid
        logger.info(
            f"TEST: cache key format test {('PASSED' if success else 'FAILED')}"
        )
        return success
    except Exception as e:
        logger.error(f"TEST: cache key format test failed: {e}")
        return False


def test_hybrid_scoring_logic() -> bool:
    """Test the hybrid scoring calculations.

    Returns:
        True if test passes, False otherwise
    """
    logger.info("TEST: Starting hybrid scoring logic test")
    try:
        test_scenarios = [
            {
                "predicted": 1.5,
                "load": 0,
                "weight": 0.3,
                "desc": "Idle endpoint",
                "expected_min": 1.0,
                "expected_max": 1.1,
            },
            {
                "predicted": 1.5,
                "load": 2,
                "weight": 0.3,
                "desc": "Medium load",
                "expected_min": 1.1,
                "expected_max": 1.4,
            },
            {
                "predicted": 1.5,
                "load": 5,
                "weight": 0.3,
                "desc": "High load",
                "expected_min": 1.4,
                "expected_max": 2.0,
            },
            {
                "predicted": 1.5,
                "load": None,
                "weight": 0.3,
                "desc": "No load data",
                "expected_min": 1.5,
                "expected_max": 1.5,
            },
        ]
        all_passed = True
        for scenario in test_scenarios:
            predicted_time = scenario["predicted"]
            current_load = scenario["load"]
            weight = scenario["weight"]
            desc = scenario["desc"]
            expected_min = scenario["expected_min"]
            expected_max = scenario["expected_max"]
            logger.debug(f"TEST: Scenario - {desc}")
            logger.debug(
                f"TEST: ML prediction: {predicted_time}s, Load: {current_load}, Weight: {weight}"
            )
            if current_load is not None:
                max_reasonable_load = 5.0
                load_penalty = min(current_load / max_reasonable_load, 2.0)
                prediction_component = predicted_time * (1.0 - weight)
                load_component = load_penalty * weight * predicted_time
                hybrid_score = prediction_component + load_component
            else:
                hybrid_score = predicted_time
            score_valid = expected_min <= hybrid_score <= expected_max
            if not score_valid:
                logger.error(
                    f"TEST: {desc} - Score {hybrid_score:.3f} not in range [{expected_min}, {expected_max}]"
                )
                all_passed = False
            else:
                logger.debug(f"TEST: {desc} - Score {hybrid_score:.3f} OK")
        logger.info(
            f"TEST: Hybrid scoring logic test {('PASSED' if all_passed else 'FAILED')}"
        )
        return all_passed
    except Exception as e:
        logger.error(f"TEST: Hybrid scoring logic test failed: {e}")
        return False


def test_callback_flow_simulation() -> bool:
    """Test the complete callback flow simulation.

    Returns:
        True if test passes, False otherwise
    """
    logger.info("TEST: Starting callback flow simulation test")
    try:
        expected_kwargs = {
            "litellm_params": {
                "metadata": {"model_group": "gpt-4"},
                "model_info": {"id": "deployment-123"},
            }
        }
        has_litellm_params = "litellm_params" in expected_kwargs
        has_metadata = "metadata" in expected_kwargs.get("litellm_params", {})
        has_model_info = "model_info" in expected_kwargs.get("litellm_params", {})
        has_model_group = "model_group" in expected_kwargs.get(
            "litellm_params", {}
        ).get("metadata", {})
        has_deployment_id = "id" in expected_kwargs.get("litellm_params", {}).get(
            "model_info", {}
        )
        structure_valid = all(
            [
                has_litellm_params,
                has_metadata,
                has_model_info,
                has_model_group,
                has_deployment_id,
            ]
        )
        if structure_valid:
            logger.debug("TEST: Expected kwargs structure is valid")
            logger.debug(f"TEST: Structure: {json.dumps(expected_kwargs, indent=2)}")
        else:
            logger.error("TEST: Expected kwargs structure is invalid")
        flow_steps = [
            "LiteLLM calls log_pre_api_call (before request)",
            "Handler extracts model_group and deployment_id from kwargs",
            "Handler increments counter in cache",
            "Request is sent to LLM provider",
            "LiteLLM calls log_success_event (after successful response)",
            "Handler decrements counter in cache",
        ]
        logger.debug("TEST: Simulated callback flow:")
        for i, step in enumerate(flow_steps, 1):
            logger.debug(f"TEST:   {i}. {step}")
        success = structure_valid and len(flow_steps) == 6
        logger.info(
            f"TEST: Callback flow simulation test {('PASSED' if success else 'FAILED')}"
        )
        return success
    except Exception as e:
        logger.error(f"TEST: Callback flow simulation test failed: {e}")
        return False


def test_predictor_client_integration() -> bool:
    """Test predictor client integration (if available).

    Returns:
        True if test passes, False otherwise
    """
    logger.info("TEST: Starting predictor client integration test")
    try:
        from llm_router.predictor.predictor_client import (
            create_predictor_client_from_env,
        )

        logger.debug("TEST: Creating predictor client from environment")
        client = create_predictor_client_from_env()
        if client is None:
            logger.warning(
                "TEST: Predictor client is None - service may not be available"
            )
            return True
        logger.debug("TEST: Performing health check")
        is_healthy = client.health_check()
        if is_healthy:
            logger.info("TEST: Predictor service is healthy")
            logger.debug("TEST: Service is healthy and responding")
        else:
            logger.warning("TEST: Predictor service health check failed")
        success = client is not None
        logger.info(
            f"TEST: Predictor client integration test {('PASSED' if success else 'FAILED')}"
        )
        return success
    except Exception as e:
        logger.warning(f"TEST: Predictor client integration test failed: {e}")
        return True


def run_integration_tests() -> bool:
    """Run all integration tests.

    Returns:
        True if all tests pass, False otherwise
    """
    logger.info("TEST: Starting LiteLLM Least Busy integration test suite")
    test_functions = [
        ("LiteLLM Compatible Setup", test_litellm_compatible_setup),
        ("Cache Key Format", test_cache_key_format),
        ("Hybrid Scoring Logic", test_hybrid_scoring_logic),
        ("Callback Flow Simulation", test_callback_flow_simulation),
        ("Predictor Client Integration", test_predictor_client_integration),
    ]
    results = []
    for test_name, test_func in test_functions:
        logger.info(f"TEST: Running {test_name}...")
        try:
            success = test_func()
            results.append((test_name, success))
            status = "[SUCCESS]" if success else "[ERROR]"
            logger.info(f"TEST: {status} {test_name}")
        except Exception as e:
            logger.error(f"TEST: {test_name}: ERROR - {e}")
            results.append((test_name, False))
    passed = sum((1 for _, success in results if success))
    total = len(results)
    logger.info("TEST: Final results summary:")
    for test_name, success in results:
        status = "[SUCCESS]" if success else "[ERROR]"
        logger.info(f"TEST:   {status} {test_name}")
    overall_success = passed == total
    logger.info(f"TEST: Overall result: {passed}/{total} tests passed")
    if overall_success:
        logger.info("TEST: [SUCCESS] All integration tests passed!")
    else:
        logger.warning("TEST: [WARNING] Some integration tests failed")
    return overall_success


def main() -> None:
    """Main test execution function."""
    print("[INFO] LiteLLM Least Busy Integration Test Suite")
    print("=" * 60)
    os.environ.setdefault("LOG_LEVEL", "INFO")
    success = run_integration_tests()
    exit_code = 0 if success else 1
    logger.info(f"TEST: Exiting with code {exit_code}")
    sys.exit(exit_code)


if __name__ == "__main__":
    main()
