#!/usr/bin/env python3
"""
Advanced Custom Router Test Suite for ML-based Routing Demonstration.

This test demonstrates how the router selects different endpoints
based on ML time predictions for various request scenarios.
"""
from pathlib import Path
from typing import List, Dict, Any
import json
import os
import sys
import time

from dotenv import load_dotenv
from llm_router.logging_utils import get_logger
from llm_router.router_litellm.litellm_auth import LiteLLMAuth


load_dotenv()
sys.path.append(str(Path(__file__).parent.parent))
logger = get_logger(__name__)


def make_request(model: str, content: str, max_tokens: int = 100) -> Dict[str, Any]:
    """Send request to LiteLLM API and return detailed response information."""
    api_base = os.getenv("LITELLM_API_BASE", "http://localhost:4000")
    api_key = os.getenv("LITELLM_MASTER_KEY", "sk-1234")
    auth = LiteLLMAuth(api_base=api_base, api_key=api_key)
    data = {
        "model": model,
        "messages": [{"role": "user", "content": content}],
        "max_tokens": max_tokens,
    }
    try:
        start_time = time.time()
        response_data = auth.send_request("POST", "/v1/chat/completions", data)
        end_time = time.time()
        if response_data is None:
            return {
                "success": False,
                "error": "Failed to get response from API",
                "response_time": 0,
                "prompt_length": len(content),
                "request_model": model,
            }
        return {
            "success": True,
            "response_time": end_time - start_time,
            "model_used": response_data.get("model", "unknown"),
            "content": response_data["choices"][0]["message"]["content"][:150],
            "usage": response_data.get("usage", {}),
            "prompt_length": len(content),
            "request_model": model,
        }
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "response_time": 0,
            "prompt_length": len(content),
            "request_model": model,
        }


def test_routing_intelligence():
    """Test intelligent routing for different scenarios and workload types."""
    print("Testing intelligent ML-based routing")
    print("=" * 60)
    test_scenarios = [
        {
            "category": "Short chat requests",
            "model": "chat_instruct",
            "requests": [
                "Hello!",
                "How are you?",
                "What is AI?",
                "Tell me a joke",
                "What time is it?",
            ],
        },
        {
            "category": "Long chat requests",
            "model": "chat_instruct",
            "requests": [
                "Tell me in detail about the history of artificial intelligence development, starting from the first attempts to create thinking machines and ending with modern achievements in deep learning and neural networks.",
                "Explain the principles of quantum computers, their advantages over classical computers, main problems in their development and prospects for application in various fields of science and technology.",
                "Analyze the impact of social networks on modern society, consider both positive and negative aspects of their influence on interpersonal relationships, politics, economics and people's psychological health.",
            ],
        },
        {
            "category": "Simple code requests",
            "model": "chat_coder",
            "requests": [
                "def hello():",
                "print('Hello')",
                "x = 5",
                "for i in range(10):",
                "import os",
            ],
        },
        {
            "category": "Complex code requests",
            "model": "chat_coder",
            "requests": [
                "Write a comprehensive Python class for implementing a binary search tree with methods for insertion, deletion, searching, and tree traversal (inorder, preorder, postorder). Include proper error handling and documentation.",
                "Create a multi-threaded web scraper in Python that can handle rate limiting, retries, and concurrent requests while respecting robots.txt files and implementing proper exception handling for various HTTP errors.",
                "Implement a machine learning pipeline using scikit-learn that includes data preprocessing, feature selection, model training with cross-validation, hyperparameter tuning, and model evaluation with detailed metrics and visualization.",
            ],
        },
    ]
    all_results = []
    for scenario in test_scenarios:
        print(f"\n{scenario['category']}")
        print(f"   Model: {scenario['model']}")
        print(f"   Request count: {len(scenario['requests'])}")
        scenario_results = []
        for i, request in enumerate(scenario["requests"], 1):
            print(f"\n   {i}. Request ({len(request)} chars): {request[:50]}...")
            result = make_request(scenario["model"], request, max_tokens=50)
            if result["success"]:
                print(f"      Time: {result['response_time']:.2f}s")
                print(f"      Endpoint: {result['model_used']}")
                print(f"      Tokens: {result['usage'].get('total_tokens', 'N/A')}")
            else:
                print(f"      Error: {result['error']}")
            scenario_results.append(result)
            time.sleep(0.5)
        all_results.extend(scenario_results)
        successful = [r for r in scenario_results if r["success"]]
        if successful:
            avg_time = sum((r["response_time"] for r in successful)) / len(successful)
            endpoints_used = set((r["model_used"] for r in successful))
            print(f"\n   Scenario statistics:")
            print(f"      Successful: {len(successful)}/{len(scenario_results)}")
            print(f"      Average time: {avg_time:.2f}s")
            print(f"      Endpoints used: {len(endpoints_used)}")
            print(f"      Endpoints: {', '.join(sorted(endpoints_used))}")
    return analyze_routing_patterns(all_results)


def analyze_routing_patterns(results: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Analyze routing patterns and performance characteristics."""
    print(f"\nRouting pattern analysis")
    print("=" * 60)
    successful = [r for r in results if r["success"]]
    if not successful:
        print("No successful requests for analysis")
        return {}
    short_prompts = [r for r in successful if r["prompt_length"] < 50]
    long_prompts = [r for r in successful if r["prompt_length"] >= 50]
    chat_requests = [r for r in successful if r["request_model"] == "chat_instruct"]
    code_requests = [r for r in successful if r["request_model"] == "chat_coder"]
    endpoint_usage = {}
    for result in successful:
        endpoint = result["model_used"]
        if endpoint not in endpoint_usage:
            endpoint_usage[endpoint] = {"count": 0, "total_time": 0, "avg_time": 0}
        endpoint_usage[endpoint]["count"] += 1
        endpoint_usage[endpoint]["total_time"] += result["response_time"]
    for endpoint in endpoint_usage:
        endpoint_usage[endpoint]["avg_time"] = (
            endpoint_usage[endpoint]["total_time"] / endpoint_usage[endpoint]["count"]
        )
    print(f"Overall statistics:")
    print(f"   Total requests: {len(results)}")
    print(f"   Successful: {len(successful)}")
    print(
        f"   Average response time: {sum((r['response_time'] for r in successful)) / len(successful):.2f}s"
    )
    print(f"\nPrompt length analysis:")
    if short_prompts:
        print(f"   Short prompts (<50 chars): {len(short_prompts)} requests")
        print(
            f"   Average time: {sum((r['response_time'] for r in short_prompts)) / len(short_prompts):.2f}s"
        )
    if long_prompts:
        print(f"   Long prompts (≥50 chars): {len(long_prompts)} requests")
        print(
            f"   Average time: {sum((r['response_time'] for r in long_prompts)) / len(long_prompts):.2f}s"
        )
    print(f"\nTask type analysis:")
    if chat_requests:
        print(f"   Chat requests: {len(chat_requests)} requests")
        print(
            f"   Average time: {sum((r['response_time'] for r in chat_requests)) / len(chat_requests):.2f}s"
        )
    if code_requests:
        print(f"   Code requests: {len(code_requests)} requests")
        print(
            f"   Average time: {sum((r['response_time'] for r in code_requests)) / len(code_requests):.2f}s"
        )
    print(f"\nEndpoint usage:")
    for endpoint, stats in sorted(
        endpoint_usage.items(), key=lambda x: x[1]["count"], reverse=True
    ):
        print(f"   {endpoint}:")
        print(f"      Requests: {stats['count']}")
        print(f"      Average time: {stats['avg_time']:.2f}s")
        print(f"      Total time: {stats['total_time']:.2f}s")
    return {
        "total_requests": len(results),
        "successful_requests": len(successful),
        "endpoint_usage": endpoint_usage,
        "avg_response_time": (
            sum((r["response_time"] for r in successful)) / len(successful)
            if successful
            else 0
        ),
    }


def check_predictor_service() -> bool:
    """Check predictor service availability and functionality."""
    print("Checking predictor service...")
    predict_url = os.getenv("PREDICT_URL", "http://localhost:8008")
    try:
        import requests

        response = requests.get(f"{predict_url}/health", timeout=5)
        if response.status_code == 200:
            health_data = response.json()
            print(f"   Predictor service is available")
            print(f"   Status: {health_data.get('status', 'unknown')}")
            print(f"   Chat model: {health_data.get('chat_model', 'unknown')}")
            print(f"   Code model: {health_data.get('code_model', 'unknown')}")
            print("   Testing prediction functionality...")
            test_cases = [
                {
                    "task_type": "chat",
                    "prompt": "Hello world test",
                    "model": "chat_instruct",
                },
                {
                    "task_type": "code",
                    "prompt": "def hello(): pass",
                    "model": "chat_coder",
                },
            ]
            for test_case in test_cases:
                test_prediction = requests.get(
                    f"{predict_url}/predict/{test_case['task_type']}",
                    params={
                        "prompt": test_case["prompt"],
                        "hardware": "gpu_a100",
                        "model": test_case["model"],
                        "instances": 1,
                        "requests": 1,
                    },
                    timeout=10,
                )
                if test_prediction.status_code == 200:
                    pred_data = test_prediction.json()
                    predicted_time = pred_data.get("predicted_time", 0)
                    print(
                        f"   {test_case['task_type']} prediction: {predicted_time:.3f}s"
                    )
                else:
                    print(
                        f"   {test_case['task_type']} prediction error (HTTP {test_prediction.status_code})"
                    )
                    return False
            return True
        else:
            print(f"   Predictor service unavailable (HTTP {response.status_code})")
            return False
    except requests.exceptions.ConnectionError:
        print(f"   Cannot connect to predictor service at {predict_url}")
        print("   Make sure predictor service is running at the specified URL")
        return False
    except Exception as e:
        print(f"   Predictor service check error: {e}")
        return False


def check_environment_variables() -> bool:
    """Check for required environment variables."""
    required_vars = [
        "LITELLM_API_BASE",
        "LITELLM_MASTER_KEY",
        "PREDICT_URL",
        "PREDICT_TIMEOUT",
    ]
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    if missing_vars:
        print(f"Missing environment variables: {', '.join(missing_vars)}")
        print("Make sure .env file contains all required variables.")
        print("For HTTP API integration required:")
        print("  - PREDICT_URL (predictor service URL)")
        print("  - PREDICT_TIMEOUT (HTTP request timeout)")
        return False
    return True


def main():
    """Main test execution function."""
    print("Advanced Custom Router Test Suite with HTTP API")
    print("Demonstrating ML-based routing via HTTP API with pattern analysis\n")
    if not check_environment_variables():
        sys.exit(1)
    api_base = os.getenv("LITELLM_API_BASE", "http://localhost:4000")
    api_key = os.getenv("LITELLM_MASTER_KEY", "sk-1234")
    predict_url = os.getenv("PREDICT_URL", "http://localhost:8008")
    print(f"LiteLLM Router: {api_base}")
    print(f"Predictor Service: {predict_url}")
    print(f"API key: {api_key[:8]}...")
    print("\nChecking predictor service...")
    predictor_available = check_predictor_service()
    if not predictor_available:
        print("Predictor service unavailable.")
        print(
            "Router can work with fallback strategy, but ML predictions will be unavailable."
        )
        print("For full functionality start predictor service.")
        print("Continuing test with fallback mode...\n")
    auth = LiteLLMAuth(api_base=api_base, api_key=api_key)
    print("\nChecking authentication...")
    if not auth.test_auth():
        print("Authentication error. Check API settings.")
        sys.exit(1)
    print("Authentication successful")
    print("\nChecking service health...")
    try:
        health_data = auth.send_request("GET", "/health")
        if health_data is None:
            print("Could not get service health information")
            sys.exit(1)
        healthy_count = len(health_data.get("healthy_endpoints", []))
        if healthy_count == 0:
            print("No available endpoints. Check configuration.")
            sys.exit(1)
        print(f"Service available. Healthy endpoints: {healthy_count}")
        if "healthy_endpoints" in health_data:
            print(f"Available endpoints:")
            for endpoint in health_data["healthy_endpoints"]:
                print(f"   • {endpoint}")
    except Exception as e:
        print(f"Service check error: {e}")
        sys.exit(1)
    print(f"\nStarting tests...")
    analysis = test_routing_intelligence()
    if analysis and analysis.get("successful_requests", 0) > 0:
        print(f"\nTesting completed successfully!")
        if predictor_available:
            print(f"Custom Router with HTTP API demonstrated intelligent routing!")
        else:
            print(f"Custom Router works in fallback mode!")
        print(f"Processed {analysis['successful_requests']} successful requests")
        print(f"Average response time: {analysis['avg_response_time']:.2f}s")
        if predictor_available:
            print(f"ML predictions were used for optimal routing")
        else:
            print(f"Fallback routing used (without ML predictions)")
    else:
        print(f"\nTesting completed with errors.")
    return 0


if __name__ == "__main__":
    sys.exit(main())
