#!/usr/bin/env python3
"""
External Predictor Service Tests.

Test external predictor service functionality with real HTTP requests.
Requires a running predictor service to pass.
"""
from pathlib import Path
import logging
import os
import sys

from llm_router.logging_utils import get_logger
import pytest
import requests


sys.path.append(str(Path(__file__).parent.parent))


def load_env_variables():
    """Load environment variables from .env file if present."""
    env_file = Path(__file__).parent.parent / ".env"
    if env_file.exists():
        with open(env_file) as f:
            for line in f:
                if line.strip() and (not line.startswith("#")):
                    key, value = line.strip().split("=", 1)
                    os.environ[key] = value


load_env_variables()
logger = get_logger(__name__)


class TestPredictorService:
    """Test cases for external predictor service."""

    @pytest.fixture(autouse=True)
    def setup(self):
        """Set up test environment."""
        is_docker = os.path.exists("/.dockerenv") or os.getcwd().startswith("/app")
        if is_docker:
            env_url = os.getenv("PREDICT_URL", "")
            if "localhost" in env_url:
                self.base_url = env_url.replace("localhost", "host.docker.internal")
                logger.info(
                    f"DOCKER: Replaced localhost with host.docker.internal: {env_url} → {self.base_url}"
                )
            else:
                self.base_url = env_url or "http://host.docker.internal:8008"
                logger.info(
                    "DOCKER: Using environment URL or default host.docker.internal"
                )
        else:
            self.base_url = os.getenv("PREDICT_URL", "http://localhost:8008")
            logger.info("LOCAL: Using localhost for local environment")
        self.timeout = 30
        logger.info(f"TEST: Testing predictor service at: {self.base_url}")

    def test_health_check(self):
        """Test predictor service health by making a prediction request."""
        test_data = {
            "text_input": "test",
            "hardware": "1xH100",
            "model_name": "test-model",
            "concurrent_requests": 1,
            "model_instances": 1,
        }
        response = requests.post(
            f"{self.base_url}/predict/code", json=test_data, timeout=self.timeout
        )
        assert response.status_code in [200, 201]
        data = response.json()
        assert "predicted_time" in data
        assert isinstance(data["predicted_time"], (int, float))
        logger.info("TEST: Health check passed")

    def test_service_info(self):
        """Test predictor service info by testing both endpoints."""
        # Test code endpoint
        code_data = {
            "text_input": "test code",
            "hardware": "1xH100",
            "model_name": "test-model",
            "concurrent_requests": 1,
            "model_instances": 1,
        }
        code_response = requests.post(
            f"{self.base_url}/predict/code", json=code_data, timeout=self.timeout
        )
        assert code_response.status_code in [200, 201]

        # Test chat endpoint
        chat_data = {
            "text_input": "test chat",
            "hardware": "1xH100",
            "model_name": "test-model",
            "concurrent_requests": 1,
            "model_instances": 1,
        }
        chat_response = requests.post(
            f"{self.base_url}/predict/chat", json=chat_data, timeout=self.timeout
        )
        assert chat_response.status_code in [200, 201]

        logger.info("TEST: Service info - both code and chat endpoints are working")

    def test_predict_endpoint(self):
        """Test prediction endpoint with valid request."""
        data = {
            "text_input": "Hello, this is a test prompt for chat completion.",
            "hardware": "gpu_a100",
            "model_name": "chat_instruct",
            "concurrent_requests": 1,
            "model_instances": 1,
        }
        response = requests.post(
            f"{self.base_url}/predict/chat", json=data, timeout=self.timeout
        )
        assert response.status_code in [200, 201]
        response_data = response.json()
        required_fields = ["predicted_time", "task_type"]
        for field in required_fields:
            assert field in response_data
        assert isinstance(response_data["predicted_time"], (int, float))
        assert response_data["predicted_time"] > 0
        assert response_data["task_type"] == "chat"
        logger.info(
            f"TEST: Chat prediction successful - {response_data['predicted_time']:.3f}s"
        )

    @pytest.mark.parametrize(
        "prompt,expected_length",
        [
            ("Hi", 2),
            ("Hello, how are you today?", 25),
            (
                "Write a comprehensive guide about machine learning algorithms and their applications in modern data science.",
                120,
            ),
            (
                "Create a detailed explanation of how neural networks work, including backpropagation, different types of layers, activation functions, optimization algorithms, and provide practical examples of implementation in various industries and sectors.",
                243,
            ),
        ],
    )
    def test_predict_with_different_prompt_lengths(self, prompt, expected_length):
        """Test prediction with prompts of different lengths."""
        data = {
            "text_input": prompt,
            "hardware": "gpu_a100",
            "model_name": "chat_instruct",
            "concurrent_requests": 1,
            "model_instances": 1,
        }
        response = requests.post(
            f"{self.base_url}/predict/chat", json=data, timeout=self.timeout
        )
        assert response.status_code in [200, 201]
        response_data = response.json()
        assert response_data["predicted_time"] > 0
        logger.info(
            f"TEST: Prompt length {len(prompt)} -> {response_data['predicted_time']:.3f}s"
        )

    def test_predict_invalid_task_type(self):
        """Test prediction with invalid task type."""
        params = {
            "prompt": "Test prompt",
            "hardware": "gpu_a100",
            "model": "chat_instruct",
            "instances": 1,
            "requests": 1,
        }
        response = requests.get(
            f"{self.base_url}/predict/invalid_task", params=params, timeout=self.timeout
        )
        assert response.status_code == 400

    def test_predict_missing_fields(self):
        """Test prediction with missing required fields."""
        test_cases = [
            {
                "name": "missing_prompt",
                "params": {"hardware": "gpu_a100", "model": "chat_instruct"},
            },
            {
                "name": "missing_hardware",
                "params": {"prompt": "Test prompt", "model": "chat_instruct"},
            },
            {
                "name": "missing_model",
                "params": {"prompt": "Test prompt", "hardware": "gpu_a100"},
            },
        ]
        for case in test_cases:
            response = requests.get(
                f"{self.base_url}/predict/chat",
                params=case["params"],
                timeout=self.timeout,
            )
            assert response.status_code == 400, f"Expected 400 for {case['name']}"
            logger.info(f"TEST: Validation test passed for {case['name']}")

    def test_predict_consistency(self):
        """Test prediction consistency with same inputs."""
        params = {
            "prompt": "This is a consistent test prompt for prediction validation.",
            "hardware": "gpu_v100",
            "model": "chat_instruct",
            "instances": 1,
            "requests": 1,
        }
        predictions = []
        for i in range(3):
            response = requests.get(
                f"{self.base_url}/predict/chat", params=params, timeout=self.timeout
            )
            assert response.status_code == 200
            data = response.json()
            predictions.append(data["predicted_time_seconds"])
        avg_prediction = sum(predictions) / len(predictions)
        for prediction in predictions:
            variance = abs(prediction - avg_prediction) / avg_prediction
            assert variance < 0.1, f"Prediction variance too high: {variance:.2%}"
        logger.info(
            f"TEST: Consistency check passed - avg: {avg_prediction:.3f}s, variance: <10%"
        )


def main():
    """Main test runner."""
    logger.info("TEST: Starting external predictor service tests")
    try:
        pytest.main([__file__, "-v", "--tb=short"])
    except SystemExit as e:
        if e.code == 0:
            logger.info("TEST: All tests passed!")
        else:
            logger.error("TEST: Some tests failed!")
        return e.code
    except Exception as e:
        logger.error(f"TEST: Test execution error: {e}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
