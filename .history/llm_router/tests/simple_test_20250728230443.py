#!/usr/bin/env python3
"""
Simple Custom Router Test Suite.

This module provides basic router functionality testing using only Python standard
library and authentication module without external dependencies.
"""
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
import json
import logging
import os
import sys
import time

from dotenv import load_dotenv
from llm_router.logging_utils import get_logger
from llm_router.router_litellm.litellm_auth import LiteLLMAuth


load_dotenv()
sys.path.append(str(Path(__file__).parent.parent))
logger = get_logger(__name__)


class RouterTestResult:
    """Container for test results."""

    def __init__(
        self,
        success: bool,
        response_time: float = 0.0,
        model_used: str = "unknown",
        content: str = "",
        usage: Optional[Dict[str, Any]] = None,
        error: str = "",
    ):
        self.success = success
        self.response_time = response_time
        self.model_used = model_used
        self.content = content
        self.usage = usage or {}
        self.error = error


class TestCase:
    """Container for test case configuration."""

    def __init__(
        self, name: str, model: str, content: str, expected_endpoint_type: str
    ):
        self.name = name
        self.model = model
        self.content = content
        self.expected_endpoint_type = expected_endpoint_type


def make_request(model: str, content: str, max_tokens: int = 100) -> RouterTestResult:
    """Send request to LiteLLM API.

    Args:
        model: The model name to use for the request.
        content: The message content to send.
        max_tokens: Maximum tokens to generate in response.

    Returns:
        RouterTestResult containing the response data and metadata.
    """
    api_base = os.getenv("LITELLM_API_BASE", "http://localhost:4000")
    api_key = os.getenv("LITELLM_MASTER_KEY", "sk-1234")
    auth = LiteLLMAuth(api_base=api_base, api_key=api_key)
    payload = {
        "model": model,
        "messages": [{"role": "user", "content": content}],
        "max_tokens": max_tokens,
    }
    try:
        start_time = time.time()
        response_data = auth.send_request("POST", "/v1/chat/completions", payload)
        end_time = time.time()
        if response_data is None:
            return RouterTestResult(
                success=False, error="Failed to get response from API"
            )
        message_content = ""
        if (
            "choices" in response_data
            and len(response_data["choices"]) > 0
            and ("message" in response_data["choices"][0])
            and ("content" in response_data["choices"][0]["message"])
        ):
            message_content = response_data["choices"][0]["message"]["content"]
        return RouterTestResult(
            success=True,
            response_time=end_time - start_time,
            model_used=response_data.get("model", "unknown"),
            content=message_content,
            usage=response_data.get("usage", {}),
        )
    except Exception as e:
        logger.error(f"TEST: Request failed for model {model}: {e}")
        return RouterTestResult(success=False, error=str(e))


def test_routing() -> bool:
    """Test routing for different types of requests.

    Returns:
        True if all tests pass, False otherwise.
    """
    logger.info("TEST: Starting Custom Router testing")
    print("Testing Custom Router")
    print("=" * 50)
    test_cases = [
        TestCase(
            name="Chat request (general)",
            model="chat_instruct",
            content="Tell me an interesting fact about space",
            expected_endpoint_type="instruct",
        ),
        TestCase(
            name="Code request",
            model="chat_coder",
            content="Write a Python function to calculate fibonacci numbers. Provide only code, no explanations.",
            expected_endpoint_type="coder",
        ),
    ]
    results: List[Dict[str, Any]] = []
    for i, test_case in enumerate(test_cases, 1):
        logger.info(f"TEST: Running test case {i}: {test_case.name}")
        print(f"\n{i}. {test_case.name}")
        print(f"   Model: {test_case.model}")
        print(f"   Request: {test_case.content}")
        result = make_request(test_case.model, test_case.content)
        if result.success:
            logger.info(f"TEST: Test case {i} successful: {result.response_time:.2f}s")
            print(f"   SUCCESS")
            print(f"   Response time: {result.response_time:.2f}s")
            print(f"   Model used: {result.model_used}")
            print(f"   Tokens: {result.usage.get('total_tokens', 'N/A')}")
            print(f"   Full Response:")
            print(f"   {'-' * 40}")
            print(f"{result.content}")
            print(f"   {'-' * 40}")
        else:
            logger.error(f"TEST: Test case {i} failed: {result.error}")
            print(f"   ERROR: {result.error}")
        results.append(
            {
                "test_case": test_case.name,
                "model": test_case.model,
                "success": result.success,
                "response_time": result.response_time,
                "model_used": result.model_used,
                "error": result.error,
            }
        )
        time.sleep(1)
    print("\n" + "=" * 50)
    print("Results Summary:")
    successful = sum((1 for r in results if r["success"]))
    total = len(results)
    avg_time = sum((r["response_time"] for r in results if r["success"])) / max(
        successful, 1
    )
    print(f"   Successful requests: {successful}/{total}")
    print(f"   Average response time: {avg_time:.2f}s")
    if successful == total:
        logger.info("TEST: All tests completed successfully")
        print("   All tests passed successfully!")
        return True
    else:
        logger.warning(f"TEST: Some tests failed: {total - successful} out of {total}")
        print("   Some tests failed")
        return False


def check_health() -> bool:
    """Check health endpoint status.

    Returns:
        True if service is healthy, False otherwise.
    """
    logger.info("TEST: Checking service health")
    print("Checking service health...")
    api_base = os.getenv("LITELLM_API_BASE", "http://localhost:4000")
    api_key = os.getenv("LITELLM_MASTER_KEY", "sk-1234")
    auth = LiteLLMAuth(api_base=api_base, api_key=api_key)
    try:
        health_data = auth.send_request("GET", "/health")
        if health_data is None:
            logger.error("TEST: Failed to get health information from service")
            print("   Failed to get service health information")
            return False
        healthy_count = len(health_data.get("healthy_endpoints", []))
        unhealthy_count = len(health_data.get("unhealthy_endpoints", []))
        logger.info(
            f"TEST: Health check results: {healthy_count} healthy, {unhealthy_count} unhealthy"
        )
        print(f"   Healthy endpoints: {healthy_count}")
        print(f"   Unhealthy endpoints: {unhealthy_count}")
        if healthy_count > 0:
            print("   Service is ready!")
            return True
        else:
            logger.warning("TEST: No healthy endpoints available")
            print("   No available endpoints!")
            return False
    except Exception as e:
        logger.error(f"TEST: Health check failed: {e}")
        print(f"   Health check error: {e}")
        return False


def check_predictor_service() -> bool:
    """Check predictor service availability.

    Returns:
        True if predictor service is available, False otherwise.
    """
    logger.info("TEST: Checking predictor service availability")
    print("Checking predictor service...")
    predict_url = os.getenv("PREDICT_URL", "http://localhost:8008")
    try:
        import requests

        # Test with a simple prediction request instead of /health
        test_data = {
            "text_input": "test",
            "hardware": "1xH100",
            "model_name": "test-model",
            "concurrent_requests": 1,
            "model_instances": 1,
        }
        response = requests.post(
            f"{predict_url}/predict/code", json=test_data, timeout=5
        )
        if response.status_code in [200, 201]:
            response_data = response.json()
            logger.info(f"TEST: Predictor service is available at {predict_url}")
            print(f"   Predictor service is available")
            print(
                f"   Response time: {response_data.get('predicted_time', 'unknown')}s"
            )
            print(f"   Task type: {response_data.get('task_type', 'unknown')}")
            print(f"   Device: {response_data.get('device', 'unknown')}")
            return True
        else:
            logger.warning(
                f"TEST: Predictor service unavailable: HTTP {response.status_code}"
            )
            print(f"   Predictor service unavailable (HTTP {response.status_code})")
            return False
    except Exception as e:
        if "ConnectionError" in str(type(e)):
            logger.warning(
                f"TEST: Cannot connect to predictor service at {predict_url}"
            )
            print(f"   Cannot connect to predictor service at {predict_url}")
            print("   Make sure predictor service is running at the specified URL")
        else:
            logger.error(f"TEST: Predictor service check failed: {e}")
            print(f"   Predictor service check error: {e}")
        return False


def check_environment_variables() -> bool:
    """Check for required environment variables.

    Returns:
        True if all required variables are present, False otherwise.
    """
    required_vars = [
        "LITELLM_API_BASE",
        "LITELLM_MASTER_KEY",
        "PREDICT_URL",
        "PREDICT_TIMEOUT",
    ]
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    if missing_vars:
        logger.error(f"TEST: Missing environment variables: {missing_vars}")
        print(f"Missing environment variables: {', '.join(missing_vars)}")
        print("Make sure the .env file contains all required variables.")
        print("For HTTP API integration, the following are required:")
        print("  - PREDICT_URL (predictor service URL)")
        print("  - PREDICT_TIMEOUT (HTTP request timeout)")
        return False
    logger.info("TEST: All required environment variables are present")
    return True


def test_chat_instruct_model() -> bool:
    """Test chat_instruct model with various general conversation scenarios.

    Returns:
        True if all chat tests pass, False otherwise.
    """
    logger.info("TEST: Testing chat_instruct model functionality")
    print("Testing chat_instruct model (General Chat)")
    print("-" * 40)
    chat_test_cases = [
        TestCase(
            name="General knowledge question",
            model="chat_instruct",
            content="What is the capital of France and what is it famous for?",
            expected_endpoint_type="instruct",
        ),
        TestCase(
            name="Creative writing request",
            model="chat_instruct",
            content="Write a short story about a robot discovering emotions for the first time.",
            expected_endpoint_type="instruct",
        ),
        TestCase(
            name="Explanation request",
            model="chat_instruct",
            content="Explain the concept of photosynthesis in simple terms that a child could understand.",
            expected_endpoint_type="instruct",
        ),
        TestCase(
            name="Problem solving",
            model="chat_instruct",
            content="I'm planning a birthday party for 20 people. What are some essential things I should consider?",
            expected_endpoint_type="instruct",
        ),
        TestCase(
            name="Historical question",
            model="chat_instruct",
            content="Tell me about the significance of the Renaissance period in European history.",
            expected_endpoint_type="instruct",
        ),
    ]
    results = []
    for i, test_case in enumerate(chat_test_cases, 1):
        logger.info(f"TEST: Running chat test {i}: {test_case.name}")
        print(f"\n{i}. {test_case.name}")
        print(f"   Model: {test_case.model}")
        print(f"   Request: {test_case.content}")
        result = make_request(test_case.model, test_case.content, max_tokens=150)
        if result.success:
            logger.info(f"TEST: Chat test {i} successful: {result.response_time:.2f}s")
            print(f"   SUCCESS")
            print(f"   Response time: {result.response_time:.2f}s")
            print(f"   Model used: {result.model_used}")
            print(f"   Full Response:")
            print(f"   {'-' * 40}")
            print(f"{result.content}")
            print(f"   {'-' * 40}")
        else:
            logger.error(f"TEST: Chat test {i} failed: {result.error}")
            print(f"   ERROR: {result.error}")
        results.append(result.success)
        time.sleep(1)
    successful = sum(results)
    total = len(results)
    print(f"\nChat Instruct Results: {successful}/{total} tests passed")
    if successful == total:
        logger.info("TEST: All chat_instruct tests completed successfully")
        return True
    else:
        logger.warning(
            f"TEST: Some chat_instruct tests failed: {total - successful} out of {total}"
        )
        return False


def test_chat_coder_model() -> bool:
    """Test chat_coder model with various programming tasks.

    Returns:
        True if all coding tests pass, False otherwise.
    """
    logger.info("TEST: Testing chat_coder model functionality")
    print("\nTesting chat_coder model (Programming Tasks)")
    print("-" * 40)
    coding_test_cases = [
        TestCase(
            name="Function implementation",
            model="chat_coder",
            content="Write a Python function called 'fibonacci' that takes a number n and returns the nth Fibonacci number. Code only, no explanations.",
            expected_endpoint_type="coder",
        ),
        TestCase(
            name="Algorithm implementation",
            model="chat_coder",
            content="Implement a binary search algorithm in Python. Code only, no comments or explanations.",
            expected_endpoint_type="coder",
        ),
        TestCase(
            name="Code completion task",
            model="chat_coder",
            content="Complete this Python function:\n\ndef calculate_factorial(n):\n    # TODO: implement factorial calculation\n    pass\n\nProvide only the complete function code, no explanations.",
            expected_endpoint_type="coder",
        ),
        TestCase(
            name="Data structure implementation",
            model="chat_coder",
            content="Create a Python class for a simple stack data structure with push, pop, and peek methods. Code only, no explanations.",
            expected_endpoint_type="coder",
        ),
        TestCase(
            name="Code debugging",
            model="chat_coder",
            content="Fix the bug in this Python code:\n\ndef sum_list(numbers):\n    total = 0\n    for i in range(len(numbers)):\n        total += numbers[i+1]\n    return total\n\nProvide only the corrected function code.",
            expected_endpoint_type="coder",
        ),
        TestCase(
            name="API integration example",
            model="chat_coder",
            content="Write a Python function that makes an HTTP GET request using the requests library and handles potential errors. Code only, no explanations.",
            expected_endpoint_type="coder",
        ),
        TestCase(
            name="Database query",
            model="chat_coder",
            content="Write a SQL query to find the top 5 customers by total order amount from tables 'customers' and 'orders'. Code only, no explanations.",
            expected_endpoint_type="coder",
        ),
    ]
    results = []
    for i, test_case in enumerate(coding_test_cases, 1):
        logger.info(f"TEST: Running coding test {i}: {test_case.name}")
        print(f"\n{i}. {test_case.name}")
        print(f"   Model: {test_case.model}")
        print(f"   Request: {test_case.content}")
        result = make_request(test_case.model, test_case.content, max_tokens=200)
        if result.success:
            logger.info(
                f"TEST: Coding test {i} successful: {result.response_time:.2f}s"
            )
            print(f"   SUCCESS")
            print(f"   Response time: {result.response_time:.2f}s")
            print(f"   Model used: {result.model_used}")
            print(f"   Full Code Response:")
            print(f"   {'-' * 40}")
            print(f"{result.content}")
            print(f"   {'-' * 40}")
        else:
            logger.error(f"TEST: Coding test {i} failed: {result.error}")
            print(f"   ERROR: {result.error}")
        results.append(result.success)
        time.sleep(1)
    successful = sum(results)
    total = len(results)
    print(f"\nChat Coder Results: {successful}/{total} tests passed")
    if successful == total:
        logger.info("TEST: All chat_coder tests completed successfully")
        return True
    else:
        logger.warning(
            f"TEST: Some chat_coder tests failed: {total - successful} out of {total}"
        )
        return False


def test_model_routing_accuracy() -> bool:
    """Test if requests are properly routed to the correct models based on content type.

    Returns:
        True if routing is working correctly, False otherwise.
    """
    logger.info("TEST: Testing model routing accuracy")
    print("\nTesting Model Routing Accuracy")
    print("-" * 40)
    routing_test_cases = [
        TestCase(
            name="General question should use chat_instruct",
            model="chat_instruct",
            content="What are the benefits of renewable energy?",
            expected_endpoint_type="instruct",
        ),
        TestCase(
            name="Programming task should use chat_coder",
            model="chat_coder",
            content="def reverse_string(s):\n    # Complete this function to reverse a string\n\nCode only, no explanations.",
            expected_endpoint_type="coder",
        ),
        TestCase(
            name="Code review should use chat_coder",
            model="chat_coder",
            content="Fix this Python code:\n\nfor i in range(len(arr)):\n    print(arr[i])\n\nProvide only the improved code.",
            expected_endpoint_type="coder",
        ),
        TestCase(
            name="Conversational query should use chat_instruct",
            model="chat_instruct",
            content="Can you help me plan a healthy meal for dinner tonight?",
            expected_endpoint_type="instruct",
        ),
    ]
    results = []
    routing_correct = 0
    for i, test_case in enumerate(routing_test_cases, 1):
        logger.info(f"TEST: Running routing test {i}: {test_case.name}")
        print(f"\n{i}. {test_case.name}")
        print(f"   Expected model: {test_case.model}")
        print(f"   Request type: {test_case.expected_endpoint_type}")
        print(f"   Request: {test_case.content}")
        result = make_request(test_case.model, test_case.content)
        if result.success:
            print(f"   SUCCESS - Request processed successfully")
            print(f"   Requested model: {test_case.model}")
            print(f"   Actual model used: {result.model_used}")
            print(f"   Response time: {result.response_time:.2f}s")
            print(f"   Full Response:")
            print(f"   {'-' * 40}")
            print(f"{result.content}")
            print(f"   {'-' * 40}")
            results.append(result.success)
            routing_correct += 1
        else:
            logger.error(f"TEST: Routing test {i} failed: {result.error}")
            print(f"   ERROR: {result.error}")
            results.append(False)
        time.sleep(1)
    successful = sum(results)
    total = len(results)
    print(f"\nRouting Test Results:")
    print(f"   Successful requests: {successful}/{total}")
    print(f"   All requests processed correctly: {routing_correct}/{total}")
    if successful == total:
        logger.info("TEST: All routing tests completed successfully")
        print(f"   All routing requests were processed successfully!")
        return True
    else:
        logger.warning(
            f"TEST: Some routing requests failed: {total - successful} failed requests"
        )
        print(f"   Some routing requests failed - check service configuration")
        return False


def main() -> None:
    """Main function to run the test suite."""
    import argparse

    parser = argparse.ArgumentParser(
        description="Custom Router Test Suite with HTTP API"
    )
    parser.add_argument(
        "--test-type",
        choices=["all", "basic", "chat", "coder", "routing"],
        default="all",
        help="Type of tests to run",
    )
    parser.add_argument(
        "--skip-env-check",
        action="store_true",
        help="Skip environment variable validation",
    )
    args = parser.parse_args()
    print("Custom Router Test Suite with HTTP API")
    print("Testing ML-based routing functionality via HTTP API\n")
    if not args.skip_env_check and (not check_environment_variables()):
        logger.error("TEST: Environment validation failed")
        sys.exit(1)
    api_base = os.getenv("LITELLM_API_BASE", "http://localhost:4000")
    api_key = os.getenv("LITELLM_MASTER_KEY", "sk-1234")
    predict_url = os.getenv("PREDICT_URL", "http://localhost:8008")
    logger.info(f"TEST: Configuration: LiteLLM={api_base}, Predictor={predict_url}")
    print(f"LiteLLM Router: {api_base}")
    print(f"Predictor Service: {predict_url}")
    print(f"API Key: {api_key[:8]}...\n")
    print(f"Test Type: {args.test_type}")
    print("=" * 60)
    if not args.skip_env_check:
        if not check_predictor_service():
            logger.warning("TEST: Predictor service is unavailable")
            print("Predictor service is unavailable.")
            print(
                "Router can work with fallback strategy, but ML predictions will be unavailable."
            )
            print("   For full functionality, start the predictor service.\n")
    if not check_health():
        logger.error("TEST: LiteLLM service is unavailable")
        print(
            "LiteLLM service is unavailable. Make sure docker-compose up -d is executed."
        )
        sys.exit(1)
    test_results = []
    if args.test_type in ["all", "basic"]:
        print("\n" + "=" * 60)
        print("BASIC ROUTING TESTS")
        print("=" * 60)
        basic_result = test_routing()
        test_results.append(("Basic Routing", basic_result))
    if args.test_type in ["all", "chat"]:
        print("\n" + "=" * 60)
        print("CHAT INSTRUCT MODEL TESTS")
        print("=" * 60)
        chat_result = test_chat_instruct_model()
        test_results.append(("Chat Instruct", chat_result))
    if args.test_type in ["all", "coder"]:
        print("\n" + "=" * 60)
        print("CHAT CODER MODEL TESTS")
        print("=" * 60)
        coder_result = test_chat_coder_model()
        test_results.append(("Chat Coder", coder_result))
    if args.test_type in ["all", "routing"]:
        print("\n" + "=" * 60)
        print("MODEL ROUTING ACCURACY TESTS")
        print("=" * 60)
        routing_result = test_model_routing_accuracy()
        test_results.append(("Model Routing", routing_result))
    print("\n" + "=" * 60)
    print("OVERALL TEST RESULTS SUMMARY")
    print("=" * 60)
    total_test_suites = len(test_results)
    passed_test_suites = sum((1 for _, result in test_results if result))
    for test_name, result in test_results:
        status = "PASSED" if result else "FAILED"
        print(f"   {test_name}: {status}")
    print(f"\nTest Suites Summary: {passed_test_suites}/{total_test_suites} passed")
    if passed_test_suites == total_test_suites:
        logger.info("TEST: All test suites completed successfully")
        print("\nAll test suites completed successfully!")
        print("Custom Router with HTTP API is working correctly!")
        sys.exit(0)
    else:
        logger.error(
            f"TEST: Some test suites failed: {total_test_suites - passed_test_suites} out of {total_test_suites}"
        )
        print(
            f"\nSome test suites failed: {total_test_suites - passed_test_suites} out of {total_test_suites}"
        )
        print("\nRecommendations:")
        for test_name, result in test_results:
            if not result:
                if "Chat Instruct" in test_name:
                    print(
                        f"  - Check chat_instruct model configuration and availability"
                    )
                elif "Chat Coder" in test_name:
                    print(f"  - Check chat_coder model configuration and availability")
                elif "Routing" in test_name:
                    print(
                        f"  - Verify router logic is correctly routing requests to appropriate models"
                    )
                elif "Basic" in test_name:
                    print(
                        f"  - Check basic router functionality and model configurations"
                    )
        sys.exit(1)


if __name__ == "__main__":
    main()
