#!/usr/bin/env python3
"""
Запуск LiteLLM с кастомным роутером через импорт патча перед стартом litellm.

Этот скрипт предоставляет удобную точку входа для запуска LiteLLM с автоматически
включенной кастомной стратегией маршрутизации. Он обрабатывает правильный порядок
импортов и настройку конфигурации, необходимые для бесшовной интеграции.

КРИТИЧЕСКИ ВАЖНЫЕ ИМПОРТЫ:
- litellm: основная библиотека, без которой система не функционирует
- run_server: функция запуска LiteLLM proxy сервера
- litellm_patch: патч для внедрения кастомной стратегии в LiteLLM Router
- LLMTimePredictorRoutingStrategy: кастомная стратегия маршрутизации

Эти импорты выполняются на глобальном уровне и обязательны для работы системы.
"""

import sys

# Добавляем /app в PYTHONPATH для импорта llm_router
sys.path.insert(0, "/app")

from llm_router import litellm_patch
from llm_router.logging_utils import get_logger
from llm_router.router.custom_router import LLMTimePredictorRoutingStrategy
import litellm

logger = get_logger(__name__)


def main() -> None:
    """
    Главная точка входа для запуска LiteLLM с кастомной маршрутизацией.

    Последовательность запуска:
    1. Валидация окружения и проверка импортированных зависимостей
    2. Применение litellm_patch для активации кастомной маршрутизации
    3. Запуск сервера LiteLLM

    Raises:
        RuntimeError: Если запуск сервера завершается неудачей
    """
    logger.info("INIT: запуск LiteLLM с кастомным роутером")

    # Валидация зависимостей
    logger.debug("INIT: валидация окружения и проверка импортированных зависимостей")
    litellm_version = getattr(litellm, "__version__", "неизвестна")
    logger.info(f"INIT: LiteLLM версия {litellm_version} успешно импортирован")
    logger.info(
        f"INIT: LLMTimePredictorRoutingStrategy доступен: {LLMTimePredictorRoutingStrategy.__name__}"
    )
    logger.info(f"INIT: litellm_patch доступен: {litellm_patch.__name__}")
    logger.info("INIT: все обязательные зависимости успешно импортированы и проверены")

    # Применение патча
    logger.info("INIT: применение патча LiteLLM для кастомной маршрутизации")
    try:
        result = litellm_patch.apply_patch()
        if result:
            logger.info("INIT: патч LiteLLM успешно применен")
        else:
            logger.warning("INIT: патч LiteLLM уже был применен ранее")
        logger.debug("INIT: кастомная стратегия маршрутизации активирована")
    except Exception as e:
        logger.error(f"INIT: не удалось применить патч LiteLLM: {e}")
        raise RuntimeError(
            "Критическая ошибка: патч LiteLLM не может быть применен"
        ) from e

    # Импорт run_server ПОСЛЕ применения патча
    logger.info("INIT: импорт run_server после применения патча")
    try:
        from litellm.proxy.proxy_cli import run_server

        logger.info("INIT: run_server успешно импортирован")
    except Exception as e:
        logger.error(f"INIT: ошибка импорта run_server: {e}")
        raise RuntimeError("Не удалось импортировать run_server") from e

    # Запуск сервера
    logger.info("INIT: запуск сервера LiteLLM с кастомной маршрутизацией")
    try:
        logger.info("INIT: передача управления серверу LiteLLM")
        run_server()
    except Exception as e:
        logger.error(f"INIT: критическая ошибка запуска сервера: {e}")
        raise RuntimeError("Не удалось запустить сервер LiteLLM") from e


if __name__ == "__main__":
    main()
