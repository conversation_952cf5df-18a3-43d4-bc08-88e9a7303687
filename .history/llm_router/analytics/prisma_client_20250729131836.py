#!/usr/bin/env python3
"""
Analytics Prisma Client.

Provides Prisma client for LLM-Router analytics operations.
Compatible with LiteLLM database schema and operations.
"""
from pathlib import Path
from typing import Optional, Any, List, Dict
import os
import sys

from llm_router.logging_utils import get_logger


logger = get_logger(__name__)


class AnalyticsPrismaClientError(Exception):
    """Raised when analytics Prisma client operations fail."""

    pass


class AnalyticsPrismaClient:
    """Prisma client for analytics operations.

    Provides direct access to Prisma client for executing raw SQL queries
    and database operations needed for analytics reporting.

    Attributes:
        db: Prisma client instance for database operations
        _connected: Whether client is connected to database
    """

    def __init__(self, database_url: Optional[str] = None, litellm_prisma_client=None):
        """Initialize analytics Prisma client.

        Args:
            database_url: Database URL. If None, will try to get from environment.
            litellm_prisma_client: LiteLLM PrismaClient instance. If provided, will use its .db attribute.
        """
        self.db: Optional[Any] = None
        self._connected: bool = False
        self.database_url = database_url or os.getenv("DATABASE_URL")

        # If LiteLLM PrismaClient is provided, use its .db attribute directly
        if litellm_prisma_client is not None:
            if hasattr(litellm_prisma_client, "db"):
                self.db = litellm_prisma_client.db
                self._connected = True  # LiteLLM client is already connected
                logger.info("PRISMA_CLIENT: Using LiteLLM Prisma client")
                return
            else:
                logger.warning(
                    "PRISMA_CLIENT: Provided litellm_prisma_client has no .db attribute"
                )

        if not self.database_url:
            logger.warning(
                "PRISMA_CLIENT: No DATABASE_URL provided - client will be in mock mode"
            )
            return
        try:
            from prisma import Prisma

            self.db = Prisma()
            logger.info("PRISMA_CLIENT: Analytics Prisma client initialized")
        except ImportError as e:
            logger.error(f"PRISMA_CLIENT: Failed to import Prisma client: {e}")
            logger.warning("PRISMA_CLIENT: Client will be in mock mode")
            self.db = None
        except Exception as e:
            logger.error(f"PRISMA_CLIENT: Failed to initialize Prisma client: {e}")
            raise AnalyticsPrismaClientError(f"Failed to initialize Prisma client: {e}")

    async def connect(self) -> None:
        """Connect to database.

        Raises:
            AnalyticsPrismaClientError: When connection fails.
        """
        if not self.db:
            logger.warning("PRISMA_CLIENT: No Prisma client available - mock mode")
            return
        if self._connected:
            logger.debug("PRISMA_CLIENT: Already connected")
            return
        try:
            await self.db.connect()
            self._connected = True
            logger.info("PRISMA_CLIENT: Successfully connected to database")
        except Exception as e:
            logger.error(f"PRISMA_CLIENT: Failed to connect to database: {e}")
            raise AnalyticsPrismaClientError(f"Failed to connect to database: {e}")

    async def disconnect(self) -> None:
        """Disconnect from database."""
        if not self.db:
            logger.debug("PRISMA_CLIENT: No Prisma client to disconnect")
            return
        if not self._connected:
            logger.debug("PRISMA_CLIENT: Already disconnected")
            return
        try:
            await self.db.disconnect()
            self._connected = False
            logger.info("PRISMA_CLIENT: Successfully disconnected from database")
        except Exception as e:
            logger.error(f"PRISMA_CLIENT: Error disconnecting from database: {e}")

    async def query_raw(self, query: str) -> List[Dict[str, Any]]:
        """Execute raw SQL query.

        Args:
            query: SQL query string

        Returns:
            List of result dictionaries

        Raises:
            AnalyticsPrismaClientError: When query execution fails
        """
        if not self.db:
            logger.debug("PRISMA_CLIENT: Mock mode - returning empty results")
            return []
        if not self._connected:
            await self.connect()
        try:
            # Check if this is LiteLLM PrismaClient (has .db attribute) or regular Prisma client
            if hasattr(self.db, "db"):
                # LiteLLM PrismaClient - use .db.query_raw()
                result = await self.db.db.query_raw(query)
            else:
                # Regular Prisma client - use .query_raw() directly
                result = await self.db.query_raw(query)
            logger.debug(
                f"PRISMA_CLIENT: Query executed successfully, returned {(len(result) if result else 0)} rows"
            )
            return result if result else []
        except Exception as e:
            logger.error(f"PRISMA_CLIENT: Query execution failed: {e}")
            logger.debug(f"PRISMA_CLIENT: Failed query: {query}")
            raise AnalyticsPrismaClientError(f"Query execution failed: {e}")

    async def execute_raw(self, query: str) -> None:
        """Execute raw SQL command (INSERT, UPDATE, DELETE).

        Args:
            query: SQL command string

        Raises:
            AnalyticsPrismaClientError: When command execution fails
        """
        if not self.db:
            logger.debug("PRISMA_CLIENT: Mock mode - command not executed")
            return
        if not self._connected:
            await self.connect()
        try:
            # Check if this is LiteLLM PrismaClient (has .db attribute) or regular Prisma client
            if hasattr(self.db, "db"):
                # LiteLLM PrismaClient - use .db.execute_raw()
                await self.db.db.execute_raw(query)
            else:
                # Regular Prisma client - use .execute_raw() directly
                await self.db.execute_raw(query)
            logger.debug("PRISMA_CLIENT: Command executed successfully")
        except Exception as e:
            logger.error(f"PRISMA_CLIENT: Command execution failed: {e}")
            logger.debug(f"PRISMA_CLIENT: Failed command: {query}")
            raise AnalyticsPrismaClientError(f"Command execution failed: {e}")

    async def health_check(self) -> bool:
        """Check database connection health.

        Returns:
            True if database is accessible, False otherwise
        """
        if not self.db:
            logger.warning("PRISMA_CLIENT: No Prisma client available for health check")
            return False
        try:
            if not self._connected:
                await self.connect()
            result = await self.query_raw("SELECT 1 as test")
            is_healthy = len(result) > 0 and result[0].get("test") == 1
            if is_healthy:
                logger.info("PRISMA_CLIENT: Database health check passed")
            else:
                logger.warning(
                    "PRISMA_CLIENT: Database health check failed - unexpected result"
                )
            return is_healthy
        except Exception as e:
            logger.error(f"PRISMA_CLIENT: Database health check failed: {e}")
            return False

    @property
    def is_connected(self) -> bool:
        """Check if client is connected to database."""
        return self._connected

    @property
    def is_available(self) -> bool:
        """Check if Prisma client is available (not in mock mode)."""
        return self.db is not None


_analytics_prisma_client: Optional[AnalyticsPrismaClient] = None


def get_analytics_prisma_client(
    database_url: Optional[str] = None,
) -> AnalyticsPrismaClient:
    """Get global analytics Prisma client instance.

    Args:
        database_url: Database URL. If None, will use existing client or create with env var.

    Returns:
        Shared AnalyticsPrismaClient instance
    """
    global _analytics_prisma_client
    if _analytics_prisma_client is None:
        _analytics_prisma_client = AnalyticsPrismaClient(database_url=database_url)
        logger.info("PRISMA_CLIENT: Global analytics Prisma client created")
    return _analytics_prisma_client


async def initialize_analytics_prisma() -> AnalyticsPrismaClient:
    """Initialize and connect global analytics Prisma client.

    Returns:
        Connected AnalyticsPrismaClient instance
    """
    client = get_analytics_prisma_client()
    await client.connect()
    logger.info(
        "PRISMA_CLIENT: Global analytics Prisma client initialized and connected"
    )
    return client


async def cleanup_analytics_prisma() -> None:
    """Cleanup global analytics Prisma client."""
    global _analytics_prisma_client
    if _analytics_prisma_client:
        await _analytics_prisma_client.disconnect()
        _analytics_prisma_client = None
        logger.info("PRISMA_CLIENT: Global analytics Prisma client cleaned up")


class AnalyticsPrismaContext:
    """Context manager for analytics Prisma client with automatic connection management."""

    def __init__(self, database_url: Optional[str] = None):
        """Initialize context manager.

        Args:
            database_url: Database URL. If None, will use environment variable.
        """
        self.database_url = database_url
        self.client: Optional[AnalyticsPrismaClient] = None

    async def __aenter__(self) -> AnalyticsPrismaClient:
        """Enter context - create and connect client."""
        self.client = AnalyticsPrismaClient(database_url=self.database_url)
        await self.client.connect()
        return self.client

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Exit context - disconnect client."""
        if self.client:
            await self.client.disconnect()
