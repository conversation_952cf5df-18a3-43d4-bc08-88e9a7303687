#!/usr/bin/env python3
"""
Логгер аналитики маршрутизации.

Кастомный LiteLLM callback для логирования решений маршрутизации и метрик производительности.

КРИТИЧЕСКИ ВАЖНО - ПОРЯДОК ВСТАВКИ В БД:
Из-за foreign key constraints в PostgreSQL схеме, вставка данных ДОЛЖНА происходить в строгом порядке:

1. LiteLLM_PromptLogs (родительская таблица) - содержит request_id как PK
2. LiteLLM_PredictorLogs (ссылается на PromptLogs.request_id)
3. LiteLLM_ResponseLogs (ссылается на PromptLogs.request_id)
4. LiteLLM_EndpointAnalysis (ссылается на PredictorLogs.id)

Нарушение этого порядка приведет к ошибке "violates foreign key constraint".

КРИТИЧЕСКИ ВАЖНО - СВЯЗЬ С LITELLM SPEND LOGS:
Для корректной связи с таблицей LiteLLM_SpendLogs необходимо использовать тот же
приоритет идентификаторов что и в LiteLLM:
1. response_obj.get("id") - ID ответа от LLM провайдера (например, "chatcmpl-xxx")
2. kwargs.get("litellm_call_id") - внутренний LiteLLM ID (только как fallback)

НЕ использовать только litellm_call_id - это приведет к потере связи с spend logs!

ЗАЩИТНЫЕ МЕХАНИЗМЫ:
- Проверка успешности каждой вставки с выбросом исключения при неудаче
- FK constraint validation перед каждой вставкой зависимых таблиц
- Автоматическая проверка существования parent записей в БД
"""
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Union, Tuple
import asyncio
import json
import statistics
import time
import traceback
import uuid

from .data_builder import extract_endpoint_analyses_data, create_router_analysis_data
from .db_client import AnalyticsDBClient
from .metadata_utils import get_metadata, log_metadata_debug_info, set_metadata
from litellm.integrations.custom_logger import CustomLogger
from llm_router.logging_utils import get_logger


logger = get_logger(__name__)


class RouterAnalyticsLogger(CustomLogger):
    """LiteLLM CustomLogger для аналитики router решений.

    Реализует элегантный metadata-паттерн по примеру LiteLLM для передачи данных
    между router компонентами и analytics callbacks. Все аналитические данные
    передаются через `router_analysis` в metadata, включая FK IDs.

    Архитектурные принципы:
    - Следование паттерну LiteLLM metadata для передачи данных между компонентами
    - Устранение race conditions через локальные переменные вместо shared state
    - Thread-safe операции для высоконагруженных систем (сотни запросов/сек)
    - Автоматический FK lookup по LiteLLM deployment IDs для упрощения
    - Graceful degradation - ошибки аналитики НЕ прерывают LLM запросы
    - КРИТИЧНО: использование правильного request_id для связи с LiteLLM_SpendLogs

    Приоритет Request ID (совпадает с LiteLLM spend_tracking_utils.py):
    1. response_obj.get("id") - ID ответа от LLM провайдера (например, "chatcmpl-xxx")
    2. kwargs.get("litellm_call_id") - внутренний LiteLLM ID (только fallback)

    Схема потока данных:
    1. Router → metadata injection: `set_metadata(request_kwargs, "router_analysis", data)`
    2. LiteLLM → callbacks: автоматическая передача metadata
    3. Callbacks → DB: секвенциальная запись с FK relationships
    4. FK IDs передаются через router_analysis (как LiteLLM в standard_logging_object)

    Пример использования:
        >>> # В Router компоненте
        >>> router_analysis = create_router_analysis_data(...)
        >>> set_metadata(request_kwargs, "router_analysis", router_analysis)
        >>>
        >>> # В callback (автоматически)
        >>> router_analysis = kwargs["metadata"]["router_analysis"]
        >>> predictor_log_id = await self._write_predictor_analysis(...)
        >>> router_analysis["_predictor_log_id"] = predictor_log_id  # FK для endpoint analyses

    Потокобезопасность:
    - Исправлена проблема `self._current_predictor_log_id` race condition
    - ID передаются через параметры/metadata, а не shared instance variables
    - Каждый запрос работает со своими локальными данными

    Совместимость:
    - Полная совместимость с LiteLLM CustomLogger API
    - Автоматическое обнаружение и обработка router_analysis в metadata
    - Graceful handling отсутствующих данных без прерывания LLM запросов
    """

    def __init__(
        self,
        db_client: Optional[AnalyticsDBClient] = None,
        queue_max_size: int = 1000,
        enabled: bool = True,
        prisma_client=None,
        processed_requests_ttl_minutes: int = 15,
    ):
        """Инициализация логгера аналитики роутера.

        Args:
            db_client: Клиент базы данных аналитики (создается автоматически если не указан)
            queue_max_size: Максимальный размер очереди для фоновой обработки
            enabled: Включен ли логгер аналитики
            prisma_client: Prisma клиент для работы с БД (опционально)
            processed_requests_ttl_minutes: TTL для кэша обработанных request_id в минутах
        """
        super().__init__()
        self.enabled = enabled
        self.db_client = db_client or AnalyticsDBClient(
            litellm_prisma_client=prisma_client
        )
        self.log_queue = asyncio.Queue(maxsize=queue_max_size)
        self.stream_trackers = {}

        # Кэш обработанных request_id для предотвращения дублирования при потоковых запросах
        self.processed_requests: Dict[str, datetime] = {}
        self.processed_requests_ttl = timedelta(minutes=processed_requests_ttl_minutes)

        self._background_task: Optional[asyncio.Task] = None
        self._start_background_worker()
        logger.info(
            f"ANALYTICS: RouterAnalyticsLogger инициализирован (enabled={enabled}, queue_size={queue_max_size}, ttl={processed_requests_ttl_minutes}min)"
        )

    def register_with_litellm(self, auto_register: bool = True) -> bool:
        """Регистрация RouterAnalyticsLogger в LiteLLM callbacks.

        Args:
            auto_register: Автоматически регистрировать если callbacks уже существует

        Returns:
            True если регистрация успешна, False иначе
        """
        try:
            import litellm

            logger.info("ANALYTICS: Setting up RouterAnalyticsLogger...")

            if isinstance(litellm.callbacks, list):
                already_registered = any(
                    isinstance(cb, RouterAnalyticsLogger) for cb in litellm.callbacks
                )
                if not already_registered:
                    litellm.callbacks.append(self)
                    logger.info(
                        "ANALYTICS: Registered RouterAnalyticsLogger in litellm.callbacks"
                    )
                else:
                    logger.info("ANALYTICS: RouterAnalyticsLogger already registered")
            else:
                if auto_register:
                    litellm.callbacks = [self]
                    logger.info(
                        "ANALYTICS: Initialized callbacks list with analytics logger"
                    )
                else:
                    logger.warning(
                        "ANALYTICS: litellm.callbacks is not a list and auto_register=False"
                    )
                    return False

            logger.info("ANALYTICS: RouterAnalyticsLogger setup completed successfully")
            return True

        except Exception as e:
            logger.warning(f"ANALYTICS: Failed to setup RouterAnalyticsLogger: {e}")
            logger.info("ANALYTICS: Continuing without analytics logging")
            return False

    def collect_analytics_for_deployments(
        self,
        request_kwargs: Optional[Dict],
        model: str,
        messages: Optional[List],
        input: Optional[Union[str, List]],
        selected_deployment: Dict[str, Any],
        evaluations: Optional[List[Dict[str, Any]]] = None,
        analysis_start_time: Optional[datetime] = None,
        analysis_start_time_ts: Optional[float] = None,
        load_balancing_weight: Optional[float] = None,
        selection_method: str = "hybrid",
        error_message: Optional[str] = None,
    ):
        """Универсальный сбор аналитических данных для любого количества deployments.

        Обрабатывает все сценарии маршрутизации:
        - Одиночный deployment (evaluations=None или len=1)
        - Множественные deployments (evaluations с несколькими элементами)
        - Fallback случаи (selection_method="fallback")

        Args:
            request_kwargs: Параметры запроса LiteLLM
            model: Имя модели
            messages: Сообщения чата
            input: Входные данные для non-chat запросов
            selected_deployment: Выбранный deployment
            evaluations: Список оценок deployments (None для single/fallback)
            analysis_start_time: Время начала анализа
            analysis_start_time_ts: Timestamp начала анализа
            load_balancing_weight: Вес балансировки нагрузки
            selection_method: Метод выбора (hybrid/ml_only/fallback)
            error_message: Сообщение об ошибке для fallback случаев
        """
        try:
            if analysis_start_time is None:
                analysis_start_time = datetime.now()
            if analysis_start_time_ts is None:
                analysis_start_time_ts = time.time()
            if load_balancing_weight is None:
                load_balancing_weight = 0.3  # default value

            analysis_completed_time = datetime.now()
            analysis_duration_ms = int((time.time() - analysis_start_time_ts) * 1000)
            selected_deployment_id = selected_deployment.get("model_info", {}).get("id")

            if evaluations is None or len(evaluations) == 0:
                evaluations = [
                    {
                        "deployment_id": selected_deployment_id,
                        "deployment": selected_deployment,
                        "predicted_time": None,
                        "hybrid_score": None,
                        "current_load": 0,  # Default value since we don't have leastbusy here
                    }
                ]

            selected_evaluation = None
            for eval_data in evaluations:
                if eval_data.get("deployment_id") == selected_deployment_id:
                    selected_evaluation = eval_data
                    break
            if selected_evaluation is None:
                selected_evaluation = evaluations[0] if evaluations else {}

            if selection_method == "hybrid" and len(evaluations) == 1:
                if selected_evaluation.get("predicted_time") is None:
                    selection_method = "fallback"
                else:
                    selection_method = "ml_only"

            predictor_results = {
                "analysis_started_at": analysis_start_time.isoformat(),
                "analysis_completed_at": analysis_completed_time.isoformat(),
                "analysis_duration_ms": analysis_duration_ms,
                "deployments_analyzed_count": len(evaluations),
                "selected_deployment_id": selected_deployment_id,
                "predicted_time": selected_evaluation.get("predicted_time"),
                "load_balancing_weight": load_balancing_weight,
                "selected_hybrid_score": selected_evaluation.get("hybrid_score"),
                "selected_current_load": selected_evaluation.get("current_load"),
                "selection_method": selection_method,
                "least_busy_available": any(
                    (
                        eval_data.get("current_load") is not None
                        for eval_data in evaluations
                    )
                ),
                "endpoint_analyses": [
                    {
                        "deployment_id": eval_data.get("deployment_id"),
                        "predictor_response_time": eval_data.get("predicted_time"),
                        "predictor_error": eval_data.get("predictor_error"),
                        "predictor_duration_ms": eval_data.get("predictor_duration_ms"),
                        "current_load": eval_data.get("current_load"),
                        "load_source": eval_data.get("load_source", "unknown"),
                        "hybrid_score": eval_data.get("hybrid_score"),
                        "final_rank": i + 1,
                        "was_selected": eval_data.get("deployment_id")
                        == selected_deployment_id,
                    }
                    for i, eval_data in enumerate(evaluations)
                ],
            }

            if error_message and selection_method == "fallback":
                predictor_results["error_message"] = error_message

            router_analysis_data = create_router_analysis_data(
                model_name=model,
                request_kwargs=self._build_analytics_request_kwargs(
                    model, messages, input, request_kwargs, predictor_results
                ),
            )
            set_metadata(request_kwargs, "router_analysis", router_analysis_data)
            logger.info(
                f"ANALYTICS: Collected data for {selection_method} selection: {len(evaluations)} deployments, selected {selected_deployment_id}"
            )
        except Exception as e:
            logger.error(f"ANALYTICS: Error collecting analytics data: {e}")
            logger.debug(f"ANALYTICS: Error traceback: {traceback.format_exc()}")

    def _build_analytics_request_kwargs(
        self,
        model: str,
        messages: Optional[Any],
        input_data: Optional[Any],
        request_kwargs: Optional[Dict[str, Any]],
        predictor_results: Dict[str, Any],
    ) -> Dict[str, Any]:
        """Создает request_kwargs для create_router_analysis_data.

        Объединяет базовые параметры LLM запроса со специфичными данными аналитики
        роутера, включая результаты предиктора и справочные данные.

        Args:
            model: Имя модели
            messages: Сообщения чата
            input_data: Входные данные для non-chat запросов
            request_kwargs: Оригинальные параметры запроса
            predictor_results: Результаты анализа предиктора

        Returns:
            Полный словарь request_kwargs для create_router_analysis_data
        """
        return {
            "messages": messages,
            "input_data": input_data,
            "task_type": (
                request_kwargs.get("task_type", "chat") if request_kwargs else "chat"
            ),
            "temperature": (
                request_kwargs.get("temperature") if request_kwargs else None
            ),
            "max_tokens": request_kwargs.get("max_tokens") if request_kwargs else None,
            "top_p": request_kwargs.get("top_p") if request_kwargs else None,
            "stream": request_kwargs.get("stream", False) if request_kwargs else False,
            "user": request_kwargs.get("user") if request_kwargs else None,
            "predictor_results": predictor_results,
        }

    def _get_request_id(
        self, kwargs: Dict[str, Any], response_obj: Optional[Any] = None
    ) -> Optional[str]:
        """Получение request_id с правильным приоритетом для связи с LiteLLM_SpendLogs.

        КРИТИЧНО: Эта функция нужна ТОЛЬКО в RouterAnalyticsLogger, потому что только здесь
        доступен response_obj с правильным ID от LLM провайдера. На этапе роутинга
        response_obj еще не существует, поэтому там используется простой litellm_call_id.

        Использует тот же приоритет что и LiteLLM spend_tracking_utils.get_spend_logs_id():
        1. response_obj.id - ID ответа от LLM провайдера (например, "chatcmpl-xxx")
        2. kwargs.get("litellm_call_id") - внутренний LiteLLM ID (только fallback)

        Args:
            kwargs: Параметры запроса от LiteLLM
            response_obj: Объект ответа от LLM (ModelResponse или dict) с правильным ID от провайдера

        Returns:
            request_id для записи в БД, связанный с LiteLLM_SpendLogs
        """
        request_id = None
        if response_obj:
            try:
                if hasattr(response_obj, "id"):
                    request_id = getattr(response_obj, "id")
                elif isinstance(response_obj, dict):
                    request_id = response_obj.get("id")
            except Exception as e:
                logger.debug(f"ANALYTICS: Error extracting id from response_obj: {e}")
        if not request_id:
            request_id = kwargs.get("litellm_call_id")
        return request_id

    def _extract_router_analysis(self, kwargs: Dict[str, Any]) -> Dict[str, Any]:
        """Стандартизированное извлечение router_analysis из kwargs.

        Использует унифицированные metadata утилиты для получения данных из правильного места
        в структуре LiteLLM kwargs независимо от того где они фактически хранятся.

        Args:
            kwargs: Параметры запроса от LiteLLM

        Returns:
            Словарь с данными router_analysis или пустой словарь если не найдено
        """
        try:
            log_metadata_debug_info(kwargs, "in_analytics_callback")
            router_analysis = get_metadata(kwargs, "router_analysis")
            if router_analysis:
                logger.debug(
                    f"ANALYTICS: Found router_analysis with keys: {list(router_analysis.keys())}"
                )
                return router_analysis
            logger.debug("ANALYTICS: No router_analysis found in any metadata location")
            return {}
        except Exception as e:
            logger.error(f"ANALYTICS: Error extracting router_analysis: {e}")
            return {}

    def _is_request_processed(self, request_id: str) -> bool:
        """Проверяет был ли request_id уже обработан (для предотвращения дублирования при стриминге).

        Args:
            request_id: Идентификатор запроса

        Returns:
            True если запрос уже был обработан, False иначе
        """
        if request_id in self.processed_requests:
            processed_time = self.processed_requests[request_id]
            # Проверяем не истекло ли время жизни записи
            if datetime.now() - processed_time < self.processed_requests_ttl:
                return True
            else:
                # Удаляем устаревшую запись
                del self.processed_requests[request_id]
        return False

    def _mark_request_processed(self, request_id: str) -> None:
        """Отмечает request_id как обработанный.

        Args:
            request_id: Идентификатор запроса
        """
        self.processed_requests[request_id] = datetime.now()
        logger.debug(f"ANALYTICS: Marked request {request_id} as processed")

    def _cleanup_processed_requests(self) -> None:
        """Очищает устаревшие записи из кэша обработанных запросов."""
        current_time = datetime.now()
        expired_requests = [
            request_id
            for request_id, processed_time in self.processed_requests.items()
            if current_time - processed_time >= self.processed_requests_ttl
        ]

        for request_id in expired_requests:
            del self.processed_requests[request_id]

        if expired_requests:
            logger.debug(
                f"ANALYTICS: Cleaned up {len(expired_requests)} expired request IDs from cache"
            )

    def _start_background_worker(self) -> None:
        """Запуск фонового обработчика для асинхронной очереди.

        Создает и запускает фоновую asyncio задачу, которая непрерывно
        обрабатывает данные аналитики из очереди. Если обработчик уже
        запущен, ничего не делает.

        Gracefully обрабатывает отсутствие активного event loop (например, в тестах).
        """
        try:
            logger.debug("ANALYTICS: Attempting to start background worker...")
            # Проверяем есть ли активный event loop
            try:
                asyncio.get_running_loop()
            except RuntimeError:
                logger.debug(
                    "ANALYTICS: No running event loop - background worker будет запущен позже"
                )
                return

            if self._background_task is None or self._background_task.done():
                logger.debug("ANALYTICS: Creating background task...")
                self._background_task = asyncio.create_task(
                    self._background_db_writer()
                )
                logger.info("ANALYTICS: Background worker task created successfully")
            else:
                logger.debug("ANALYTICS: Background worker already running")
        except Exception as e:
            logger.error(
                f"ANALYTICS: Failed to start background worker: {e}", exc_info=True
            )

    async def _background_db_writer(self) -> None:
        """Фоновый обработчик для записи данных аналитики из очереди.

        Работает непрерывно в фоновой задаче, обрабатывая элементы логов из
        асинхронной очереди. Ошибки в обработке аналитики логируются, но
        не прерывают работу обработчика для продолжения основного потока запросов.

        Также выполняет периодическую очистку кэша обработанных request_id.
        """
        logger.info("ANALYTICS: Background DB writer started")
        processed_count = 0
        cleanup_interval = 100  # Очистка кэша каждые 100 обработанных элементов

        while True:
            try:
                log_item = await self.log_queue.get()
                await self._process_log_item(log_item)
                self.log_queue.task_done()

                processed_count += 1
                # Периодическая очистка кэша обработанных запросов
                if processed_count % cleanup_interval == 0:
                    self._cleanup_processed_requests()

            except asyncio.CancelledError:
                logger.info("ANALYTICS: Background worker cancelled")
                break
            except Exception as e:
                logger.error(
                    f"ANALYTICS: Background worker error ({type(e).__name__}): {e}",
                    exc_info=True,
                )
                await asyncio.sleep(0.1)

    async def _process_log_item(self, log_item: Dict[str, Any]) -> None:
        """Обработка одного элемента лога из очереди.

        Args:
            log_item: Элемент лога для обработки. Должен содержать поле 'type' со значением
                     'success' или 'failure', и другие поля в зависимости от типа.
        """
        try:
            item_type = log_item.get("type")
            request_id = log_item.get("request_id")
            if item_type == "success":
                await self._write_success_analytics(
                    request_id=request_id,
                    router_analysis=log_item["router_analysis"],
                    response_obj=log_item["response_obj"],
                    start_time=log_item["start_time"],
                    end_time=log_item["end_time"],
                )
            elif item_type == "failure":
                await self._write_failure_analytics(
                    request_id=request_id,
                    router_analysis=log_item["router_analysis"],
                    error_obj=log_item.get("error_obj"),
                )
            logger.debug(
                f"ANALYTICS: Background processed {item_type} for request {request_id}"
            )
        except Exception as e:
            logger.error(
                f"ANALYTICS: Failed to process log item ({type(e).__name__}): {e}",
                exc_info=True,
            )

    async def async_log_success_event(self, kwargs, response_obj, start_time, end_time):
        """Обработка успешного вызова LLM API.

        Этот метод вызывается LiteLLM при успешном завершении API вызова.
        Извлекает данные анализа маршрутизации из метаданных запроса и либо
        ставит их в очередь для фоновой обработки, либо обрабатывает немедленно.

        Использует кэш обработанных request_id для предотвращения дублирования
        записей при потоковых запросах (где LiteLLM может вызывать колбеки несколько раз).

        Извлечение данных из kwargs:
        - metadata.router_analysis: Данные решения маршрутизации, добавленные во время роутинга запроса,
          содержащие результаты предиктора, выбор развертывания и справочные данные
        - Другие стандартные LiteLLM параметры для контекста запроса

        Args:
            kwargs: Параметры запроса от LiteLLM, содержащие:
                   - metadata: Словарь с данными router_analysis если доступен
                   - Другие LiteLLM параметры запроса
            response_obj: Объект ответа от LLM API, содержащий токены, тайминги и т.д.
            start_time: Время начала запроса как объект datetime
            end_time: Время окончания запроса как объект datetime
        """
        if not self.enabled:
            return
        request_id = self._get_request_id(kwargs, response_obj)
        if not request_id:
            logger.warning(
                "ANALYTICS: No request_id found in response_obj.id or fallback"
            )
            return

        # Проверяем кэш обработанных запросов для предотвращения дублирования
        if self._is_request_processed(request_id):
            logger.debug(
                f"ANALYTICS: Request {request_id} already processed, skipping duplicate"
            )
            return

        router_analysis = self._extract_router_analysis(kwargs)
        if not router_analysis:
            logger.debug(f"ANALYTICS: No router analysis data for request {request_id}")
            return
        logger.debug(f"ANALYTICS: async_log_success_event request_id {request_id}")
        logger.debug(f"ANALYTICS: async_log_success_event router kwargs: {kwargs}")
        logger.debug(
            f"ANALYTICS: async_log_success_event router response_obj: {response_obj}"
        )
        try:
            # Отмечаем запрос как обработанный ДО постановки в очередь
            self._mark_request_processed(request_id)

            await self._queue_log_item(
                {
                    "type": "success",
                    "request_id": request_id,
                    "router_analysis": router_analysis,
                    "response_obj": response_obj,
                    "start_time": start_time,
                    "end_time": end_time,
                }
            )
            logger.debug(f"ANALYTICS: Queued success data for request {request_id}")
        except Exception as e:
            logger.error(
                f"ANALYTICS: Failed to log success event for request {request_id} ({type(e).__name__}): {e}",
                exc_info=True,
            )

    async def async_log_failure_event(self, kwargs, response_obj, start_time, end_time):
        """Обработка неудачного вызова LLM API.

        Этот метод вызывается LiteLLM при неудаче API вызова. Извлекает
        данные анализа маршрутизации и информацию об ошибке для хранения в аналитике.

        Использует кэш обработанных request_id для предотвращения дублирования
        записей при потоковых запросах.

        Args:
            kwargs: Параметры запроса из LiteLLM, содержащие
                   метаданные с данными router_analysis
            response_obj: Объект ответа с ошибкой, содержащий детали ошибки
            start_time: Время начала запроса как объект datetime
            end_time: Время окончания запроса как объект datetime
        """
        if not self.enabled:
            return
        request_id = self._get_request_id(kwargs, response_obj)
        if not request_id:
            logger.warning(
                "ANALYTICS: No request_id found in response_obj.id or fallback for failure"
            )
            return

        # Проверяем кэш обработанных запросов для предотвращения дублирования
        if self._is_request_processed(request_id):
            logger.debug(
                f"ANALYTICS: Request {request_id} already processed, skipping duplicate failure"
            )
            return

        router_analysis = self._extract_router_analysis(kwargs)
        if not router_analysis:
            logger.debug(f"ANALYTICS: No router analysis data for request {request_id}")
            return
        logger.debug(f"ANALYTICS: async_log_failure_event request_id {request_id}")
        logger.debug(f"ANALYTICS: async_log_failure_event router kwargs: {kwargs}")
        logger.debug(
            f"ANALYTICS: async_log_failure_event router response_obj: {response_obj}"
        )
        try:
            # Отмечаем запрос как обработанный ДО постановки в очередь
            self._mark_request_processed(request_id)

            await self._queue_log_item(
                {
                    "type": "failure",
                    "request_id": request_id,
                    "router_analysis": router_analysis,
                    "error_obj": response_obj,
                }
            )
            logger.debug(f"ANALYTICS: Queued failure data for request {request_id}")
        except Exception as e:
            logger.error(
                f"ANALYTICS: Failed to log failure event for request {request_id} ({type(e).__name__}): {e}",
                exc_info=True,
            )

    async def async_log_stream_event(self, kwargs, response_obj, start_time, end_time):
        """Обработка чанка потокового ответа.

        Этот метод вызывается для каждого чанка в потоковом ответе. Отслеживает
        метрики времени, такие как время до первого токена (TTFT) и джиттер ответа.

        Args:
            kwargs: Параметры запроса
            response_obj: Объект чанка потока с контентом и данными времени
            start_time: Время начала запроса
            end_time: Время получения чанка
        """
        if not self.enabled:
            return
        request_id = self._get_request_id(kwargs, response_obj)
        if not request_id:
            return
        try:
            if request_id not in self.stream_trackers:
                self.stream_trackers[request_id] = {
                    "first_token_time": end_time,
                    "chunk_times": [end_time],
                    "chunks_received": 1,
                    "start_time": start_time,
                }
            else:
                tracker = self.stream_trackers[request_id]
                tracker["chunk_times"].append(end_time)
                tracker["chunks_received"] += 1
            logger.debug(f"ANALYTICS: Stream event tracked for request {request_id}")
        except Exception as e:
            logger.error(
                f"ANALYTICS: Failed to track stream event for request {request_id} ({type(e).__name__}): {e}"
            )

    def _calculate_stream_metrics(
        self, request_id: str
    ) -> Tuple[Optional[float], Optional[float]]:
        """Вычисление метрик потока для запроса.

        Вычисляет время до первого токена (TTFT) и джиттер ответа из
        отслеживаемых данных потока.

        Args:
            request_id: ID запроса для вычисления метрик

        Returns:
            Кортеж (ttft_ms, jitter_ms) или (None, None) если нет данных
        """
        tracker = self.stream_trackers.get(request_id)
        if not tracker:
            return (None, None)
        try:
            start_time = tracker["start_time"]
            first_token_time = tracker["first_token_time"]
            ttft_ms = (first_token_time - start_time).total_seconds() * 1000
            chunk_times = tracker["chunk_times"]
            jitter_ms = None
            if len(chunk_times) > 1:
                time_diffs = []
                for i in range(1, len(chunk_times)):
                    diff = (chunk_times[i] - chunk_times[i - 1]).total_seconds() * 1000
                    time_diffs.append(diff)
                if len(time_diffs) > 1:
                    jitter_ms = statistics.stdev(time_diffs)
            del self.stream_trackers[request_id]
            return (ttft_ms, jitter_ms)
        except Exception as e:
            logger.error(
                f"ANALYTICS: Error calculating stream metrics for {request_id} ({type(e).__name__}): {e}"
            )
            return (None, None)

    async def _queue_log_item(self, log_item: Dict[str, Any]) -> None:
        """Постановка элемента лога в очередь для фоновой обработки.

        Args:
            log_item: Элемент лога для постановки в очередь фоновой обработки

        Raises:
            Exception: Если очередь переполнена и не может принять новые элементы
        """
        try:
            await self.log_queue.put(log_item)
        except Exception as e:
            logger.error(
                f"ANALYTICS: Failed to queue log item ({type(e).__name__}): {e}"
            )
            raise

    async def _write_success_analytics(
        self,
        request_id: str,
        router_analysis: Dict[str, Any],
        response_obj: Dict[str, Any],
        start_time: datetime,
        end_time: datetime,
    ) -> None:
        """Запись полных данных аналитики для успешного запроса.

        Обрабатывает все данные аналитики последовательно, поддерживая правильные FK связи
        через metadata-паттерн по примеру LiteLLM. Все ID передаются через router_analysis
        без дополнительных параметров методов.

        Args:
            request_id: Уникальный идентификатор запроса для корреляции
            router_analysis: Полные данные анализа маршрутизации, содержащие все необходимые ID
            response_obj: Объект ответа LLM API, содержащий метрики и токены
            start_time: Время начала запроса для вычисления длительности
            end_time: Время завершения запроса для вычисления длительности
        """
        try:
            reference_data = router_analysis.get("reference_data", {})
            await self._upsert_reference_data(reference_data)
            prompt_data = router_analysis.get("prompt_data", {})
            await self._write_prompt_data(request_id, prompt_data)
            predictor_data = router_analysis.get("predictor_analysis", {})
            predictor_log_id = await self._write_predictor_analysis(
                request_id, predictor_data
            )
            if predictor_log_id:
                router_analysis["_predictor_log_id"] = predictor_log_id
            await self._write_response_data(
                request_id, router_analysis, response_obj, start_time, end_time
            )
            await self._write_endpoint_analyses(request_id, router_analysis)
            logger.debug(
                f"ANALYTICS: Successfully wrote success analytics data for request {request_id}"
            )
        except Exception as e:
            logger.error(
                f"ANALYTICS: Failed to write success analytics for {request_id} ({type(e).__name__}): {e}",
                exc_info=True,
            )

    async def _write_failure_analytics(
        self,
        request_id: str,
        router_analysis: Dict[str, Any],
        error_obj: Optional[Dict[str, Any]] = None,
    ) -> None:
        """Запись полных данных аналитики для неудавшегося запроса.

        Обрабатывает данные аналитики для ошибок, поддерживая правильные FK связи
        через metadata-паттерн по примеру LiteLLM.

        Args:
            request_id: Уникальный идентификатор запроса для корреляции
            router_analysis: Полные данные анализа маршрутизации, содержащие все необходимые ID
            error_obj: Объект ошибки из неудавшегося запроса, содержащий детали ошибки
        """
        try:
            reference_data = router_analysis.get("reference_data", {})
            await self._upsert_reference_data(reference_data)
            prompt_data = router_analysis.get("prompt_data", {})
            await self._write_prompt_data(request_id, prompt_data)
            predictor_data = router_analysis.get("predictor_analysis", {})
            predictor_log_id = await self._write_predictor_analysis(
                request_id, predictor_data
            )
            if predictor_log_id:
                router_analysis["_predictor_log_id"] = predictor_log_id
            await self._write_failure_response_data(
                request_id, router_analysis, error_obj
            )
            await self._write_endpoint_analyses(request_id, router_analysis)
            logger.debug(
                f"ANALYTICS: Successfully wrote failure analytics data for request {request_id}"
            )
        except Exception as e:
            logger.error(
                f"ANALYTICS: Failed to write failure analytics for {request_id} ({type(e).__name__}): {e}",
                exc_info=True,
            )

    async def _write_prompt_data(
        self, request_id: str, prompt_data: Dict[str, Any]
    ) -> None:
        """Запись данных промпта в LiteLLM_PromptLogs с FK lookup модели.

        Сохраняет параметры промпта, включая сообщения, настройки модели
        и классификацию задач для отслеживания паттернов использования.

        Args:
            request_id: Уникальный идентификатор запроса для корреляции
            prompt_data: Данные промпта из анализа роутера, содержащие сообщения,
                        параметры модели и классификацию задач
        """
        success = await self.db_client.insert_prompt_log(request_id, prompt_data)
        if not success:
            logger.error(
                f"ANALYTICS: Failed to insert prompt log for request {request_id}"
            )
            raise Exception(f"Failed to insert prompt log for request {request_id}")

    async def _write_predictor_analysis(
        self, request_id: str, predictor_data: Dict[str, Any]
    ) -> Optional[int]:
        """Запись анализа предиктора в LiteLLM_PredictorLogs.

        Сохраняет данные решения роутера, включая выбранное развертывание,
        анализ времени и метрики балансировки нагрузки для отслеживания
        производительности и оптимизации.

        Args:
            request_id: Уникальный идентификатор запроса для корреляции
            predictor_data: Данные анализа предиктора из роутера, содержащие
                          логику выбора развертывания, предсказания времени и метрики

        Returns:
            ID созданной записи predictor log для связи с endpoint analyses или None при ошибке
        """
        predictor_log_id = await self.db_client.insert_predictor_log_simple(
            request_id, predictor_data
        )
        if not predictor_log_id:
            logger.error(
                f"ANALYTICS: Failed to insert predictor log for request {request_id}"
            )
            raise Exception(f"Failed to insert predictor log for request {request_id}")
        return predictor_log_id

    async def _write_response_data(
        self,
        request_id: str,
        router_analysis: Dict[str, Any],
        response_obj: Dict[str, Any],
        start_time: datetime,
        end_time: datetime,
    ) -> None:
        """Запись реальных результатов выполнения запроса в LiteLLM_ResponseLogs.

        Извлекает и сохраняет метрики производительности, использование токенов
        и результаты выполнения для сравнения с предсказаниями предиктора.

        Args:
            request_id: Уникальный идентификатор запроса для корреляции
            router_analysis: Полные данные анализа роутера с deployment_id
            response_obj: Объект ответа LLM API, содержащий токены, использование и метаданные
            start_time: Время начала запроса для вычисления длительности
            end_time: Время завершения запроса
        """
        predictor_data = router_analysis.get("predictor_analysis", {})
        predicted_time = predictor_data.get("predicted_time")
        selected_deployment_id = predictor_data.get("selected_deployment_id")
        if not selected_deployment_id:
            logger.debug(f"ANALYTICS: router_analysis: {router_analysis}")
            logger.error(
                f"ANALYTICS: selected_deployment_id отсутствует в predictor_data для {request_id}"
            )
            return
        actual_response_time = (end_time - start_time).total_seconds()
        prediction_accuracy = None
        prediction_error = None
        if predicted_time and predicted_time > 0:
            prediction_error = actual_response_time - predicted_time
            prediction_accuracy = abs(prediction_error) / actual_response_time
        usage = response_obj.get("usage", {})
        ttft, jitter = (None, None)
        is_stream = router_analysis.get("prompt_data", {}).get("stream", False)
        if is_stream:
            ttft, jitter = self._calculate_stream_metrics(request_id)
        response_data = {
            "deployment_id": selected_deployment_id,
            "api_call_started_at": start_time,
            "completion_time": end_time,
            "actual_response_time": actual_response_time,
            "time_to_first_token": ttft,
            "is_stream_response": is_stream,
            "stream_jitter": jitter,
            "input_tokens": usage.get("prompt_tokens"),
            "output_tokens": usage.get("completion_tokens"),
            "total_tokens": usage.get("total_tokens"),
            "tokens_per_second": (
                usage.get("completion_tokens", 0) / actual_response_time
                if actual_response_time > 0
                else None
            ),
            "response_finish_reason": self._extract_finish_reason(response_obj),
            "success": True,
            "prediction_accuracy": prediction_accuracy,
            "prediction_error": prediction_error,
            "predicted_time": predicted_time,
        }
        success = await self.db_client.insert_response_log_simple(
            request_id, response_data
        )
        if not success:
            logger.error(
                f"ANALYTICS: Failed to insert response log for request {request_id}"
            )
            raise Exception(f"Failed to insert response log for request {request_id}")

    async def _write_failure_response_data(
        self,
        request_id: str,
        router_analysis: Dict[str, Any],
        error_obj: Optional[Dict[str, Any]] = None,
    ) -> None:
        """Запись данных ошибочного ответа в LiteLLM_ResponseLogs.

        Записывает информацию об ошибке, включая тип ошибки, сообщение и код
        статуса для отладки и анализа надежности.
        Использует упрощенный FK lookup по deployment_id для новой схемы.

        Args:
            request_id: Уникальный идентификатор запроса для корреляции
            router_analysis: Полные данные анализа роутера с информацией о развертывании
            error_obj: Объект ошибки из неудавшегося запроса, содержащий детали ошибки
        """
        try:
            predictor_data = router_analysis.get("predictor_analysis", {})
            predicted_time = predictor_data.get("predicted_time")
            litellm_deployment_id = predictor_data.get("selected_deployment_id")
            if not litellm_deployment_id:
                logger.warning(
                    f"ANALYTICS: Отсутствует selected_deployment_id для failure response request {request_id}"
                )
                return
            error_type = None
            error_message = None
            http_status_code = None
            if error_obj:
                if isinstance(error_obj, dict):
                    error_type = error_obj.get("error", {}).get("type")
                    error_message = error_obj.get("error", {}).get("message")
                    http_status_code = error_obj.get("status_code")
                elif hasattr(error_obj, "__class__"):
                    error_type = error_obj.__class__.__name__
                    error_message = str(error_obj)
                    http_status_code = getattr(error_obj, "status_code", None)
                else:
                    error_message = str(error_obj)
            current_time = datetime.utcnow()
            response_data = {
                "deployment_id": litellm_deployment_id,
                "api_call_started_at": current_time,
                "completion_time": current_time,
                "actual_response_time": 0.0,
                "time_to_first_token": None,
                "is_stream_response": router_analysis.get("prompt_data", {}).get(
                    "stream", False
                ),
                "stream_jitter": None,
                "input_tokens": None,
                "output_tokens": None,
                "total_tokens": None,
                "tokens_per_second": None,
                "response_finish_reason": None,
                "success": False,
                "error_type": error_type,
                "error_message": error_message,
                "http_status_code": http_status_code,
                "prediction_accuracy": None,
                "prediction_error": None,
                "predicted_time": predicted_time,
            }
            success = await self.db_client.insert_response_log_simple(
                request_id, response_data
            )
            if not success:
                logger.error(
                    f"ANALYTICS: Failed to insert failure response log for request {request_id}"
                )
                raise Exception(
                    f"Failed to insert failure response log for request {request_id}"
                )
            logger.debug(
                f"ANALYTICS: Wrote failure response log for request {request_id}"
            )
        except Exception as e:
            logger.warning(
                f"ANALYTICS: Failed to write failure response data for {request_id}: {e}"
            )

    async def _write_endpoint_analyses(
        self, request_id: str, router_analysis: Dict[str, Any]
    ) -> None:
        """Запись данных анализа endpoints в LiteLLM_EndpointAnalysis.

        Сохраняет детальный анализ каждого endpoint, рассмотренного во время маршрутизации,
        включая оценки предсказаний, метрики нагрузки и рейтинг выбора.
        Использует metadata-паттерн LiteLLM для передачи всех данных включая FK IDs.

        Args:
            request_id: Уникальный идентификатор запроса для корреляции
            router_analysis: Полные данные анализа роутера, содержащие все необходимые ID
        """
        predictor_log_id = router_analysis.get("_predictor_log_id")
        if not predictor_log_id:
            logger.warning(
                f"ANALYTICS: predictor_log_id не найден в router_analysis для request {request_id}"
            )
            return
        predictor_analysis = router_analysis.get("predictor_analysis", {})
        endpoint_analyses = predictor_analysis.get("endpoint_analyses", [])
        if not endpoint_analyses:
            logger.debug(f"ANALYTICS: No endpoint analyses for request {request_id}")
            return
        reference_data = router_analysis.get("reference_data", {})
        deployments_data = reference_data.get("deployments", [])
        if not deployments_data:
            logger.warning(
                f"ANALYTICS: No deployments reference data for request {request_id}"
            )
            return
        prompt_data = router_analysis.get("prompt_data", {})
        task_type = prompt_data.get("task_type", "chat")
        try:
            processed_analyses = extract_endpoint_analyses_data(
                endpoint_analyses=endpoint_analyses,
                deployments_data=deployments_data,
                task_type=task_type,
            )
            for endpoint_data in processed_analyses:
                success = await self.db_client.insert_endpoint_analysis_simple(
                    predictor_log_id, endpoint_data
                )
                if not success:
                    logger.error(
                        f"ANALYTICS: Failed to insert endpoint analysis for deployment {endpoint_data.get('deployment_id')}"
                    )
            logger.debug(
                f"ANALYTICS: Wrote {len(processed_analyses)} endpoint analyses for request {request_id}"
            )
        except Exception as e:
            logger.error(
                f"ANALYTICS: Failed to process endpoint analyses for request {request_id}: {e}"
            )

    def _parse_datetime(
        self, datetime_str: Union[str, datetime, None]
    ) -> Optional[datetime]:
        """Парсинг строки datetime в объект datetime.

        Поддерживает различные форматы datetime строк и возвращает None
        для невалидных значений вместо выбрасывания исключений.

        Args:
            datetime_str: Строка datetime, объект datetime, или None

        Returns:
            Объект datetime или None если парсинг не удался
        """
        if not datetime_str:
            return None
        if isinstance(datetime_str, datetime):
            return datetime_str
        try:
            return datetime.fromisoformat(str(datetime_str).replace("Z", "+00:00"))
        except (ValueError, AttributeError):
            logger.warning(f"ANALYTICS: Не удалось распарсить datetime: {datetime_str}")
            return None

    def _extract_finish_reason(self, response_obj: Dict[str, Any]) -> Optional[str]:
        """Извлекает причину завершения из объекта ответа.

        Args:
            response_obj: Объект ответа LLM API

        Returns:
            Причина завершения или None если не найдена
        """
        try:
            choices = response_obj.get("choices", [])
            if choices and len(choices) > 0:
                return choices[0].get("finish_reason")
        except (KeyError, IndexError, TypeError):
            pass
        return None

    async def _upsert_reference_data(self, reference_data: Dict[str, Any]) -> None:
        """Обновление справочных данных (models и deployments) в БД.

        Обрабатывает справочные данные из router_analysis для создания или обновления
        записей в LiteLLM_Models и LiteLLM_Deployments. Это критично для FK связей
        в новой схеме с автоинкрементными PK.

        Args:
            reference_data: Словарь справочных данных с ключами 'models' и 'deployments'
        """
        if not reference_data:
            logger.debug("ANALYTICS: No reference data to upsert")
            return
        try:
            models_data = reference_data.get("models", [])
            for model_data in models_data:
                try:
                    model_id = await self.db_client.upsert_model(model_data)
                    logger.debug(
                        f"ANALYTICS: Upserted model {model_data.get('name')} (ID: {model_id})"
                    )
                except Exception as e:
                    logger.error(
                        f"ANALYTICS: Failed to upsert model {model_data.get('name')}: {e}"
                    )
            deployments_data = reference_data.get("deployments", [])
            for deployment_data in deployments_data:
                try:
                    deployment_id = await self.db_client.upsert_deployment(
                        deployment_data
                    )
                    logger.debug(
                        f"ANALYTICS: Upserted deployment {deployment_data.get('deployment_id')} (ID: {deployment_id})"
                    )
                except Exception as e:
                    logger.error(
                        f"ANALYTICS: Failed to upsert deployment {deployment_data.get('deployment_id')}: {e}"
                    )
            logger.debug(
                f"ANALYTICS: Successfully processed {len(models_data)} models and {len(deployments_data)} deployments"
            )
        except Exception as e:
            logger.error(f"ANALYTICS: Failed to upsert reference data: {e}")

    async def cleanup(self) -> None:
        """Очистка ресурсов и завершение работы фонового обработчика.

        Корректно завершает работу логгера аналитики путем отмены фоновых задач,
        обработки оставшихся элементов очереди и закрытия соединений с базой данных.
        """
        try:
            if self._background_task and (not self._background_task.done()):
                self._background_task.cancel()
                try:
                    await self._background_task
                except asyncio.CancelledError:
                    pass
            if self.log_queue:
                remaining_items = []
                while not self.log_queue.empty():
                    try:
                        item = self.log_queue.get_nowait()
                        remaining_items.append(item)
                        self.log_queue.task_done()
                    except asyncio.QueueEmpty:
                        break
                for item in remaining_items:
                    try:
                        await self._process_log_item(item)
                    except Exception as e:
                        logger.error(
                            f"ANALYTICS: Failed to process remaining item: {e}"
                        )

            # Очистка кэша обработанных request_id
            if self.processed_requests:
                cache_size = len(self.processed_requests)
                self.processed_requests.clear()
                logger.debug(f"ANALYTICS: Cleared {cache_size} request IDs from cache")

            if self.db_client:
                await self.db_client.close()
            logger.info("ANALYTICS: RouterAnalyticsLogger cleanup completed")
        except Exception as e:
            logger.error(f"ANALYTICS: Error during cleanup: {e}", exc_info=True)
