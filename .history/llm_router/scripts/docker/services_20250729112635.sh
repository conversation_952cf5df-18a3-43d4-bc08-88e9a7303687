#!/bin/bash

# Docker Services Management Module
# Handles basic Docker service operations

# Load constants first, then common functions
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/../.." && pwd)"
source "$SCRIPT_DIR/scripts/common/constants.sh"
source "$SCRIPT_DIR/scripts/common/functions.sh"
source "$SCRIPT_DIR/scripts/common/paths.sh"
source "$SCRIPT_DIR/scripts/docker/sync.sh"
source "$SCRIPT_DIR/scripts/docker/containers.sh"

# =============================================================================
# DOCKER COMPOSE WRAPPER
# =============================================================================

# Docker Compose wrapper
dc() {
    local cmd="$1"; shift
    docker-compose -f "$COMPOSE_FILE" -p "$PROJECT_NAME" "$cmd" "$@"
}

# =============================================================================
# CORE SERVICE MANAGEMENT FUNCTIONS
# =============================================================================

# Build Docker images
docker_build() {
    log_info "Building Docker images..."
    
    validate_file_exists "$SCRIPT_DIR/$COMPOSE_FILE" "Docker Compose file"
    
    cd "$SCRIPT_DIR"
    dc build
    
    log_info "Images built successfully"
}

# Start services
docker_up() {
    local service="$1"
    
    log_info "Starting services..."
    
    validate_file_exists "$SCRIPT_DIR/$COMPOSE_FILE" "Docker Compose file"
    
    cd "$SCRIPT_DIR"
    if [[ -n "$service" ]]; then
        log_info "Starting service: $service"
        dc up -d "$service"
        log_info "Service $service started"
        echo "Service available at: http://localhost:4000"
    else
        log_info "Starting all services..."
        dc up -d
        log_info "All services started"
        show_all_service_urls
    fi
}

# Stop services
docker_stop() {
    local service="$1"
    
    log_info "Stopping services..."
    
    cd "$SCRIPT_DIR"
    if [[ -n "$service" ]]; then
        log_info "Stopping service: $service"
        dc stop "$service"
        log_info "Service $service stopped"
    else
        log_info "Stopping all services..."
        dc stop
        log_info "All services stopped"
    fi
}

# Stop and remove all services
docker_down() {
    log_info "Stopping and removing all services..."
    
    cd "$SCRIPT_DIR"
    
    # Capture both stdout and stderr to handle network removal warnings
    local output
    output=$(dc down 2>&1)
    local exit_code=$?
    
    # Check if the error is about network with active endpoints
    if echo "$output" | grep -q "network.*has active endpoints"; then
        # Show the regular output without the ERROR line
        echo "$output" | grep -v "ERROR: error while removing network"
        # Convert network error to warning
        local network_warning
        network_warning=$(echo "$output" | grep "ERROR: error while removing network" | sed 's/ERROR:/WARNING:/')
        if [[ -n "$network_warning" ]]; then
            echo "$network_warning"
            log_info "Network has active external containers - this is normal and can be ignored"
        fi
    else
        # For normal output or other errors, show as-is
        echo "$output"
        # Only return error code for non-network related errors
        if [[ $exit_code -ne 0 ]]; then
            return $exit_code
        fi
    fi
    
    log_info "All services stopped and removed"
}

# Restart services
docker_restart() {
    local service="$1"
    
    log_info "Restarting services..."
    
    cd "$SCRIPT_DIR"
    if [[ -n "$service" ]]; then
        log_info "Restarting service: $service"
        dc restart "$service"
        log_info "Service $service restarted"
        echo "Service available at: http://localhost:4000"
    else
        log_info "Restarting all services..."
        dc restart
        log_info "All services restarted"
        show_all_service_urls
    fi
}

# Remove dangling containers and images
docker_remove() {
    log_info "Removing dangling containers and images..."
    
    cd "$SCRIPT_DIR"
    
    # Remove stopped containers
    local containers
    containers=$(docker ps -a -q --filter "label=com.docker.compose.project=$PROJECT_NAME" 2>/dev/null || true)
    if [[ -n "$containers" ]]; then
        log_info "Removing stopped containers..."
        echo "$containers" | xargs docker rm 2>/dev/null || true
        log_info "Stopped containers removed"
    else
        log_info "No stopped containers to remove"
    fi
    
    # Remove old images
    local images
    images=$(docker images -q --filter "label=com.docker.compose.project=$PROJECT_NAME" 2>/dev/null || true)
    if [[ -n "$images" ]]; then
        log_info "Removing old images..."
        echo "$images" | xargs docker rmi 2>/dev/null || true
        log_info "Old images removed"
    else
        log_info "No old images to remove"
    fi
    
    log_info "Cleanup completed"
}

# Rebuild services (full rebuild with cleanup)
docker_rebuild() {
    log_info "Rebuilding and restarting all services..."
    echo "This will: stop services → remove containers → rebuild images → start services"
    echo ""
    
    echo "Step 1/4: Stopping and removing services..."
    docker_down
    echo ""
    
    echo "Step 2/4: Cleaning up containers and images..."
    docker_remove
    echo ""
    
    echo "Step 3/4: Building images..."
    docker_build
    echo ""
    
    echo "Step 4/4: Starting services..."
    docker_up
    echo ""
    
    log_info "Rebuild completed successfully!"
    echo "Services are now running with fresh images"
}

# Safe rebuild (only rebuild without removing data)
docker_rebuild_safe() {
    log_info "Safe rebuilding services (preserving data)..."
    echo "This will: stop services → rebuild images → start services"
    echo ""
    
    echo "Step 1/3: Stopping services..."
    docker_stop
    echo ""
    
    echo "Step 2/3: Building images..."
    docker_build
    echo ""
    
    echo "Step 3/3: Starting services..."
    docker_up
    echo ""
    
    log_info "Safe rebuild completed successfully!"
}

# Sync code to running containers
docker_sync() {
    log_info "Syncing code to running containers..."
    
    # Check if litellm container is running using docker-compose ps
    if ! validate_service_running "litellm"; then
        return 1
    fi
    
    # Get container ID for copying files
    local container_id
    container_id=$(docker-compose -p "$PROJECT_NAME" ps -q litellm)
    
    if [[ -z "$container_id" ]]; then
        log_error "Could not get container ID for litellm service"
        return 1
    fi
    
    # Sync entire llm_router folder
    log_info "Syncing entire llm_router folder to container..."
    docker cp "$SCRIPT_DIR/." "$container_id:$APP_ROUTER_DIR/"
    
    # Restart container to reload code
    log_info "Restarting LiteLLM container..."
    dc restart litellm
    
    log_info "Code synced and container restarted"
    echo "Service available at: http://localhost:4000"
}

# Show service logs (snapshot)
docker_logs() {
    local service="$1"
    
    cd "$SCRIPT_DIR"
    if [[ -n "$service" ]]; then
        log_info "Logs for service: $service (snapshot)"
        dc logs "$service"
    else
        log_info "Logs for all services (snapshot):"
        dc logs
    fi
}

# Follow service logs (real-time)
docker_logs_follow() {
    local service="$1"
    
    cd "$SCRIPT_DIR"
    if [[ -n "$service" ]]; then
        log_info "Following logs for service: $service (real-time)"
        dc logs -f "$service"
    else
        log_info "Following logs for all services (real-time):"
        dc logs -f
    fi
}

# Clear service logs
docker_logs_clear() {
    local service="$1"
    
    cd "$SCRIPT_DIR"
    if [[ -n "$service" ]]; then
        log_info "Clearing logs for service: $service"
        
        # Find container name pattern that matches the service
        local container_name
        case "$service" in
            litellm)
                # Look for containers that contain 'litellm' or 'router' in the name
                container_name=$(docker ps -a --format "{{.Names}}" | grep -E "(litellm|router)" | head -1)
                ;;
            *)
                # For other services, use the standard pattern first, then fallback to search
                container_name="${PROJECT_NAME}-${service}-1"
                if ! docker ps -a --format "{{.Names}}" | grep -q "^${container_name}$"; then
                    # Fallback: search for container name containing the service name
                    container_name=$(docker ps -a --format "{{.Names}}" | grep "$service" | head -1)
                fi
                ;;
        esac
        
        if [[ -z "$container_name" ]]; then
            log_error "No container found for service: $service"
            log_info "Available containers:"
            docker ps -a --format "table {{.Names}}\t{{.Status}}"
            return 1
        fi
        
        log_info "Found container: $container_name"
        
        # Get log path and clear it
        local log_path
        log_path=$(docker inspect --format='{{.LogPath}}' "$container_name" 2>/dev/null)
        
        if [[ -n "$log_path" && -f "$log_path" ]]; then
            # Try to clear the log file
            if sudo truncate -s 0 "$log_path" 2>/dev/null; then
                log_success "Cleared logs for $service ($container_name)"
            elif echo "" | sudo tee "$log_path" > /dev/null 2>&1; then
                log_success "Cleared logs for $service ($container_name)"
            else
                log_warning "Could not clear logs for $service - insufficient permissions"
                log_info "Try running with sudo or check Docker log rotation settings"
                log_info "Log file path: $log_path"
            fi
        else
            log_warning "Log file not found for $service ($container_name)"
        fi
    else
        log_info "Clearing logs for all services..."
        local cleared_count=0
        local failed_count=0
        
        # Get all container names
        local containers
        containers=$(docker ps -a --format "{{.Names}}")
        
        if [[ -z "$containers" ]]; then
            log_warning "No containers found"
            return 1
        fi
        
        for container_name in $containers; do
            local log_path
            log_path=$(docker inspect --format='{{.LogPath}}' "$container_name" 2>/dev/null)
            
            if [[ -n "$log_path" && -f "$log_path" ]]; then
                if sudo truncate -s 0 "$log_path" 2>/dev/null || echo "" | sudo tee "$log_path" > /dev/null 2>&1; then
                    log_info "OK Cleared logs for $container_name"
                    ((cleared_count++))
                else
                    log_warning "FAIL Failed to clear logs for $container_name"
                    ((failed_count++))
                fi
            else
                log_info "- No log file found for $container_name"
            fi
        done
        
        if [[ $cleared_count -gt 0 ]]; then
            log_success "Successfully cleared logs for $cleared_count container(s)"
        fi
        
        if [[ $failed_count -gt 0 ]]; then
            log_warning "$failed_count container(s) could not be cleared - check permissions"
        fi
    fi
}

# Show service status
docker_status() {
    log_info "Services status:"
    cd "$SCRIPT_DIR"
    dc ps
    echo ""
    
    # Additional status information
    echo "Service URLs:"
    show_all_service_urls
    echo ""
    
    # Check which services are responding
    echo "Service health check:"
    for service in "${SERVICES[@]}"; do
        local port="${SERVICE_PORTS[$service]}"
        if [[ -n "$port" ]]; then
            if nc -z localhost "$port" 2>/dev/null; then
                echo "  OK $service (port $port) - responding"
            else
                echo "  FAIL $service (port $port) - not responding"
            fi
        fi
    done
}

# =============================================================================
# HELP FUNCTION
# =============================================================================

show_docker_help() {
    cat << EOF
Docker Services Management Commands

USAGE:
  ./run.sh docker [COMMAND] [SERVICE]

BASIC COMMANDS:
  build                 Build Docker images
  up [SERVICE]          Start services (all services if no SERVICE specified)
  down                  Stop and remove all services
  stop [SERVICE]        Stop services (all services if no SERVICE specified)
  restart [SERVICE]     Restart services (all services if no SERVICE specified)
  remove                Remove dangling containers and images
  rebuild               Full rebuild (stop → remove → build → start)
  rebuild-safe          Safe rebuild (stop → build → start, preserve data)

SYNC COMMANDS:
  sync                  Full sync + restart + health check
  sync-full             Copy all files to container (no restart)
  sync-restart          Full sync + restart + health check

CONTAINER MANAGEMENT:
  container COMMAND     Manage individual containers (see: ./run.sh docker container help)

MONITORING COMMANDS:
  logs [SERVICE]        Show service logs (snapshot)
  logs-follow [SERVICE] Follow service logs (real-time)
  logs-clear [SERVICE]  Clear service logs
  status                Show service status and health

EXAMPLES:
  ./run.sh docker build                 # Build all images
  ./run.sh docker up                    # Start all services
  ./run.sh docker up litellm            # Start only LiteLLM service
  ./run.sh docker sync                  # Sync files + restart + health check
  ./run.sh docker sync-full             # Sync files only (no restart)
  ./run.sh docker container start predictor  # Start predictor container
  ./run.sh docker container stop database    # Stop database container
  ./run.sh docker container status           # Show all container status
  ./run.sh docker logs litellm          # Show LiteLLM logs
  ./run.sh docker logs-follow           # Follow all logs in real-time
  ./run.sh docker logs-clear litellm    # Clear LiteLLM logs
  ./run.sh docker status                # Show service status
  ./run.sh docker restart litellm       # Restart LiteLLM service
  ./run.sh docker down                  # Stop all services
  ./run.sh docker rebuild               # Full rebuild

SERVICES: litellm (4000), db (5432), prometheus (9090)

EOF
}

# =============================================================================
# COMMAND DISPATCHER
# =============================================================================

handle_docker_services_command() {
    local command="$1"
    shift
    
    # Initialize environment
    init_constants
    init_paths
    
    case "$command" in
        build)
            docker_build
            ;;
        up)
            docker_up "$@"
            ;;
        down)
            docker_down
            ;;
        stop)
            docker_stop "$@"
            ;;
        restart)
            docker_restart "$@"
            ;;
        remove)
            docker_remove
            ;;
        rebuild)
            docker_rebuild
            ;;
        rebuild-safe)
            docker_rebuild_safe
            ;;
        sync|sync-full|sync-restart)
            # Delegate sync commands to sync module
            handle_sync_command "$command" "$@"
            ;;
        container)
            # Delegate container management commands to container module
            handle_container_command "$@"
            ;;
        logs)
            docker_logs "$@"
            ;;
        logs-follow)
            docker_logs_follow "$@"
            ;;
        logs-clear)
            docker_logs_clear "$@"
            ;;
        status)
            docker_status
            ;;
        help|--help|-h)
            show_docker_help
            ;;
        *)
            show_error_with_usage "Unknown docker command: $command" "./run.sh docker help"
            return 1
            ;;
    esac
}

# If script is called directly, handle the command
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    handle_docker_services_command "$@"
fi 