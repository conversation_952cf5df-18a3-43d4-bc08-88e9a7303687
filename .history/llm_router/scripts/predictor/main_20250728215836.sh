#!/bin/bash

# Predictor Service Commands
# Main handler for predictor service operations

# Load common functions and constants
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/../.." && pwd)"
source "$SCRIPT_DIR/scripts/common/constants.sh"
source "$SCRIPT_DIR/scripts/common/functions.sh"

# =============================================================================
# PREDICTOR COMMAND HANDLERS
# =============================================================================

# Handle predictor main commands
handle_predictor_main_command() {
    local command="$1"
    shift
    
    case "$command" in
        health)
            handle_predictor_health "$@"
            ;;
        info)
            handle_predictor_info "$@"
            ;;
        predict)
            handle_predictor_predict "$@"
            ;;
        predict-chat)
            handle_predictor_predict_chat "$@"
            ;;
        predict-code)
            handle_predictor_predict_code "$@"
            ;;
        predict-custom)
            handle_predictor_predict_custom "$@"
            ;;
        *)
            echo "ERROR: Unknown predictor command: $command"
            echo "Use './run.sh predictor help' for available commands"
            return 1
            ;;
    esac
}

# =============================================================================
# HEALTH CHECK COMMANDS
# =============================================================================

# Check predictor service health
handle_predictor_health() {
    echo "🏥 PREDICTOR SERVICE HEALTH CHECK"
    echo "================================="
    echo ""
    
    # Check if running in Docker environment
    if docker ps > /dev/null 2>&1; then
        echo "Running health check in Docker container..."
        execute_docker_module "llm_router.predictor.predictor_client" "Checking predictor service health"
    else
        echo "Running health check locally..."
        cd "$SCRIPT_DIR"
        python3 -c "
from llm_router.predictor.predictor_client import create_predictor_client_from_env
import sys

try:
    client = create_predictor_client_from_env()
    if client.health_check():
        print('✅ Predictor service is healthy')
        sys.exit(0)
    else:
        print('❌ Predictor service health check failed')
        sys.exit(1)
except Exception as e:
    print(f'❌ Health check error: {e}')
    sys.exit(1)
finally:
    try:
        client.close()
    except:
        pass
"
    fi
}

# Get predictor service information
handle_predictor_info() {
    echo "ℹ️  PREDICTOR SERVICE INFORMATION"
    echo "================================="
    echo ""
    
    # Check if running in Docker environment
    if docker ps > /dev/null 2>&1; then
        echo "Getting service info in Docker container..."
        execute_docker_module "llm_router.predictor.predictor_client" "Getting predictor service information" --info
    else
        echo "Getting service info locally..."
        cd "$SCRIPT_DIR"
        python3 -c "
from llm_router.predictor.predictor_client import create_predictor_client_from_env
import json
import sys

try:
    client = create_predictor_client_from_env()
    info = client.get_service_info()
    if info:
        print('✅ Service Information:')
        print(json.dumps(info, indent=2))
        sys.exit(0)
    else:
        print('❌ Could not retrieve service information')
        sys.exit(1)
except Exception as e:
    print(f'❌ Error getting service info: {e}')
    sys.exit(1)
finally:
    try:
        client.close()
    except:
        pass
"
    fi
}

# =============================================================================
# PREDICTION TESTING COMMANDS
# =============================================================================

# Test prediction with default parameters
handle_predictor_predict() {
    echo "🔮 PREDICTOR SERVICE TEST"
    echo "========================="
    echo ""
    
    # Check if running in Docker environment
    if docker ps > /dev/null 2>&1; then
        echo "Running prediction test in Docker container..."
        execute_docker_module "llm_router.predictor.predictor_client" "Testing predictor service with default parameters" --test
    else
        echo "Running prediction test locally..."
        cd "$SCRIPT_DIR"
        python3 -c "
from llm_router.predictor.predictor_client import create_predictor_client_from_env
import sys

try:
    client = create_predictor_client_from_env()
    
    # Test with sample parameters
    predicted_time = client.predict(
        task_type='chat',
        prompt='Test prompt for timing prediction',
        hardware='gpu_a100',
        model='chat_instruct',
        instances=1,
        requests=1
    )
    
    print(f'✅ Prediction successful: {predicted_time:.3f} seconds')
    print(f'   Task: chat')
    print(f'   Hardware: gpu_a100')
    print(f'   Model: chat_instruct')
    print(f'   Instances: 1')
    print(f'   Requests: 1')
    sys.exit(0)
    
except Exception as e:
    print(f'❌ Prediction failed: {e}')
    sys.exit(1)
finally:
    try:
        client.close()
    except:
        pass
"
    fi
}

# Test chat task prediction
handle_predictor_predict_chat() {
    echo "💬 PREDICTOR CHAT TASK TEST"
    echo "==========================="
    echo ""
    
    # Check if running in Docker environment
    if docker ps > /dev/null 2>&1; then
        echo "Running chat prediction test in Docker container..."
        execute_docker_module "llm_router.predictor.predictor_client" "Testing chat task prediction" --test-chat
    else
        echo "Running chat prediction test locally..."
        cd "$SCRIPT_DIR"
        python3 -c "
from llm_router.predictor.predictor_client import create_predictor_client_from_env
import sys

try:
    client = create_predictor_client_from_env()
    
    # Test chat task with different parameters
    test_cases = [
        {'hardware': 'gpu_a100', 'model': 'chat_instruct'},
        {'hardware': 'gpu_v100', 'model': 'chat_instruct'},
        {'hardware': 'gpu_a100', 'model': 'chat_coder'},
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f'Test {i}: {case[\"hardware\"]} + {case[\"model\"]}')
        try:
            predicted_time = client.predict(
                task_type='chat',
                prompt='Hello, how are you today? Can you help me with a question?',
                hardware=case['hardware'],
                model=case['model'],
                instances=1,
                requests=1
            )
            print(f'  ✅ Prediction: {predicted_time:.3f} seconds')
        except Exception as e:
            print(f'  ❌ Failed: {e}')
        print()
    
    sys.exit(0)
    
except Exception as e:
    print(f'❌ Chat prediction test failed: {e}')
    sys.exit(1)
finally:
    try:
        client.close()
    except:
        pass
"
    fi
}

# Test code task prediction
handle_predictor_predict_code() {
    echo "💻 PREDICTOR CODE TASK TEST"
    echo "==========================="
    echo ""
    
    # Check if running in Docker environment
    if docker ps > /dev/null 2>&1; then
        echo "Running code prediction test in Docker container..."
        execute_docker_module "llm_router.predictor.predictor_client" "Testing code task prediction" --test-code
    else
        echo "Running code prediction test locally..."
        cd "$SCRIPT_DIR"
        python3 -c "
from llm_router.predictor.predictor_client import create_predictor_client_from_env
import sys

try:
    client = create_predictor_client_from_env()
    
    # Test code task with different parameters
    test_cases = [
        {'hardware': 'gpu_a100', 'model': 'chat_coder'},
        {'hardware': 'gpu_v100', 'model': 'chat_coder'},
        {'hardware': 'gpu_a100', 'model': 'chat_instruct'},
    ]
    
    code_prompt = '''def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

# Optimize this function for better performance'''
    
    for i, case in enumerate(test_cases, 1):
        print(f'Test {i}: {case[\"hardware\"]} + {case[\"model\"]}')
        try:
            predicted_time = client.predict(
                task_type='code',
                prompt=code_prompt,
                hardware=case['hardware'],
                model=case['model'],
                instances=1,
                requests=1
            )
            print(f'  ✅ Prediction: {predicted_time:.3f} seconds')
        except Exception as e:
            print(f'  ❌ Failed: {e}')
        print()
    
    sys.exit(0)
    
except Exception as e:
    print(f'❌ Code prediction test failed: {e}')
    sys.exit(1)
finally:
    try:
        client.close()
    except:
        pass
"
    fi
}

# Interactive custom prediction test
handle_predictor_predict_custom() {
    echo "🎛️  PREDICTOR CUSTOM PREDICTION TEST"
    echo "===================================="
    echo ""

    # Check if running in Docker environment
    if docker ps > /dev/null 2>&1; then
        echo "Running custom prediction test in Docker container..."
        echo "Note: Interactive mode not available in Docker. Using predefined test cases."

        execute_docker_module "llm_router.predictor.predictor_client" "Testing custom prediction parameters" --test-custom
    else
        echo "Running custom prediction test locally..."
        echo ""

        # Interactive parameter collection
        echo "Enter prediction parameters (press Enter for defaults):"
        echo ""

        read -p "Task type (chat/code) [chat]: " task_type
        task_type=${task_type:-chat}

        read -p "Hardware (gpu_a100/gpu_v100/etc.) [gpu_a100]: " hardware
        hardware=${hardware:-gpu_a100}

        read -p "Model (chat_instruct/chat_coder/etc.) [chat_instruct]: " model
        model=${model:-chat_instruct}

        read -p "Number of instances [1]: " instances
        instances=${instances:-1}

        read -p "Number of requests [1]: " requests
        requests=${requests:-1}

        read -p "Custom prompt (or press Enter for default): " custom_prompt
        if [[ -z "$custom_prompt" ]]; then
            if [[ "$task_type" == "code" ]]; then
                custom_prompt="def hello_world():\n    print('Hello, World!')\n\n# Add error handling to this function"
            else
                custom_prompt="Hello! Can you help me understand how machine learning works?"
            fi
        fi

        echo ""
        echo "Testing with parameters:"
        echo "  Task type: $task_type"
        echo "  Hardware: $hardware"
        echo "  Model: $model"
        echo "  Instances: $instances"
        echo "  Requests: $requests"
        echo "  Prompt: ${custom_prompt:0:50}..."
        echo ""

        cd "$SCRIPT_DIR"
        python3 -c "
from llm_router.predictor.predictor_client import create_predictor_client_from_env
import sys

try:
    client = create_predictor_client_from_env()

    predicted_time = client.predict(
        task_type='$task_type',
        prompt='$custom_prompt',
        hardware='$hardware',
        model='$model',
        instances=$instances,
        requests=$requests
    )

    print(f'✅ Custom prediction successful!')
    print(f'   Predicted time: {predicted_time:.3f} seconds')
    print(f'   Task type: $task_type')
    print(f'   Hardware: $hardware')
    print(f'   Model: $model')
    print(f'   Instances: $instances')
    print(f'   Requests: $requests')
    sys.exit(0)

except Exception as e:
    print(f'❌ Custom prediction failed: {e}')
    sys.exit(1)
finally:
    try:
        client.close()
    except:
        pass
"
    fi
}
