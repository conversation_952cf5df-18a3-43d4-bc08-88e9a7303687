#!/bin/bash

# Constants and configuration for LLM Router scripts
# This file contains all constants, paths, and configuration mappings

# Guard against multiple loading
if [[ -n "$CONSTANTS_LOADED" ]]; then
    return 0
fi
readonly CONSTANTS_LOADED=1

# =============================================================================
# PROJECT CONFIGURATION
# =============================================================================

# Project settings (only set if not already defined)
if [[ -z "$PROJECT_NAME" ]]; then
    PROJECT_NAME="llm_router"
fi

if [[ -z "$COMPOSE_FILE" ]]; then
    COMPOSE_FILE="docker-compose.yml"
fi

# Default configuration file path
if [[ -z "$DEFAULT_CONFIG_FILE" ]]; then
    DEFAULT_CONFIG_FILE="router_litellm/litellm.config.yaml"
fi

# Directory structure
if [[ -z "$UTILS_DIR" ]]; then
    UTILS_DIR="utils"
fi

if [[ -z "$TESTS_DIR" ]]; then
    TESTS_DIR="tests"
fi

if [[ -z "$DEBUG_DIR" ]]; then
    DEBUG_DIR="debug"
fi

if [[ -z "$SCRIPTS_DIR" ]]; then
    SCRIPTS_DIR="scripts"
fi

if [[ -z "$COMMON_DIR" ]]; then
    COMMON_DIR="common"
fi

if [[ -z "$CONFIG_BACKUPS_DIR" ]]; then
    CONFIG_BACKUPS_DIR="config_backups"
fi

if [[ -z "$ANALYTICS_DIR" ]]; then
    ANALYTICS_DIR="analytics"
fi

# =============================================================================
# DOCKER CONTAINER PATHS
# =============================================================================

# Docker container paths
if [[ -z "$APP_CONTAINER_ROOT" ]]; then
    APP_CONTAINER_ROOT="/app"
fi

if [[ -z "$APP_ROUTER_DIR" ]]; then
    APP_ROUTER_DIR="/app/llm_router"
fi

if [[ -z "$APP_TESTS_DIR" ]]; then
    APP_TESTS_DIR="/app/llm_router/tests"
fi

if [[ -z "$APP_UTILS_DIR" ]]; then
    APP_UTILS_DIR="/app/llm_router/utils"
fi

if [[ -z "$APP_DEBUG_DIR" ]]; then
    APP_DEBUG_DIR="/app/llm_router/debug"
fi

if [[ -z "$APP_ANALYTICS_MIGRATIONS_DIR" ]]; then
    APP_ANALYTICS_MIGRATIONS_DIR="/app/llm_router/analytics/migrations"
fi

if [[ -z "$APP_ANALYTICS_SCHEMA_PATH" ]]; then
    APP_ANALYTICS_SCHEMA_PATH="llm_router/analytics/schema.prisma"
fi

if [[ -z "$APP_ROOT_TESTS_DIR" ]]; then
    APP_ROOT_TESTS_DIR="/app/tests"
fi

# =============================================================================
# SERVICE CONFIGURATION
# =============================================================================

# Service ports mapping
declare -gA SERVICE_PORTS=(
    [litellm]=4000
    [db]=5432
    [prometheus]=9090
)

# Service names
if [[ -z "${SERVICES}" ]]; then
    SERVICES=(litellm db prometheus)
fi

# Container names (can be overridden via environment variables)
if [[ -z "$LITELLM_CONTAINER_NAME" ]]; then
    LITELLM_CONTAINER_NAME="filin-litellm-router"
fi

if [[ -z "$DATABASE_CONTAINER_NAME" ]]; then
    DATABASE_CONTAINER_NAME="litellm_db"
fi

if [[ -z "$PROMETHEUS_CONTAINER_NAME" ]]; then
    PROMETHEUS_CONTAINER_NAME="llm_router_prometheus_1"
fi

# GPUStack container names
if [[ -z "$GPUSTACK_SERVER_CONTAINER_NAME" ]]; then
    GPUSTACK_SERVER_CONTAINER_NAME="gpustack-server"
fi

# =============================================================================
# SCRIPT PATH MAPPINGS
# =============================================================================

# Local script paths (corrected based on actual file locations)
declare -gA LOCAL_SCRIPTS=(
    # Authentication scripts
    ["check_gpustack_auth"]="utils/check_gpustack_auth.py"
    ["check_litellm_auth"]="utils/check_litellm_auth.py"
    ["check_gpustack_key"]="utils/check_gpustack_key.py"
    
    # Configuration scripts
    ["config_manager"]="config/config_manager.py"
    ["config_manager_models"]="config/config_manager_models.py"
    ["service_config"]="config/service_config.py"
    ["check_db_router_settings"]="utils/check_db_router_settings.py"
    
    # GPUStack management scripts
    ["update_config"]="config/update_config.py"
    ["create_gpustack_models"]="config/create_gpustack_models.py"
    ["update_gpustack_key"]="config/update_gpustack_key.py"
    ["list_gpustack_models"]="router_gpustack/list_gpustack_models.py"
    
    # Monitoring and validation scripts
    ["monitor_routing"]="utils/monitor_routing.py"
    ["validate_system"]="utils/validate_system.py"
    
    # Debug scripts
    ["debug_config"]="debug/debug_config.py"
)

# Docker script paths (scripts that run inside Docker containers)
declare -gA DOCKER_SCRIPTS=(
    # Test scripts
    ["test_predictor_service"]="$APP_TESTS_DIR/predictor/test_predictor_service.py"
    ["test_predictor_managed_models"]="$APP_TESTS_DIR/predictor/test_predictor_managed_models.py"
    ["test_predictor_debug"]="$APP_TESTS_DIR/predictor/test_predictor_debug.py"
    ["test_predictor_infinity"]="$APP_TESTS_DIR/predictor/test_predictor_infinity.py"
    ["test_router_predictor_integration"]="$APP_TESTS_DIR/test_router_predictor_integration.py"
    ["test_llm_router"]="$APP_TESTS_DIR/unit/test_llm_router.py"
    ["test_least_busy_integration"]="$APP_TESTS_DIR/unit/test_least_busy_integration.py"
    ["test_load_balancing_simple"]="$APP_TESTS_DIR/load_balancing/test_load_balancing_simple.py"
    ["test_load_balancing_advanced"]="$APP_TESTS_DIR/load_balancing/test_load_balancing_advanced.py"
    ["test_high_load_balancing"]="$APP_TESTS_DIR/load_balancing/test_high_load_balancing.py"
    
    # Debug scripts
    ["debug_router"]="$APP_ROUTER_DIR/debug/debug_router.py"
    ["debug_least_busy"]="$APP_ROUTER_DIR/debug/debug_least_busy.py"
    ["debug_endpoint_selection"]="$APP_ROUTER_DIR/debug/debug_endpoint_selection.py"
    ["debug_cache_counters"]="$APP_ROUTER_DIR/debug/debug_cache_counters.py"
    ["debug_router_initialization"]="$APP_ROUTER_DIR/debug/debug_router_initialization.py"
    ["debug_cache_architecture"]="$APP_ROUTER_DIR/debug/debug_cache_architecture.py"
    ["debug.test_cache_initialization"]="$APP_ROUTER_DIR/debug/test_cache_initialization.py"
    ["debug_cache"]="$APP_ROUTER_DIR/debug/debug_cache.py"
    
    # Integration test scripts
    ["run_integration_tests"]="$APP_TESTS_DIR/run_integration_tests.py"
)

# Scripts that exist in tests/ directory but are called locally
declare -gA LOCAL_TEST_SCRIPTS=(
    ["simple_test"]="tests/simple_test.py"
    ["advanced_test"]="tests/advanced_test.py"
)

# Python modules (должны запускаться через python -m)
declare -gA LOCAL_MODULES=(
    # Пустой - все модули теперь в Docker
)

# Docker modules (должны запускаться через python -m в Docker)
declare -gA DOCKER_MODULES=(
    # Пустой - все модули теперь работают на хосте
)

# =============================================================================
# COMMAND CATEGORIES
# =============================================================================

# Docker commands
DOCKER_COMMANDS=(
    "build" "up" "down" "stop" "restart" "remove" "rebuild" "rebuild-safe"
    "sync" "logs" "logs-follow" "status"
)

# Custom configuration commands
CUSTOM_CONFIG_COMMANDS=(
    "env" "mount" "cmd" "default"
)

# Configuration management commands
CONFIG_COMMANDS=(
    "check" "list" "list-settings" "update" "update-models" "add-models"
    "clear-models" "update-router" "router-settings" "check-db"
    "services" "services-internal" "services-external"
)

# Test categories
TEST_CATEGORIES=(
    "health" "all" "models" "predictor" "gpustack" "litellm" "router" "balancing"
)

# Debug commands
DEBUG_COMMANDS=(
    "status" "reset-all" "reset" "endpoints" "counters" "router-init" "cache-arch"
)

# GPUStack commands
GPUSTACK_COMMANDS=(
    "list" "create" "update"
)

# =============================================================================
# HELP MESSAGES
# =============================================================================

# Category descriptions for main help
declare -gA CATEGORY_DESCRIPTIONS=(
    ["docker"]="Docker service management (build, up, down, logs, etc.)"
    ["custom"]="Custom configuration modes (env, mount, cmd, default)"
    ["config"]="Configuration management (models, settings, validation)"
    ["update"]="Configuration and credential updates"
    ["test"]="Testing commands (health, models, router, balancing)"
    ["gpustack"]="GPUStack operations (models, credentials)"
    ["debug"]="Debug and diagnostics tools"
    ["monitor"]="Monitoring and analysis tools"
    ["analytics"]="Analytics verification and reporting system"
)

# =============================================================================
# DEFAULT VALUES
# =============================================================================

# Default test parameters
DEFAULT_HIGH_LOAD_REQUESTS=30
DEFAULT_HIGH_LOAD_CONCURRENT=8
DEFAULT_STRESS_REQUESTS=100
DEFAULT_STRESS_CONCURRENT=15
DEFAULT_MONITOR_DURATION=10

# =============================================================================
# VALIDATION PATTERNS
# =============================================================================

# Valid service names for validation
VALID_SERVICES="litellm|db|prometheus"

# Valid config file extensions
VALID_CONFIG_EXTENSIONS="yaml|yml"

# =============================================================================
# ERROR MESSAGES
# =============================================================================

ERROR_MISSING_ENV_FILE="ERROR: .env file not found. Please create .env file with required environment variables."
ERROR_MISSING_COMPOSE_FILE="ERROR: docker-compose.yml file not found."
ERROR_SERVICE_NOT_RUNNING="ERROR: Service is not running. Start it first with: ./run.sh up"
ERROR_INVALID_COMMAND="ERROR: Unknown command. Use './run.sh help' to see available commands."
ERROR_MISSING_PARAMETER="ERROR: Required parameter missing."

# =============================================================================
# SUCCESS MESSAGES
# =============================================================================

SUCCESS_OPERATION_COMPLETED="Operation completed successfully"
SUCCESS_SERVICE_STARTED="Service started successfully"
SUCCESS_SERVICE_STOPPED="Service stopped successfully"
SUCCESS_CONFIG_UPDATED="Configuration updated successfully"

# =============================================================================
# INITIALIZATION FUNCTION
# =============================================================================

# Initialize all constants and validate environment
init_constants() {
    # Set script directory if not already set
    if [[ -z "$SCRIPT_DIR" ]]; then
        SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
        # Go up two levels if we're in common/ directory (scripts/common -> llm_router)
        if [[ "$(basename "$SCRIPT_DIR")" == "common" ]]; then
            SCRIPT_DIR="$(dirname "$(dirname "$SCRIPT_DIR")")"
        # Go up one level if we're in scripts/ directory
        elif [[ "$(basename "$SCRIPT_DIR")" == "scripts" ]]; then
            SCRIPT_DIR="$(dirname "$SCRIPT_DIR")"
        fi
    fi
    
    # Set environment file path - .env file is in llm_router directory (SCRIPT_DIR)
    ENV_FILE="${ENV_FILE:-$SCRIPT_DIR/.env}"
    
    # Set backup directory
    BACKUP_DIR="${BACKUP_DIR:-$SCRIPT_DIR/$CONFIG_BACKUPS_DIR}"
    
    # Export variables (skip ones)
    export SCRIPT_DIR ENV_FILE BACKUP_DIR
    # PROJECT_NAME and COMPOSE_FILE are already readonly, don't export them
    
        return 0
}

# =============================================================================
# ANALYTICS CONSTANTS
# =============================================================================

# Analytics commands
ANALYTICS_COMMANDS=(
    "health" "database" "data" "reports" "test" "maintenance" "export"
)

# Analytics health subcommands  
ANALYTICS_HEALTH_COMMANDS=("check" "monitor")

# Analytics database subcommands
ANALYTICS_DATABASE_COMMANDS=("status" "schema" "tables" "fetch" "drop")

# Analytics data subcommands
ANALYTICS_DATA_COMMANDS=("counts" "consistency" "quality" "seed" "cleanup")

# Analytics reports subcommands
ANALYTICS_REPORTS_COMMANDS=("summary" "decisions" "accuracy" "performance" "quality")

# Analytics test subcommands
ANALYTICS_TEST_COMMANDS=("e2e" "failure")

# Analytics maintenance subcommands
ANALYTICS_MAINTENANCE_COMMANDS=("setup" "reindex" "vacuum")

# Analytics export subcommands
ANALYTICS_EXPORT_COMMANDS=("training-data" "stats" "list-models" "list-providers")

# Exit codes for analytics
readonly EXIT_SUCCESS=0
readonly EXIT_CONFIG_ERROR=1
readonly EXIT_DATABASE_ERROR=2
readonly EXIT_SCHEMA_ERROR=3
readonly EXIT_PARTIAL_SUCCESS=4
readonly EXIT_DATA_ERROR=10
readonly EXIT_VALIDATION_ERROR=11
readonly EXIT_CONSISTENCY_ERROR=12
readonly EXIT_SYSTEM_ERROR=20
readonly EXIT_INTEGRATION_ERROR=21
readonly EXIT_PERFORMANCE_ERROR=22

ANALYTICS_UTILS_DIR="${APP_ROUTER_DIR}/analytics/utils"

# Analytics Python scripts paths
declare -gA ANALYTICS_PYTHON_SCRIPTS=(
    ["database_checker"]="${ANALYTICS_UTILS_DIR}/database_checker.py"
    ["health_checker"]="${ANALYTICS_UTILS_DIR}/health_checker.py"
    ["data_validator"]="${ANALYTICS_UTILS_DIR}/data_validator.py"
    ["report_generator"]="${ANALYTICS_UTILS_DIR}/report_generator.py"
    ["summary_generator"]="${ANALYTICS_UTILS_DIR}/summary_generator.py"
    ["test_data_generator"]="${ANALYTICS_UTILS_DIR}/test_data_generator.py"
    ["e2e_tester"]="${ANALYTICS_UTILS_DIR}/e2e"
)

# Analytics directory paths  

# Default analytics parameters
DEFAULT_ANALYTICS_PERIOD_HOURS=24
DEFAULT_ANALYTICS_TIMEOUT=120
DEFAULT_MAX_ANALYTICS_LAG=5
DEFAULT_E2E_TIMEOUT=120
DEFAULT_SEED_COUNT=100

# Analytics category description for main help
readonly ANALYTICS_DESCRIPTION="Analytics verification and reporting system" 

# Validate that all required constants are set
validate_constants() {
    local required_vars=(
        "SCRIPT_DIR" "ENV_FILE" "BACKUP_DIR" "PROJECT_NAME" "COMPOSE_FILE"
    )
    
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var}" ]]; then
            echo "ERROR: Required variable $var is not set"
            return 1
        fi
    done
    
    return 0
} 