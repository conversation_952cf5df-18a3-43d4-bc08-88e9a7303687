#!/bin/bash

# LLM Router Launch Script - Modular Version
# Main entry point for all LLM Router operations

set -e

# =============================================================================
# INITIALIZATION
# =============================================================================

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Load common functions and constants
source "$SCRIPT_DIR/scripts/common/constants.sh"
source "$SCRIPT_DIR/scripts/common/functions.sh"
source "$SCRIPT_DIR/scripts/common/paths.sh"

# Initialize environment
init_constants
init_common_environment

# =============================================================================
# MAIN HELP FUNCTION
# =============================================================================

show_main_help() {
    cat << EOF
LLM Router Launch Script - Usage: ./run.sh [CATEGORY] [COMMAND] [OPTIONS]

CATEGORIES:
  docker                ${CATEGORY_DESCRIPTIONS[docker]}
  custom                ${CATEGORY_DESCRIPTIONS[custom]}
  config                ${CATEGORY_DESCRIPTIONS[config]}
  update                ${CATEGORY_DESCRIPTIONS[update]}
  test                  ${CATEGORY_DESCRIPTIONS[test]}
  gpustack              ${CATEGORY_DESCRIPTIONS[gpustack]}
  debug                 ${CATEGORY_DESCRIPTIONS[debug]}
  monitor               ${CATEGORY_DESCRIPTIONS[monitor]}
  predictor             ${CATEGORY_DESCRIPTIONS[predictor]}
  analytics             ${CATEGORY_DESCRIPTIONS[analytics]} (supports --host flag)

EXAMPLES:
  ./run.sh docker up                    # Start all services
  ./run.sh docker build                 # Build images
  ./run.sh docker down                  # Stop services
  ./run.sh docker logs                  # Show logs
  ./run.sh docker status                # Show service status
  ./run.sh update config                # Update GPUSTACK_KEY in config
  ./run.sh update gpustack              # Update all GPUStack credentials
  ./run.sh update gpustack --yes        # Update all GPUStack credentials without prompts
  ./run.sh test endpoint                # Test health endpoint
  ./run.sh test models                  # Test all available models
  ./run.sh test model chat_instruct     # Test specific model
  ./run.sh config check                 # Check configuration
  ./run.sh debug status                 # Show debug status
  ./run.sh monitor routing              # Monitor routing decisions
  ./run.sh predictor health             # Check predictor service health
  ./run.sh predictor predict            # Test prediction functionality
  ./run.sh gpustack credentials test    # Test GPUStack authentication
  ./run.sh analytics health check       # Check analytics system health
  ./run.sh analytics migrate status --host   # Host-based migration status
  ./run.sh analytics database status --host  # Host-based database check

HELP:
  ./run.sh help [CATEGORY]              # Show help for specific category
  ./run.sh [CATEGORY] help              # Alternative help syntax

SERVICES: litellm (4000), db (5432), prometheus (9090)

For detailed help on any category, use:
  ./run.sh help docker
  ./run.sh help config
  ./run.sh help update
  ./run.sh help test
  ./run.sh help debug
  ./run.sh help predictor
  ./run.sh help analytics

EOF
}

# Show category-specific help
show_category_help() {
    local category="$1"
    
    case "$category" in
        docker)
            source "$SCRIPT_DIR/scripts/docker/services.sh"
            show_docker_help
            ;;
        custom)
            source "$SCRIPT_DIR/scripts/docker/custom.sh"
            show_custom_help
            ;;
        config)
            show_config_help
            ;;
        update)
            show_update_help
            ;;
        test)
            show_test_help
            ;;
        gpustack)
            show_gpustack_help
            ;;
        debug)
            show_debug_help
            ;;
        monitor)
            show_monitor_help
            ;;
        predictor)
            show_predictor_help
            ;;
        analytics)
            source "$SCRIPT_DIR/scripts/analytics/main.sh"
            show_analytics_help
            ;;
        *)
            echo "ERROR: Unknown category: $category"
            echo ""
            show_main_help
            return 1
            ;;
    esac
}

# =============================================================================
# CATEGORY HELP FUNCTIONS
# =============================================================================

show_update_help() {
    cat << EOF
Update Commands

USAGE:
  ./run.sh update [COMMAND] [OPTIONS]

CONFIGURATION UPDATES:
      config                Update GPUSTACK_KEY in router_litellm/litellm.config.yaml

GPUSTACK CREDENTIAL UPDATES:
  gpustack              Update all GPUStack credentials
  gpustack token        Update GPUSTACK_TOKEN in .env
  gpustack key          Update GPUSTACK_KEY (API key)  
  gpustack password     Update GPUSTACK_PASSWORD in .env

OPTIONS:
  --yes                 Skip confirmation prompts (for gpustack command)

EXAMPLES:
  ./run.sh update config                # Update GPUSTACK_KEY in config file
  ./run.sh update gpustack              # Update all GPUStack credentials
  ./run.sh update gpustack --yes        # Update all GPUStack credentials without prompts
  ./run.sh update gpustack token        # Update only GPUSTACK_TOKEN
  ./run.sh update gpustack key          # Update only GPUSTACK_KEY
  ./run.sh update gpustack password     # Update only GPUSTACK_PASSWORD

NOTES:
  - config updates the GPUSTACK_KEY in router_litellm/litellm.config.yaml
  - gpustack commands update credentials in .env file
  - Use 'gpustack' without additional parameters to update all credentials
  - Use --yes flag to skip confirmation prompts for automated scripts
  - GPUStack credential updates require $GPUSTACK_SERVER_CONTAINER_NAME container to be running

EOF
}

show_config_help() {
    cat << EOF
Configuration Management Commands

USAGE:
  ./run.sh config [COMMAND] [OPTIONS]

BASIC COMMANDS:
  check                 Check router configuration locally
  update [FILE]         Update configuration from YAML file
  services              Check service URLs (auto-detect context)

MODEL MANAGEMENT:
  models list           Show current models from database
  models update [FILE]  Update models to database
  models add [FILE]     Add models to database
  models clear          Clear all models from database

SETTINGS MANAGEMENT:
  settings list         Show current settings from database
  settings update-router [FILE]  Update router settings
  settings router       Show router settings

VALIDATION:
  validation check      Check configuration locally
  validation check-db   Check database settings
  validation check-system  Full system validation

SERVICE CONFIGURATION:
  services-internal     Show Docker internal service URLs
  services-external     Show host machine service URLs

EXAMPLES:
  ./run.sh config check                     # Check configuration
  ./run.sh config update                    # Update from router_litellm/litellm.config.yaml
  ./run.sh config models list               # List models
  ./run.sh config models update             # Update models from file
  ./run.sh config settings router           # Show router settings
  ./run.sh config validation check-system   # Full validation
  ./run.sh config services                  # Auto-detect and show service URLs

NOTES:
  - check runs configuration validation locally (no Docker required)
  - update applies configuration changes to the system
  - models commands manage model configurations in database
  - settings commands manage router settings
  - validation commands perform system health checks
  - services auto-detects whether you're using Docker or local setup

EOF
}

show_test_help() {
    cat << EOF
Testing Commands

USAGE:
  ./run.sh test [CATEGORY] [COMMAND] [OPTIONS]

BASIC TESTS:
  endpoint              Test health endpoint
  models                Test all available models
  model MODEL_NAME      Test specific model
  quick-check [CATEGORY] Quick OK/FAIL checks by category (system, analytics, predictor, least-busy, gpustack, routing, all)

MODEL TESTS:
  models connectivity   Test predictor service connectivity
  models functionality  Test predictor service functionality
  models managed-models Test predictor with managed models configuration
  models debug          Debug predictor infinity values with router parameters
  models infinity       Simple test for predictor infinity issue
  models integration    Test router-predictor integration
  models predictor-all  Run all predictor tests
  models specific MODEL Test specific model by name
  models all-available  Test all available models

ROUTER TESTS:
  router simple         Simple router tests
  router unit           Internal unit tests (with mocks)
  router integration    Integration tests
  router advanced       Advanced tests
  router least-busy     Least Busy integration tests
  router load-balancing Load balancing functionality tests
  router all            Run all router tests

BALANCING TESTS:
  balancing simple      Basic load balancing tests
  balancing advanced    Advanced load balancing tests
  balancing high-load   High-load balancing tests
  balancing stress      Stress testing
  balancing all         Run all balancing tests

GPUSTACK INTEGRATION TESTS:
  gpustack api          Test GPUStack API integration (comprehensive)
  gpustack data-update  Test GPUStack data update functionality
  gpustack health       Test GPUStack integration health
  gpustack auth         Test GPUStack authentication only
  gpustack cache        Test GPUStack cache functionality
  gpustack all          Run all GPUStack integration tests

AUTHENTICATION TESTS:
  auth gpustack         Test GPUStack authentication
  auth litellm          Test LiteLLM authentication
  auth all              Test all authentication methods

ANALYTICS TESTS:
  analytics sql         Test Analytics SQL operations (6 tables with asyncpg)
                        Validates LiteLLM_Models, LiteLLM_Deployments, etc.
                        Tests RouterAnalyticsLogger callback integration
                        Requires Docker environment (SQL tests require container)

RACE CONDITION TESTS:
  race-condition quick [MODEL]      Quick race condition test (20 requests)
  race-condition model MODEL        Test specific model for race condition
  race-condition all                Test all models for race condition  
  race-condition detailed MODEL [N] Detailed test with N requests

PRISMA CLIENT TESTS:
  prisma status         Test Prisma client connection status (connect/disconnect)
  prisma query "SQL"    Execute custom SQL query via Prisma client
  prisma schema         Test analytics schema tables and access
  prisma mock           Test mock mode functionality
  prisma context        Test context manager functionality
  prisma benchmark [N]  Performance benchmark (default: 10 iterations)
  prisma stress [N]     Stress test with concurrent connections (default: 5)
  prisma all            Run all Prisma client tests

BACKWARD COMPATIBILITY:
  health                Alias for 'endpoint'
  all                   Alias for 'models' 
  MODEL_NAME            Direct model testing (e.g. chat_instruct)

EXAMPLES:
  ./run.sh test endpoint                    # Test health endpoint
  ./run.sh test models                      # Test all available models
  ./run.sh test model chat_instruct         # Test specific model
  ./run.sh test chat_instruct               # Test specific model (backward compatibility)
  ./run.sh test health                      # Test health endpoint (backward compatibility)
  ./run.sh test quick-check system          # Quick system check (Docker services, health)
  ./run.sh test quick-check analytics       # Quick analytics check (DB, E2E, data)
  ./run.sh test quick-check all             # Quick check of all categories
  ./run.sh test models connectivity         # Test predictor connectivity
  ./run.sh test models functionality        # Test predictor functionality
  ./run.sh test models managed-models       # Test with managed models
  ./run.sh test models debug                # Debug infinity values
  ./run.sh test models infinity             # Test infinity issue
  ./run.sh test models integration          # Test integration
  ./run.sh test models predictor-all        # Run all predictor tests
  ./run.sh test models specific chat_instruct # Test specific model by name
  ./run.sh test models all-available        # Test all available models
  ./run.sh test router simple               # Test simple router functionality
  ./run.sh test router least-busy           # Test least busy integration
  ./run.sh test balancing high-load         # Test high-load balancing
  ./run.sh test balancing stress 100 20     # Custom stress test
  ./run.sh test auth all                    # Test all authentication
  ./run.sh test gpustack api                # Test GPUStack API integration
  ./run.sh test gpustack data-update        # Test data update functionality
  ./run.sh test gpustack health             # Test integration health
  ./run.sh test gpustack all                # Run all GPUStack tests
  ./run.sh test analytics sql               # Test Analytics SQL operations (Docker)
  ./run.sh test race-condition quick        # Quick race condition test with chat_instruct
  ./run.sh test race-condition model chat_coder  # Test race condition for chat_coder
  ./run.sh test race-condition all          # Test race condition for all models
  ./run.sh test race-condition detailed chat_instruct 50  # Detailed test with 50 requests
  ./run.sh test prisma status               # Test Prisma client connection
  ./run.sh test prisma query "SELECT 1"     # Execute custom SQL query
  ./run.sh test prisma schema               # Test analytics schema
  ./run.sh test prisma benchmark 20         # Performance benchmark
  ./run.sh test prisma stress 10            # Stress test
  ./run.sh test prisma all                  # Run all Prisma tests

GPUSTACK INTEGRATION TESTS:
  The GPUStack integration tests validate the connection and data synchronization
  with GPUStack physical machines for enhanced routing decisions:

  - api: Comprehensive API integration test including data fetching and processing
  - data-update: Test data update cycles, cache management, and refresh functionality
  - health: Quick health check of GPUStack integration components
  - auth: Authentication testing with GPUStack API
  - cache: Cache functionality and invalidation testing
  - all: Complete test suite for GPUStack integration

  Requirements:
  - GPUSTACK_INTERNAL environment variable (e.g., http://host.docker.internal:80/v1)
  - GPUSTACK_KEY environment variable with valid API key

PRISMA CLIENT TESTS:
  The Prisma client tests validate database connectivity and operations
  using the new AnalyticsPrismaClient for analytics database operations:

  - status: Test connection status with connect/disconnect cycle
  - query: Execute custom SQL queries via Prisma client for debugging
  - schema: Validate analytics schema tables and data access
  - mock: Test mock mode functionality without database connection
  - context: Test context manager functionality for automatic connection management
  - benchmark: Performance benchmark with configurable iterations (default: 10)
  - stress: Concurrent connections stress test (default: 5 connections)
  - all: Complete test suite for Prisma client operations

  Requirements:
  - Docker container must be running (./run.sh docker up)
  - Database must be accessible with proper credentials
  - Prisma client must be properly configured

PREDICTOR SERVICE TESTS:
  The predictor service tests provide comprehensive testing of the external
  ML prediction service that helps with load balancing decisions:

  - connectivity: Basic service connectivity and health checks
  - functionality: Full service functionality including predictions
  - managed-models: Tests with your configured managed models
  - debug: Debug tools for troubleshooting infinity prediction values
  - infinity: Specific tests for infinity issue scenarios
  - integration: Router-predictor integration with real requests
  - predictor-all: Comprehensive test suite running all predictor tests

NOTES:
  - Most tests require LiteLLM service to be running
  - Predictor tests require predictor service to be accessible
  - GPUStack tests require GPUSTACK_INTERNAL and GPUSTACK_KEY environment variables
  - Model tests use the health testing framework
  - All tests provide detailed output and summaries
  - Use 'predictor-all' for comprehensive predictor service validation
  - Use 'gpustack all' for comprehensive GPUStack integration validation
  - Use 'quick-check' for fast OK/FAIL validation without detailed output

EOF
}

show_gpustack_help() {
    cat << EOF
GPUStack Operations Commands

USAGE:
  ./run.sh gpustack [CATEGORY] [COMMAND] [OPTIONS]

HARDWARE INFORMATION:
  workers list          List all workers with hardware summary
  workers show          Show detailed worker information
  instances list        List all model instances
  instances show        Show detailed instance information
  info summary          Show complete cluster summary

MODEL OPERATIONS:
  models list           List all models in GPUStack
  models create [FILE]  Create models from configuration
  models show MODEL     Show model details

CREDENTIAL MANAGEMENT:
  credentials update    Update all credentials (password → token → key)
  credentials token     Update token only
  credentials password  Update password only
  credentials key       Update API key only
  credentials test      Test authentication
  credentials status    Show credential status

EXAMPLES:
  ./run.sh gpustack workers list           # List workers with hardware
  ./run.sh gpustack instances list         # List model instances
  ./run.sh gpustack info summary           # Complete cluster overview
  ./run.sh gpustack models list            # List models
  ./run.sh gpustack models create          # Create models
  ./run.sh gpustack credentials update     # Update all credentials
  ./run.sh gpustack credentials test       # Test authentication

EOF
}

show_debug_help() {
    cat << EOF
Debug and Diagnostics Commands

USAGE:
  ./run.sh debug [COMMAND] [OPTIONS]

ROUTER DEBUG:
  router status         Show request tracker status
  router reset-all      Reset all counters
  router reset DEPLOY   Reset specific deployment
  router local          Debug configuration locally

CACHE DEBUG:
  cache init            Test cache manager initialization
  cache status          Show cache status in running router
  cache force           Force cache update via API
  cache health          Test GPUStack API connectivity

EXAMPLES:
  ./run.sh debug router status              # Show tracker status
  ./run.sh debug router reset-all           # Reset all counters
  ./run.sh debug router local               # Local debug
  ./run.sh debug cache init                 # Test cache initialization
  ./run.sh debug cache status               # Check cache status

EOF
}

show_monitor_help() {
    cat << EOF
Monitoring and Analysis Commands

USAGE:
  ./run.sh monitor [COMMAND] [DURATION] [OUTPUT_FILE]

COMMANDS:
  routing [DURATION] [OUTPUT_FILE]  Monitor routing decisions

EXAMPLES:
  ./run.sh monitor routing                  # Monitor for 10 minutes
  ./run.sh monitor routing 30               # Monitor for 30 minutes
  ./run.sh monitor routing 15 analysis.json # Save to file

EOF
}

show_predictor_help() {
    cat << EOF
Predictor Service Commands

USAGE:
  ./run.sh predictor [COMMAND] [OPTIONS]

HEALTH CHECKS:
  health                Check predictor service health
  info                  Get service information

PREDICTION TESTING:
  predict               Test prediction functionality with sample data
  predict-chat          Test chat task prediction
  predict-code          Test code task prediction
  predict-custom        Test custom prediction with parameters

EXAMPLES:
  ./run.sh predictor health                 # Check service health
  ./run.sh predictor info                   # Get service information
  ./run.sh predictor predict                # Test with default parameters
  ./run.sh predictor predict-chat           # Test chat task prediction
  ./run.sh predictor predict-code           # Test code task prediction
  ./run.sh predictor predict-custom         # Interactive custom prediction

NOTES:
  - All commands run inside Docker container
  - Predictor service must be accessible via PREDICT_URL environment variable
  - Default timeout is 5 seconds (configurable via PREDICT_TIMEOUT)
  - Service uses /health endpoint for health checks
  - Predictions use /predict/chat and /predict/code endpoints

EOF
}

# =============================================================================
# BACKWARD COMPATIBILITY FUNCTIONS
# =============================================================================

# Handle backward compatibility commands
handle_legacy_command() {
    local command="$1"
    shift
    
    case "$command" in
        # Docker commands (direct mapping)
        build|up|down|stop|restart|remove|rebuild|rebuild-safe|sync|logs|logs-follow|status)
            log_info "Executing legacy command: $command (mapped to docker $command)"
            source "$SCRIPT_DIR/scripts/docker/services.sh"
            handle_docker_command "$command" "$@"
            ;;
        
        # Custom config commands
        env|mount|cmd|default)
            log_info "Executing legacy command: $command (mapped to custom $command)"
            source "$SCRIPT_DIR/scripts/docker/custom.sh"
            handle_custom_command "$command" "$@"
            ;;
        
        # Other legacy commands that map to categories
        test)
            handle_test_command "$@"
            ;;
        config)
            handle_config_command "$@"
            ;;
        debug)
            handle_debug_command "$@"
            ;;
        monitor)
            handle_monitor_command "$@"
            ;;
        list)
            source "$SCRIPT_DIR/scripts/gpustack/models.sh"
            handle_gpustack_models_command "list" "$@"
            ;;
        create)
            source "$SCRIPT_DIR/scripts/gpustack/models.sh"
            handle_gpustack_models_command "create" "$@"
            ;;
        update)
            # Handle different update types
            local update_type="$1"
            shift || true
            
            case "$update_type" in
                        config)
            # Update litellm.config.yaml with new GPUSTACK_KEY
            execute_local_script_by_key "update_config" "Updating litellm.config.yaml with new GPUSTACK_KEY" --config "$SCRIPT_DIR/$DEFAULT_CONFIG_FILE"
            ;;
                gpustack)
                    local gpustack_subtype="${1:-all}"
                    shift || true
                    
                    source "$SCRIPT_DIR/scripts/gpustack/credentials.sh"
                    case "$gpustack_subtype" in
                        token)
                            handle_gpustack_credentials_command "update-token"
                            ;;
                        key)
                            handle_gpustack_credentials_command "update-key"
                            ;;
                        password)
                            handle_gpustack_credentials_command "update-password"
                            ;;
                        all|"")
                            handle_gpustack_credentials_command "update-all"
                            ;;
                        *)
                            echo "ERROR: Unknown GPUStack update target: $gpustack_subtype"
                            echo "Usage: ./run.sh update gpustack [all|token|key|password]"
                            return 1
                            ;;
                    esac
                    ;;
                ""*)
                    # Default to gpustack credentials update for backward compatibility
                    source "$SCRIPT_DIR/scripts/gpustack/credentials.sh"
                    handle_gpustack_credentials_command "update-all" "$update_type" "$@"
                    ;;
                *)
                    echo "ERROR: Unknown update target: $update_type"
                    echo "Usage: ./run.sh update [config|gpustack]"
                    return 1
                    ;;
            esac
            ;;
        
        *)
            return 1  # Not a legacy command
            ;;
    esac
}

# =============================================================================
# COMMAND HANDLERS
# =============================================================================

# Handle Docker commands
handle_docker_command() {
    source "$SCRIPT_DIR/scripts/docker/services.sh"
    handle_docker_services_command "$@"
}

# Handle custom configuration commands
handle_custom_command() {
    source "$SCRIPT_DIR/scripts/docker/custom.sh"
    handle_custom_command "$@"
}

# Handle config commands
handle_config_command() {
    local subcommand="$1"
    
    # Check if subcommand is provided
    if [[ -z "$subcommand" ]]; then
        show_config_help
        return 0
    fi
    
    shift
    
    case "$subcommand" in
        models)
            source "$SCRIPT_DIR/scripts/config/models.sh"
            handle_models_command "$@"
            ;;
        settings)
            source "$SCRIPT_DIR/scripts/config/settings.sh"
            handle_settings_command "$@"
            ;;
        validation)
            source "$SCRIPT_DIR/scripts/config/validation.sh"
            handle_validation_command "$@"
            ;;
        help|--help|-h)
            show_config_help
            ;;
        *)
            source "$SCRIPT_DIR/scripts/config/manager.sh"
            handle_manager_command "$subcommand" "$@"
            ;;
    esac
}

# Handle update commands
handle_update_command() {
    local update_type="$1"
    
    # Check if update_type is provided
    if [[ -z "$update_type" ]]; then
        show_update_help
        return 0
    fi
    
    shift
    
    case "$update_type" in
        config)
            # Update litellm.config.yaml with new GPUSTACK_KEY
            execute_local_script_by_key "update_config" "Updating litellm.config.yaml with new GPUSTACK_KEY" --config "$SCRIPT_DIR/$DEFAULT_CONFIG_FILE"
            ;;
        gpustack)
            local gpustack_subtype="${1:-all}"
            shift || true
            
            source "$SCRIPT_DIR/scripts/gpustack/credentials.sh"
            case "$gpustack_subtype" in
                token)
                    handle_gpustack_credentials_command "update-token"
                    ;;
                key)
                    handle_gpustack_credentials_command "update-key"
                    ;;
                password)
                    handle_gpustack_credentials_command "update-password"
                    ;;
                all|"")
                    handle_gpustack_credentials_command "update-all"
                    ;;
                *)
                    echo "ERROR: Unknown GPUStack update target: $gpustack_subtype"
                    echo "Usage: ./run.sh update gpustack [all|token|key|password]"
                    return 1
                    ;;
            esac
            ;;
        help|--help|-h)
            show_update_help
            ;;
        *)
            echo "ERROR: Unknown update target: $update_type"
            echo "Usage: ./run.sh update [config|gpustack] [OPTIONS]"
            echo "Use './run.sh update help' for more information"
            return 1
            ;;
    esac
}

# Handle race condition test commands
handle_race_condition_test_command() {
    local subcommand="$1"
    
    # Check if subcommand is provided
    if [[ -z "$subcommand" ]]; then
        cat << EOF
Race Condition Test Commands

USAGE:
  ./run.sh test race-condition [COMMAND] [OPTIONS]

COMMANDS:
  quick [MODEL]         Quick race condition test (default: chat_instruct)
  model MODEL           Test specific model for race condition
  all                   Test all models for race condition
  detailed MODEL [N]    Detailed test with N requests (default: 50)

OPTIONS:
  --requests N          Number of concurrent requests (default: 20)

EXAMPLES:
  ./run.sh test race-condition quick                    # Quick test with chat_instruct
  ./run.sh test race-condition model chat_coder        # Test chat_coder
  ./run.sh test race-condition all                     # Test all models
  ./run.sh test race-condition detailed chat_instruct 50  # Detailed test with 50 requests

DESCRIPTION:
  Тестирует race condition в конкурентной маршрутизации.
  При race condition все запросы идут на один deployment
  вместо равномерного распределения между доступными deployments.

EOF
        return 0
    fi
    
    shift
    
    case "$subcommand" in
        quick)
            local model="${1:-chat_instruct}"
            echo "🚀 QUICK RACE CONDITION TEST"
            echo "Model: $model"
            echo "=================================="
            
            if docker ps > /dev/null 2>&1; then
                echo "Running in Docker container..."
                execute_docker_module "llm_router.tests.routing.test_concurrent_routing_race_condition" "Quick race condition test for $model" "--model" "$model" "--requests" "20"
            else
                echo "Running locally..."
                cd "$SCRIPT_DIR"
                python -m llm_router.tests.routing.test_concurrent_routing_race_condition --model "$model" --requests 20
            fi
            ;;
        
        model)
            local model="$1"
            if [[ -z "$model" ]]; then
                echo "ERROR: Model name required"
                echo "Usage: ./run.sh test race-condition model MODEL_NAME"
                return 1
            fi
            
            echo "🔍 RACE CONDITION TEST FOR MODEL: $model"
            echo "========================================"
            
            if docker ps > /dev/null 2>&1; then
                echo "Running in Docker container..."
                execute_docker_module "llm_router.tests.routing.test_concurrent_routing_race_condition" "Race condition test for $model" "--model" "$model" "--requests" "30"
            else
                echo "Running locally..."
                cd "$SCRIPT_DIR"
                python -m llm_router.tests.routing.test_concurrent_routing_race_condition --model "$model" --requests 30
            fi
            ;;
        
        all)
            echo "🚀 RACE CONDITION TEST FOR ALL MODELS"
            echo "====================================="
            
            if docker ps > /dev/null 2>&1; then
                echo "Running in Docker container..."
                execute_docker_module "llm_router.tests.routing.test_concurrent_routing_race_condition" "Race condition test for all models" "--all-models"
            else
                echo "Running locally..."
                cd "$SCRIPT_DIR"
                python -m llm_router.tests.routing.test_concurrent_routing_race_condition --all-models
            fi
            ;;
        
        detailed)
            local model="$1"
            local requests="${2:-50}"
            
            if [[ -z "$model" ]]; then
                echo "ERROR: Model name required"
                echo "Usage: ./run.sh test race-condition detailed MODEL_NAME [REQUESTS]"
                return 1
            fi
            
            echo "🔍 DETAILED RACE CONDITION TEST"
            echo "Model: $model"
            echo "Requests: $requests"
            echo "================================"
            
            if docker ps > /dev/null 2>&1; then
                echo "Running in Docker container..."
                execute_docker_module "llm_router.tests.routing.test_concurrent_routing_race_condition" "Detailed race condition test for $model" "--model" "$model" "--requests" "$requests"
            else
                echo "Running locally..."
                cd "$SCRIPT_DIR"
                python -m llm_router.tests.routing.test_concurrent_routing_race_condition --model "$model" --requests "$requests"
            fi
            ;;
        
        help|--help|-h)
            handle_race_condition_test_command
            ;;
        
        *)
            echo "ERROR: Unknown race-condition command: $subcommand"
            echo "Use './run.sh test race-condition help' for available commands"
            return 1
            ;;
    esac
}

# Handle test commands
handle_test_command() {
    local category="$1"
    
    # Check if category is provided
    if [[ -z "$category" ]]; then
        show_test_help
        return 0
    fi
    
    shift
    
    case "$category" in
        endpoint|model|health|all)
            source "$SCRIPT_DIR/scripts/testing/health.sh"
            handle_health_command "$category" "$@"
            ;;
        models)
            # Check if this is a subcommand for model testing
            if [[ "$1" == "connectivity" || "$1" == "functionality" || "$1" == "managed-models" || "$1" == "debug" || "$1" == "infinity" || "$1" == "integration" || "$1" == "predictor-all" || "$1" == "specific" || "$1" == "all-available" ]]; then
                source "$SCRIPT_DIR/scripts/testing/models.sh"
                handle_models_command "$@"
            else
                # Default to health module for "test models" (test all models)
                source "$SCRIPT_DIR/scripts/testing/health.sh"
                handle_health_command "$category" "$@"
            fi
            ;;
        router)
            source "$SCRIPT_DIR/scripts/testing/router.sh"
            handle_router_command "$@"
            ;;
        balancing)
            source "$SCRIPT_DIR/scripts/testing/router.sh"
            handle_balancing_command "$@"
            ;;
        gpustack)
            handle_gpustack_test_command "$@"
            ;;
        auth)
            source "$SCRIPT_DIR/scripts/testing/auth.sh"
            handle_auth_command "$@"
            ;;
        analytics)
            # Redirect to analytics test subcommands
            source "$SCRIPT_DIR/scripts/analytics/test.sh"
            handle_analytics_test "$@"
            ;;
        prisma)
            source "$SCRIPT_DIR/scripts/testing/prisma.sh"
            handle_prisma_command "$@"
            ;;
        race-condition)
            handle_race_condition_test_command "$@"
            ;;
        quick-check)
            source "$SCRIPT_DIR/scripts/testing/quick_check.sh"
            main "$@"
            ;;
        help|--help|-h)
            show_test_help
            ;;
        *)
            # Treat as model name for backward compatibility
            source "$SCRIPT_DIR/scripts/testing/health.sh"
            handle_health_command "$category" "$@"
            ;;
    esac
}

# Handle GPUStack test commands
handle_gpustack_test_command() {
    local subcommand="$1"
    
    # Check if subcommand is provided
    if [[ -z "$subcommand" ]]; then
        cat << EOF
GPUStack Integration Test Commands

USAGE:
  ./run.sh test gpustack [COMMAND]

COMMANDS:
  api           Comprehensive API integration test
  data-update   Test data update cycles and cache management  
  health        Quick health check of integration
  auth          Test authentication with GPUStack API
  cache         Test cache functionality and invalidation
  all           Run all GPUStack integration tests

REQUIREMENTS:
  Environment variables:
  - GPUSTACK_INTERNAL: GPUStack API URL (e.g., http://host.docker.internal:80/v1)
  - GPUSTACK_KEY: Valid GPUStack API key

EXAMPLES:
  ./run.sh test gpustack api          # Test API integration
  ./run.sh test gpustack data-update  # Test data updates
  ./run.sh test gpustack health       # Quick health check
  ./run.sh test gpustack all          # Run all tests

EOF
        return 0
    fi
    
    shift
    
    case "$subcommand" in
        api)
            echo "Running GPUStack API Integration Tests"
            echo "======================================="
            
            # Check if running in Docker environment
            if docker ps > /dev/null 2>&1; then
                echo "Detected Docker environment"
                echo "Running tests in Docker container..."
                execute_docker_module "llm_router.tests.gpustack.test_gpustack_api_integration" "Running GPUStack API integration tests"
            else
                echo "Running tests locally..."
                cd "$SCRIPT_DIR"
                python -m llm_router.tests.gpustack.test_gpustack_api_integration
            fi
            ;;
        
        data-update)
            echo "Running GPUStack Data Update Tests"
            echo "=================================="
            
            # Check if running in Docker environment
            if docker ps > /dev/null 2>&1; then
                echo "Detected Docker environment"
                echo "Running data update tests in Docker container..."
                execute_docker_module "llm_router.tests.gpustack.test_gpustack_data_update" "Running GPUStack data update tests"
            else
                echo "Running data update tests locally..."
                cd "$SCRIPT_DIR"
                python -m llm_router.tests.gpustack.test_gpustack_data_update
            fi
            ;;
        
        health)
            echo "Running GPUStack Health Check"
            echo "============================="
            
            # Run health check script
            if docker ps > /dev/null 2>&1; then
                echo "Running health check in Docker..."
                execute_docker_module "llm_router.tests.gpustack.test_gpustack_health" "Running GPUStack health check"
            else
                echo "Running health check locally..."
                cd "$SCRIPT_DIR"
                python -m llm_router.tests.gpustack.test_gpustack_health
            fi
            ;;
        
        auth)
            echo "Running GPUStack Authentication Test"
            echo "===================================="
            
            # Authentication test script
            if docker ps > /dev/null 2>&1; then
                echo "Running auth test in Docker..."
                execute_docker_module "llm_router.tests.gpustack.test_gpustack_auth" "Running GPUStack authentication test"
            else
                echo "Running auth test locally..."
                cd "$SCRIPT_DIR"
                python -m llm_router.tests.gpustack.test_gpustack_auth
            fi
            ;;
        
        cache)
            echo "Running GPUStack Cache Tests"
            echo "============================"
            
            # Cache test script
            if docker ps > /dev/null 2>&1; then
                echo "Running cache tests in Docker..."
                execute_docker_module "llm_router.tests.gpustack.test_gpustack_cache" "Running GPUStack cache tests"
            else
                echo "Running cache tests locally..."
                cd "$SCRIPT_DIR"
                python -m llm_router.tests.gpustack.test_gpustack_cache
            fi
            ;;
        
        all)
            echo "Running All GPUStack Integration Tests"
            echo "======================================"
            
            local test_results=()
            local test_names=("API Integration" "Data Update" "Health Check" "Authentication" "Cache Functionality")
            local test_commands=("api" "data-update" "health" "auth" "cache")
            
            for i in "${!test_commands[@]}"; do
                echo ""
                echo "Running ${test_names[$i]} Test..."
                echo "----------------------------------------"
                
                if handle_gpustack_test_command "${test_commands[$i]}"; then
                    test_results+=("PASSED: ${test_names[$i]}")
                else
                    test_results+=("FAILED: ${test_names[$i]}")
                fi
            done
            
            echo ""
            echo "======================================"
            echo "GPUStack Integration Test Summary:"
            echo "======================================"
            
            local all_passed=true
            for result in "${test_results[@]}"; do
                echo "  $result"
                if [[ "$result" == *"FAILED"* ]]; then
                    all_passed=false
                fi
            done
            
            echo ""
            if $all_passed; then
                echo "All GPUStack integration tests passed!"
                return 0
            else
                echo "Some GPUStack tests failed. Check configuration and environment."
                return 1
            fi
            ;;
        
        help|--help|-h)
            cat << EOF
GPUStack Integration Test Commands

USAGE:
  ./run.sh test gpustack [COMMAND]

COMMANDS:
  api           Comprehensive API integration test
  data-update   Test data update cycles and cache management  
  health        Quick health check of integration
  auth          Test authentication with GPUStack API
  cache         Test cache functionality and invalidation
  all           Run all GPUStack integration tests

REQUIREMENTS:
  Environment variables:
  - GPUSTACK_INTERNAL: GPUStack API URL (e.g., http://host.docker.internal:80/v1)
  - GPUSTACK_KEY: Valid GPUStack API key

EXAMPLES:
  ./run.sh test gpustack api          # Test API integration
  ./run.sh test gpustack data-update  # Test data updates
  ./run.sh test gpustack health       # Quick health check
  ./run.sh test gpustack all          # Run all tests

EOF
            ;;
        
        *)
            echo "Unknown GPUStack test command: $subcommand"
            echo "Use './run.sh test gpustack help' for available commands"
            return 1
            ;;
    esac
}

# Handle GPUStack commands
handle_gpustack_command() {
    local category="$1"
    
    # Check if category is provided
    if [[ -z "$category" ]]; then
        show_gpustack_help
        return 0
    fi
    
    shift
    
    case "$category" in
        workers|instances|info)
            source "$SCRIPT_DIR/scripts/gpustack/info.sh"
            handle_gpustack_info_command "$category" "$@"
            ;;
        models)
            source "$SCRIPT_DIR/scripts/gpustack/models.sh"
            handle_gpustack_models_command "$@"
            ;;
        credentials)
            source "$SCRIPT_DIR/scripts/gpustack/credentials.sh"
            handle_gpustack_credentials_command "$@"
            ;;
        help|--help|-h)
            show_gpustack_help
            ;;
        *)
            echo "ERROR: Unknown gpustack category: $category"
            echo "Use './run.sh gpustack help' for available commands"
            return 1
            ;;
    esac
}

# Handle debug commands
handle_debug_command() {
    local category="$1"
    
    # If no category provided, show help instead of default debug
    if [[ -z "$category" ]]; then
        show_debug_help
        return 0
    fi
    
    shift
    
    case "$category" in
        router)
            source "$SCRIPT_DIR/scripts/debug/router.sh"
            handle_debug_router_command "$@"
            ;;
        cache)
            source "$SCRIPT_DIR/scripts/debug/cache.sh"
            handle_debug_cache_command "$@"
            ;;
        help|--help|-h)
            show_debug_help
            ;;
        *)
            echo "ERROR: Unknown debug category: $category"
            echo "Use './run.sh debug help' for available commands"
            return 1
            ;;
    esac
}

# Handle monitor commands
handle_monitor_command() {
    local category="$1"
    shift
    
    case "$category" in
        routing|"")
            source "$SCRIPT_DIR/scripts/monitoring/routing.sh"
            handle_monitoring_routing_command "routing" "$@"
            ;;
        help|--help|-h)
            show_monitor_help
            ;;
        *)
            # Treat as duration parameter for routing
            source "$SCRIPT_DIR/scripts/monitoring/routing.sh"
            handle_monitoring_routing_command "$category" "$@"
            ;;
    esac
}

# Handle analytics commands
handle_analytics_command() {
    local category="$1"
    
    if [[ -z "$category" ]]; then
        source "$SCRIPT_DIR/scripts/analytics/main.sh"
        show_analytics_help
        return 0
    fi
    
    shift
    
    # Check for --host flag and route to host-based handler
    local use_host=false
    local new_args=()
    
    # Parse arguments to detect --host flag
    while [[ $# -gt 0 ]]; do
        case "$1" in
            --host)
                use_host=true
                shift
                ;;
            *)
                new_args+=("$1")
                shift
                ;;
        esac
    done
    
    # Load analytics utilities
    source "$SCRIPT_DIR/scripts/analytics/analytics_utils.sh"
    source "$SCRIPT_DIR/scripts/analytics/main.sh"
    
    if [[ "$use_host" == "true" ]]; then
        # Host-based execution: call host-specific handler
        handle_analytics_commands_host "$category" "${new_args[@]}"
    else
        # Docker-based execution: traditional handler
        handle_analytics_commands "$category" "${new_args[@]}"
    fi
}

# =============================================================================
# MAIN COMMAND PROCESSING
# =============================================================================

main() {
    local main_cmd="${1:-help}"
    shift || true
    
    case "$main_cmd" in
        # Help commands
        help|--help|-h)
            if [[ -n "$1" ]]; then
                show_category_help "$1"
            else
                show_main_help
            fi
            ;;
        
        # Category commands
        docker)
            handle_docker_command "$@"
            ;;
        custom)
            handle_custom_command "$@"
            ;;
        config)
            handle_config_command "$@"
            ;;
        update)
            handle_update_command "$@"
            ;;
        test)
            handle_test_command "$@"
            ;;
        gpustack)
            handle_gpustack_command "$@"
            ;;
        debug)
            handle_debug_command "$@"
            ;;
        monitor)
            handle_monitor_command "$@"
            ;;
        predictor)
            handle_predictor_command "$@"
            ;;
        analytics)
            handle_analytics_command "$@"
            ;;

        # Try legacy command handling
        *)
            if ! handle_legacy_command "$main_cmd" "$@"; then
                echo "ERROR: Unknown command: $main_cmd"
                echo ""
                show_main_help
                exit 1
            fi
            ;;
    esac
}

# =============================================================================
# SCRIPT EXECUTION
# =============================================================================

# Only run main if script is executed directly (not sourced)
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi 