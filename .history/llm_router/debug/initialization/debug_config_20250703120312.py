#!/usr/bin/env python3
"""
Configuration Debug Utility for LLM Router.

This utility helps debug configuration issues by checking:
1. YAML structure and required keys
2. Predictor service connectivity
3. Model consistency between model_list and managed_models

This can be run independently without ML dependencies.
"""
from pathlib import Path
from typing import Dict, Any
import os
import sys

from llm_router.logging_utils import get_logger


logger = get_logger(__name__)


def test_config_structure():
    """Test if configuration structure is correct."""
    try:
        import yaml

        current_dir = Path(__file__).parent.absolute()
        config_path = current_dir.parent / "router_litellm" / "litellm.config.yaml"
        logger.info(f"DEBUG: Loading configuration from: {config_path}")
        with open(config_path, "r") as f:
            config = yaml.safe_load(f)
        required_sections = ["model_list", "router_settings"]
        for section in required_sections:
            if section not in config:
                logger.error(f"DEBUG: Missing required section: {section}")
                return False
            logger.info(f"DEBUG: Found section: {section}")
        router_settings = config["router_settings"]
        required_router_keys = ["routing_strategy", "managed_models"]
        for key in required_router_keys:
            if key not in router_settings:
                logger.error(f"DEBUG: Missing required router_settings key: {key}")
                return False
            logger.info(f"DEBUG: Found router_settings key: {key}")
        if router_settings["routing_strategy"] != "custom":
            logger.warning(
                f"DEBUG: routing_strategy is '{router_settings['routing_strategy']}', expected 'custom'"
            )
        optional_keys = ["default_task_type_for_predictor", "load_balancing_weight"]
        for key in optional_keys:
            if key in router_settings:
                logger.info(
                    f"DEBUG: Found optional router_settings key: {key} = {router_settings[key]}"
                )
            else:
                logger.info(f"DEBUG: Optional router_settings key not set: {key}")
        managed_models = router_settings["managed_models"]
        if not isinstance(managed_models, list):
            logger.error("DEBUG: managed_models should be a list")
            return False
        logger.info(f"DEBUG: Found {len(managed_models)} managed models")
        for i, model in enumerate(managed_models):
            required_keys = ["id", "predictor_task_type", "predictor_params"]
            for key in required_keys:
                if key not in model:
                    logger.error(f"DEBUG: Managed model {i} missing key: {key}")
                    return False
            params = model["predictor_params"]
            param_keys = [
                "hardware",
                "model_name",
                "concurrent_requests",
                "model_instances",
            ]
            for key in param_keys:
                if key not in params:
                    logger.warning(f"DEBUG: Managed model {i} missing param: {key}")
            logger.info(
                f"DEBUG: Managed model {i}: {model['id']} -> {model['predictor_task_type']}"
            )
        return True
    except Exception as e:
        logger.error(f"DEBUG: Error loading configuration: {e}")
        return False


def test_predictor_service_connectivity():
    """Test if predictor service is available and responding."""
    try:
        import requests

        predict_url = os.environ.get("PREDICT_URL", "http://localhost:8008")
        response = requests.get(f"{predict_url}/health", timeout=5)
        if response.status_code == 200:
            logger.info(f"DEBUG: Predictor service is healthy: {predict_url}")
            try:
                health_data = response.json()
                logger.info(f"DEBUG:   Service status: {health_data}")
            except:
                logger.info(f"DEBUG:   Service responded with: {response.text[:100]}")
            return True
        else:
            logger.error(
                f"DEBUG: Predictor service health check failed: HTTP {response.status_code}"
            )
            return False
    except requests.exceptions.ConnectionError:
        logger.error(f"DEBUG: Cannot connect to predictor service at {predict_url}")
        return False
    except Exception as e:
        logger.error(f"DEBUG: Error checking predictor service: {e}")
        return False


def test_model_list_consistency():
    """Test if model_list and managed_models are consistent."""
    try:
        import yaml

        current_dir = Path(__file__).parent.absolute()
        config_path = current_dir.parent / "router_litellm" / "litellm.config.yaml"
        with open(config_path, "r") as f:
            config = yaml.safe_load(f)
        model_list_ids = set()
        for model in config["model_list"]:
            model_id = model.get("model_info", {}).get("id")
            if model_id:
                model_list_ids.add(model_id)
        managed_model_ids = set()
        for model in config["router_settings"]["managed_models"]:
            model_id = model.get("id")
            if model_id:
                managed_model_ids.add(model_id)
        logger.info(f"DEBUG: Model list IDs: {sorted(model_list_ids)}")
        logger.info(f"DEBUG: Managed model IDs: {sorted(managed_model_ids)}")
        missing_in_managed = model_list_ids - managed_model_ids
        missing_in_model_list = managed_model_ids - model_list_ids
        if missing_in_managed:
            logger.warning(
                f"DEBUG: Models in model_list but not in managed_models: {missing_in_managed}"
            )
        if missing_in_model_list:
            logger.error(
                f"DEBUG: Models in managed_models but not in model_list: {missing_in_model_list}"
            )
            return False
        if not missing_in_managed and (not missing_in_model_list):
            logger.info("DEBUG: Model list and managed models are consistent")
        return True
    except Exception as e:
        logger.error(f"DEBUG: Error checking model consistency: {e}")
        return False


def main():
    """Main function."""
    logger.info("DEBUG: === LLM Router Configuration Debug ===")
    logger.info(
        "DEBUG: This script checks configuration without requiring ML dependencies"
    )
    logger.info("DEBUG: ")
    tests = [
        ("Configuration Structure", test_config_structure),
        ("Predictor Service Connectivity", test_predictor_service_connectivity),
        ("Model List Consistency", test_model_list_consistency),
    ]
    passed = 0
    total = len(tests)
    for test_name, test_func in tests:
        logger.info(f"DEBUG: --- {test_name} ---")
        try:
            result = test_func()
            if result:
                logger.info(f"DEBUG: {test_name}: PASSED")
                passed += 1
            else:
                logger.error(f"DEBUG: {test_name}: FAILED")
        except Exception as e:
            logger.error(f"DEBUG: {test_name}: ERROR - {e}")
        logger.info("DEBUG: ")
    logger.info("DEBUG: === Summary ===")
    logger.info(f"DEBUG: Passed: {passed}/{total}")
    if passed == total:
        logger.info("DEBUG: All configuration tests passed!")
        logger.info("DEBUG: For full testing with ML models, run: ./run.sh debug")
        return 0
    else:
        logger.warning(f"DEBUG: {total - passed} tests failed")
        return 1


if __name__ == "__main__":
    sys.exit(main())
