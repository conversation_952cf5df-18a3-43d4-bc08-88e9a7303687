#!/usr/bin/env python3
"""
Пользовательская стратегия маршрутизации для LiteLLM, использующая внешний сервис llm_time_predictor
для выбора оптимальной конечной точки на основе предсказанного времени ответа.

Этот модуль обеспечивает интеллектуальную маршрутизацию запросов LLM путем предсказания времени ответа
для различных развертываний моделей через HTTP API и выбора самого быстрого варианта.
"""
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple, Union
import asyncio
import time
import traceback

from litellm.router import CustomRoutingStrategyBase
from llm_router.analytics.data_builder import create_router_analysis_data
from llm_router.analytics.metadata_utils import set_metadata
from llm_router.analytics.router_analytics_logger import RouterAnalyticsLogger
from llm_router.constants import (
    GPUSTACK_ENABLED,
    LOAD_BALANCING_WEIGHT,
    GPUSTACK_CACHE_TTL,
    DEFAULT_TASK_TYPE,
)
from llm_router.logging_utils import get_logger
from llm_router.predictor.predictor_client import PredictorClientError
from llm_router.router.deployment_selector_mixin import DeploymentSelectorMixin
from llm_router.router.gpustack_router_mixin import GPUStackRouterMixin
from llm_router.router.router_initialization_mixin import RouterInitializationMixin
from llm_router.router.least_busy_integration import LeastBusyIntegration
from llm_router.router.router_predictor_mixin import PredictorIntegration
from llm_router.router.router_utils_mixin import RouterUtilsMixin
from llm_router.router.task_type_utils import get_task_type_from_model_name
import litellm


logger = get_logger(__name__)
litellm.set_verbose = True


class LLMTimePredictorRoutingStrategy(
    CustomRoutingStrategyBase,
    RouterInitializationMixin,
    DeploymentSelectorMixin,
    GPUStackRouterMixin,
    RouterUtilsMixin,
):
    """Пользовательская стратегия маршрутизации с использованием ML-сервиса предсказания для интеллектуального выбора конечных точек.

    Эта стратегия обеспечивает интеллектуальную маршрутизацию запросов LLM, используя внешний
    ML-сервис предсказания для оценки времени ответа различных развертываний моделей.
    Она сочетает ML-предсказания с данными о нагрузке в реальном времени для выбора оптимальной
    конечной точки на основе предсказанной производительности.

    Стратегия поддерживает несколько режимов маршрутизации, включая ML-предсказания,
    отслеживание наименее загруженных узлов и гибридную оценку, объединяющую оба подхода.
    Она интегрируется с GPUStack для получения расширенной информации о оборудовании и нагрузке,
    а также включает комплексный сбор аналитических данных для мониторинга и оптимизации.

    Ключевые возможности:
    - ML-предсказание времени ответа через HTTP API
    - Отслеживание нагрузки в реальном времени и маршрутизация к наименее загруженным узлам
    - Гибридная оценка, сочетающая ML-предсказания и данные о нагрузке
    - Интеграция с GPUStack для расширенной информации о развертываниях
    - Комплексные возможности аналитики и мониторинга
    - Стратегии изящного отката при обработке ошибок

    Attributes:
        router: Экземпляр LiteLLM Router, предоставляющий доступ к развертываниям моделей.
        predictor: Экземпляр PredictorIntegration для связи с ML-сервисом предсказания.
        default_task_type: Тип ML-задачи по умолчанию, используемый когда не указан для модели.
        load_balancing_weight: Весовой коэффициент для объединения ML-предсказаний с данными о нагрузке.

    Example:
        >>> # Инициализация стратегии маршрутизации
        >>> strategy = LLMTimePredictorRoutingStrategy(router=router)
        >>>
        >>> # Маршрутизация запроса
        >>> deployment = await strategy.get_available_deployment(
        ...     model="chat_instruct",
        ...     messages=[{"role": "user", "content": "Привет"}]
        ... )
        >>> print(f"Выбранное развертывание: {deployment['model_info']['id']}")
    """

    def __init__(self, router: Optional[Any] = None, **kwargs: Any) -> None:
        """Инициализация стратегии маршрутизации с интеграцией ML-сервиса предсказания.

        Настраивает стратегию маршрутизации путем инициализации ML-клиента предсказания
        и установки возможностей отслеживания наименее загруженных узлов. Процесс
        инициализации использует фиксированные константы вместо динамических настроек
        для упрощения конфигурации и повышения предсказуемости поведения.

        Args:
            router: Экземпляр LiteLLM Router, предоставляющий доступ к развертываниям моделей
                и конфигурации маршрутизации. Может быть None во время тестирования.
            **kwargs: Дополнительные параметры конфигурации (сохранены для совместимости)

        Raises:
            ImportError: Если недоступны необходимые зависимости (predictor client, litellm).
            ConnectionError: Если сервис предсказания недоступен во время инициализации.

        Example:
            >>> strategy = LLMTimePredictorRoutingStrategy(router=router)
        """
        logger.debug("INIT: начата инициализация CustomRoutingStrategy")
        super().__init__()
        self._log_initialization_context(router, kwargs)
        self.router = router

        self._init_gpustack()
        self._init_predictor()
        self._init_least_busy()
        self._init_analytics()
        self._log_simplified_final_initialization_state()
        logger.info("INIT: инициализация стратегии маршрутизации успешно завершена")

    def _log_simplified_final_initialization_state(self) -> None:
        """Логирование упрощенного финального состояния инициализации для проверки.

        Заменяет _log_final_initialization_state из RouterInitializationMixin,
        убирая ссылки на predictor_config которого больше не существует.
        """
        logger.debug("INIT: Final state check...")
        logger.debug(
            f"PREDICTOR: Integration available: {hasattr(self, 'predictor') and self.predictor is not None}"
        )
        logger.debug(f"INIT: Using constants-based configuration")
        if self.router:
            has_logger = (
                hasattr(self.router, "leastbusy_logger")
                and self.router.leastbusy_logger is not None
            )
            logger.debug(f"LEAST_BUSY: Logger after setup: {has_logger}")

    def _init_predictor(self) -> None:
        """Инициализация компонента PredictorIntegration.

        Создает экземпляр PredictorIntegration который инициализирует predictor_client
        и обеспечивает интеграцию с ML сервисом предсказания времени ответа.
        Использует упрощенную инициализацию без predictor_config.

        Raises:
            ConnectionError: Если сервис предсказания недоступен
            Exception: При других ошибках инициализации
        """
        logger.debug("INIT: инициализация PredictorIntegration")
        try:
            self.predictor = PredictorIntegration(
                router_instance=self,
                default_task_type=DEFAULT_TASK_TYPE,
            )
            logger.debug("INIT: PredictorIntegration успешно инициализирован")
        except Exception as e:
            logger.error(f"PREDICTOR: ошибка инициализации PredictorIntegration: {e}")
            logger.debug(
                f"PREDICTOR: трассировка ошибки PredictorIntegration: {traceback.format_exc()}"
            )
            raise

    def _init_least_busy(self) -> None:
        """Инициализация компонента LeastBusyIntegration.

        Создает экземпляр LeastBusyIntegration который настраивает отслеживание
        наименее загруженных развертываний через коллбеки LiteLLM.

        Это ключевой компонент - если инициализация не удалась, роутер не может работать.

        Raises:
            Exception: Если инициализация least-busy компонента не удалась
        """
        logger.debug("INIT: инициализация LeastBusyIntegration")
        try:
            self.leastbusy = LeastBusyIntegration(router_instance=self)
            logger.debug("INIT: LeastBusyIntegration успешно инициализирован")
        except Exception as e:
            logger.error(f"LEAST_BUSY: критическая ошибка инициализации: {e}")
            logger.debug(f"LEAST_BUSY: трассировка ошибки: {traceback.format_exc()}")
            raise

    def _init_analytics(self) -> None:
        """Инициализация компонента RouterAnalyticsLogger.

        Создает и регистрирует RouterAnalyticsLogger как LiteLLM callback
        для сбора аналитических данных и записи в базу данных.

        RouterAnalyticsLogger объединяет функциональность сбора данных во время маршрутизации
        и записи данных в БД через LiteLLM callbacks.

        Raises:
            Exception: Если инициализация analytics компонента не удалась
        """
        logger.debug("INIT: инициализация RouterAnalyticsLogger")
        try:
            # Импортируем LiteLLM prisma_client для передачи в analytics
            from litellm.proxy.proxy_server import prisma_client

            # Создание единого RouterAnalyticsLogger для сбора данных и записи в БД
            # Передаем litellm_prisma_client для использования его .db атрибута
            self.analytics = RouterAnalyticsLogger(
                enabled=True, prisma_client=prisma_client
            )
            # Регистрация в LiteLLM callbacks
            success = self.analytics.register_with_litellm(auto_register=True)
            if success:
                logger.info(
                    "INIT: RouterAnalyticsLogger успешно инициализирован и зарегистрирован"
                )
            else:
                logger.warning(
                    "INIT: RouterAnalyticsLogger создан, но регистрация не удалась"
                )

        except Exception as e:
            logger.error(f"ANALYTICS: критическая ошибка инициализации: {e}")
            logger.debug(f"ANALYTICS: трассировка ошибки: {traceback.format_exc()}")
            raise

    async def get_available_deployment(
        self,
        model: str,
        messages: Optional[List] = None,
        input: Optional[Union[str, List]] = None,
        specific_deployment: Optional[bool] = None,
        request_kwargs: Optional[Dict] = None,
    ) -> Dict[str, Any]:
        """Получение оптимального развертывания для модели с использованием интеллектуальной маршрутизации.

        Это основной метод маршрутизации, который сочетает ML-предсказание времени ответа
        с отслеживанием нагрузки в реальном времени для выбора лучшего доступного развертывания
        для данного запроса. Метод обрабатывает сценарии с одним и несколькими развертываниями,
        включает комплексный сбор аналитических данных и предоставляет стратегии изящного
        отката в случае ошибок.

        Процесс маршрутизации включает:
        1. Поиск работоспособных развертываний для запрашиваемой модели
        2. Для одного развертывания: прямой выбор с аналитикой
        3. Для нескольких развертываний: ML-предсказание и гибридная оценка
        4. Откат к первому доступному развертыванию при сбое предсказания
        5. Сбор аналитических данных для мониторинга и оптимизации

        Args:
            model: Имя модели для маршрутизации запросов (например, "chat_instruct").
            messages: Опциональный список сообщений чата для контекстно-зависимой маршрутизации.
                Используется для извлечения текста промпта для ML-предсказания.
            input: Опциональный входной текст для моделей эмбеддингов или завершения.
                Альтернатива messages для не-чат сценариев.
            specific_deployment: Возвращать ли конкретное развертывание.
                В настоящее время не используется, но сохранен для совместимости интерфейса.
            request_kwargs: Дополнительные параметры запроса для отслеживания запросов и аналитики.

        Returns:
            Словарь с информацией о выбранном развертывании, включая:
            - model_info: Метаданные развертывания (id, оборудование и т.д.)
            - litellm_params: Параметры LiteLLM для развертывания
            - Дополнительные данные конфигурации развертывания

        Raises:
            Exception: Если не найдены работоспособные развертывания для модели или если
                маршрутизация полностью не удалась и нет доступного отката.

        Example:
            >>> deployment = await strategy.get_available_deployment(
            ...     model="schat_instruct",
            ...     messages=[{"role": "user", "content": "Привет"}],
            ...     request_kwargs={"temperature": 0.7}
            ... )
            >>> print(f"Выбрано: {deployment['model_info']['id']}")
        """
        logger.debug("ROUTING: вызван get_available_deployment")
        logger.debug(f"ROUTING: запрошена модель: {model}")
        logger.debug(f"ROUTING: есть messages: {bool(messages)}")
        logger.debug(f"ROUTING: есть input: {bool(input)}")
        logger.debug(f"ROUTING: kwargs запроса: {request_kwargs}")
        if request_kwargs:
            set_metadata(request_kwargs, "custom_router_model_group", model)
            logger.info(
                f"ROUTING: сохранена оригинальная model_group '{model}' в метаданных"
            )
        analysis_start_time = datetime.utcnow()
        analysis_start_time_ts = time.time()
        healthy_deployments = []
        try:
            healthy_deployments = self._get_healthy_deployments_for_model(model)
            logger.debug(
                f"ROUTING: найдено {len(healthy_deployments)} работоспособных развертываний"
            )
            if not healthy_deployments:
                logger.error(
                    "ROUTING: не найдены работоспособные развертывания для модели: %s",
                    model,
                )
                raise Exception(
                    f"Нет доступных работоспособных развертываний для модели: {model}"
                )

            # Определяем task_type для выбора стратегии роутинга на основе имени модели
            task_type = get_task_type_from_model_name(model)
            logger.info(
                f"ROUTING: определен task_type = '{task_type}' для модели {model} (определено по имени модели)"
            )
            logger.debug(
                f"ROUTING: использована функция get_task_type_from_model_name вместо predictor_config"
            )

            # Выбираем стратегию роутинга в зависимости от task_type
            use_predictor_strategy = task_type == "chat"
            if use_predictor_strategy:
                logger.info(
                    f"ROUTING: использую ПОЛНУЮ стратегию (ML предиктор + least-busy) для task_type='{task_type}'"
                )
            else:
                logger.info(
                    f"ROUTING: использую ТОЛЬКО LEAST-BUSY стратегию для task_type='{task_type}'"
                )

            if len(healthy_deployments) == 1:
                selected = healthy_deployments[0]
                deployment_id = selected.get("model_info", {}).get("id", "unknown")
                litellm_model = selected.get("litellm_params", {}).get(
                    "model", "unknown"
                )
                logger.info(
                    f"SELECTION: автоматически выбрано единственное развертывание: {deployment_id}"
                )
                # Аналитика записывается только для task_type == 'chat'
                if use_predictor_strategy:
                    self.analytics.collect_analytics_for_deployments(
                        request_kwargs=request_kwargs,
                        model=model,
                        messages=messages,
                        input=input,
                        selected_deployment=selected,
                        evaluations=None,
                        analysis_start_time=analysis_start_time,
                        analysis_start_time_ts=analysis_start_time_ts,
                        selection_method="ml_only",
                    )
                else:
                    logger.debug(
                        f"ANALYTICS: пропуск аналитики для task_type='{task_type}' (не 'chat')"
                    )
                return selected

            logger.debug(
                "ROUTING: доступны несколько развертываний - начинается интеллектуальный выбор"
            )

            # Выбираем стратегию в зависимости от task_type
            if use_predictor_strategy:
                logger.debug("ROUTING: используем полную стратегию с ML предиктором")
                selection_result = (
                    await self._select_deployment_with_predictor_and_analytics(
                        healthy_deployments,
                        model,
                        messages,
                        input,
                        request_kwargs,
                        analysis_start_time,
                        analysis_start_time_ts,
                    )
                )
            else:
                logger.debug("ROUTING: используем только least-busy стратегию")
                selection_result = await self._select_deployment_with_least_busy_only(
                    healthy_deployments,
                    model,
                    task_type,
                )

            final_deployment_id = selection_result.get("model_info", {}).get(
                "id", "unknown"
            )
            final_litellm_model = selection_result.get("litellm_params", {}).get(
                "model", "unknown"
            )
            logger.info(f"SELECTION: выбрано развертывание: {final_deployment_id}")
            logger.info("ROUTER_RETURN: возврат развертывания в LiteLLM:")
            logger.info(f"ROUTER_RETURN:   deployment_id: {final_deployment_id}")
            logger.info(f"ROUTER_RETURN:   litellm_model: {final_litellm_model}")
            logger.info(
                f"ROUTER_RETURN:   model_name: {selection_result.get('model_name', 'unknown')}"
            )
            logger.info(
                f"ROUTER_RETURN:   provider: {selection_result.get('provider', 'unknown')}"
            )
            logger.info(
                f"ROUTER_RETURN: полная структура развертывания: {selection_result}"
            )
            return selection_result
        except Exception as e:
            logger.debug(f"ROUTING: трассировка ошибки: {traceback.format_exc()}")
            if healthy_deployments:
                fallback = healthy_deployments[0]
                fallback_id = fallback.get("model_info", {}).get("id", "unknown")
                fallback_litellm_model = fallback.get("litellm_params", {}).get(
                    "model", "unknown"
                )
                logger.warning(
                    f"ROUTING: использование резервного развертывания: {fallback_id}"
                )
                logger.warning(
                    "ROUTER_FALLBACK: возврат резервного развертывания в LiteLLM:"
                )
                logger.warning(f"ROUTER_FALLBACK:   deployment_id: {fallback_id}")
                logger.warning(
                    f"ROUTER_FALLBACK:   litellm_model: {fallback_litellm_model}"
                )
                logger.warning(
                    f"ROUTER_FALLBACK:   model_name: {fallback.get('model_name', 'unknown')}"
                )
                logger.warning(
                    f"ROUTER_FALLBACK:   provider: {fallback.get('provider', 'unknown')}"
                )
                logger.warning(
                    f"ROUTER_FALLBACK: полная структура развертывания: {fallback}"
                )
                # Аналитика записывается только для task_type == 'chat'
                if use_predictor_strategy:
                    self.analytics.collect_analytics_for_deployments(
                        request_kwargs=request_kwargs,
                        model=model,
                        messages=messages,
                        input=input,
                        selected_deployment=fallback,
                        evaluations=None,
                        analysis_start_time=analysis_start_time,
                        analysis_start_time_ts=analysis_start_time_ts,
                        selection_method="fallback",
                        error_message=str(e),
                    )
                else:
                    logger.debug(
                        f"ANALYTICS: пропуск fallback аналитики для task_type='{task_type}' (не 'chat')"
                    )
                return fallback
            log_msg = f"ROUTING: маршрутизация не удалась для модели {model}: {type(e).__name__}: {e}"
            logger.error(log_msg, exc_info=True)
            raise Exception(f"Маршрутизация не удалась для модели {model}") from e

    async def _select_deployment_with_least_busy_only(
        self,
        healthy_deployments: List[Dict[str, Any]],
        model: str,
        task_type: str,
    ) -> Dict[str, Any]:
        """Выбор развертывания только на основе least-busy стратегии без ML предиктора.

        Используется для task_type != 'chat' когда не требуется ML предсказание времени ответа.
        Выбирает deployment с наименьшей текущей нагрузкой или первый доступный если
        данные о нагрузке недоступны.

        Args:
            healthy_deployments: Список работоспособных развертываний
            model: Имя модели для логирования
            task_type: Тип задачи для логирования

        Returns:
            Выбранное развертывание

        Raises:
            Exception: Если все развертывания недоступны
        """
        logger.info(
            f"LEAST_BUSY_ONLY: начинается выбор только по least-busy для task_type='{task_type}'"
        )

        try:
            # Получаем данные о нагрузке для всех deployments
            deployments_with_load = []
            for deployment in healthy_deployments:
                deployment_id = deployment.get("model_info", {}).get("id", "unknown")

                # Получаем текущую нагрузку через least-busy механизм
                current_load = self.leastbusy.get_active_requests(deployment_id)

                deployments_with_load.append(
                    {
                        "deployment": deployment,
                        "deployment_id": deployment_id,
                        "current_load": (
                            current_load if current_load is not None else float("inf")
                        ),
                    }
                )

                if current_load is not None:
                    logger.debug(
                        f"LEAST_BUSY_ONLY: {deployment_id} current_load = {current_load}"
                    )
                else:
                    logger.debug(
                        f"LEAST_BUSY_ONLY: {deployment_id} current_load = недоступно"
                    )

            # Сортируем по нагрузке (наименьшая первая)
            deployments_with_load.sort(key=lambda x: x["current_load"])

            selected_deployment = deployments_with_load[0]["deployment"]
            selected_id = deployments_with_load[0]["deployment_id"]
            selected_load = deployments_with_load[0]["current_load"]

            if selected_load == float("inf"):
                logger.info(
                    f"LEAST_BUSY_ONLY: выбрано {selected_id} (данные о нагрузке недоступны)"
                )
            else:
                logger.info(
                    f"LEAST_BUSY_ONLY: выбрано {selected_id} с нагрузкой {selected_load}"
                )

            return selected_deployment

        except Exception as e:
            logger.error(f"LEAST_BUSY_ONLY: ошибка выбора deployment: {e}")
            logger.warning(f"LEAST_BUSY_ONLY: fallback на первый доступный deployment")
            return healthy_deployments[0]

    def _get_healthy_deployments_for_model(self, model: str) -> List[Dict[str, Any]]:
        """Получение списка работоспособных развертываний для указанной модели.

        Запрашивает маршрутизатор для доступных развертываний указанной модели и
        фильтрует только работоспособные/активные развертывания. Включает детальное
        логирование для отладки проблем доступности развертываний.

        Args:
            model: Имя модели для получения развертываний (например, "chat_instruct").

        Returns:
            Список словарей конфигурации работоспособных развертываний, каждый содержит
            model_info, litellm_params и другие метаданные развертывания.

        Raises:
            ValueError: Если экземпляр маршрутизатора недоступен.

        Example:
            >>> deployments = strategy._get_healthy_deployments_for_model("chat_instruct")
            >>> print(f"Найдено {len(deployments)} работоспособных развертываний")
        """
        if not self.router:
            logger.error("SELECTION: экземпляр маршрутизатора недоступен")
            raise ValueError("Экземпляр маршрутизатора недоступен")
        try:
            logger.debug(
                f"SELECTION: получение работоспособных развертываний для модели: {model}"
            )
            healthy_deployments = self.router.get_model_list(model_name=model)
            if not healthy_deployments:
                logger.warning(
                    f"SELECTION: нет работоспособных развертываний для модели: {model}"
                )
                self._log_available_models_for_debugging()
                raise ValueError(
                    f"Нет доступных работоспособных развертываний для модели: {model}"
                )
            logger.info(
                f"SELECTION: найдено {len(healthy_deployments)} работоспособных развертываний"
            )
            self._log_deployment_details(healthy_deployments)
            return healthy_deployments
        except Exception as e:
            logger.error(
                f"SELECTION: ошибка получения развертываний для модели {model}: {e}"
            )
            logger.debug(f"SELECTION: детали исключения: {traceback.format_exc()}")
            raise ValueError(
                f"SELECTION: ошибка получения развертываний для модели {model}: {e}"
            )

    async def _select_deployment_with_predictor_and_analytics(
        self,
        healthy_deployments: List[Dict[str, Any]],
        model: str,
        messages: Optional[List] = None,
        input: Optional[Union[str, List]] = None,
        request_kwargs: Optional[Dict] = None,
        analysis_start_time: datetime = None,
        analysis_start_time_ts: float = None,
    ) -> Dict[str, Any]:
        """Расширенная версия _select_deployment_with_predictor, которая собирает аналитические данные.

        Args:
            healthy_deployments: Список конфигураций работоспособных развертываний
            model: Идентификатор модели для маршрутизации запросов
            messages: Сообщения чата для моделей завершения чата
            input: Входной текст для моделей эмбеддингов или завершения
            request_kwargs: Дополнительные параметры запроса для решений маршрутизации
            analysis_start_time: Когда начался анализ (для аналитики)
            analysis_start_time_ts: Временная метка начала анализа (для аналитики)

        Returns:
            Конфигурация выбранного развертывания
        """
        logger.info("ANALYTICS: начинается расширенный выбор со сбором аналитики")
        prompt = self._extract_prompt_for_prediction(messages, input)
        logger.debug(f"PREDICTOR: извлечена длина промпта: {len(prompt)} символов")
        logger.info(
            f"HYBRID: используется вес балансировки нагрузки: {LOAD_BALANCING_WEIGHT}"
        )
        try:
            await self.enrich_deployments_with_gpustack_data(healthy_deployments)
            logger.debug(
                f"GPUSTACK: успешно обогащено {len(healthy_deployments)} развертываний из кэша"
            )
        except Exception as e:
            logger.warning(
                f"GPUSTACK: обогащение не удалось: {e}, используются исходные развертывания"
            )
        try:
            selected_deployment, evaluations = (
                await self._select_deployment_with_hybrid_scoring(
                    healthy_deployments=healthy_deployments,
                    prompt=prompt,
                    load_balancing_weight=LOAD_BALANCING_WEIGHT,
                    return_full_analysis=True,
                )
            )
            self.analytics.collect_analytics_for_deployments(
                request_kwargs=request_kwargs,
                model=model,
                messages=messages,
                input=input,
                selected_deployment=selected_deployment,
                evaluations=evaluations,
                analysis_start_time=analysis_start_time,
                analysis_start_time_ts=analysis_start_time_ts,
                load_balancing_weight=LOAD_BALANCING_WEIGHT,
                selection_method="hybrid",
            )
            return selected_deployment
        except Exception as e:
            logger.error(f"HYBRID: гибридный выбор не удался: {e}")
            logger.debug(f"HYBRID: трассировка ошибки: {traceback.format_exc()}")
            logger.warning("HYBRID: откат к методу только ML-предсказания")
            return await self._fallback_to_ml_prediction_only(
                healthy_deployments, prompt
            )

    async def _fallback_to_ml_prediction_only(
        self, healthy_deployments: List[Dict[str, Any]], prompt: str
    ) -> Dict[str, Any]:
        """Резервный метод, использующий только ML-предсказания при сбое гибридной оценки.

        Этот метод обеспечивает безопасный откат при сбое интеграции least-busy,
        гарантируя, что маршрутизатор продолжает функционировать только с ML-предсказаниями.

        Args:
            healthy_deployments: Список доступных конфигураций развертываний
            prompt: Входной промпт для ML-предсказания

        Returns:
            Конфигурация выбранного развертывания

        Raises:
            ValueError: Если все развертывания перегружены
        """
        logger.info(
            f"FALLBACK: использование чистого ML-предсказания для {len(healthy_deployments)} развертываний"
        )
        predictions = await self._generate_ml_predictions(healthy_deployments, prompt)
        if not predictions:
            logger.error("FALLBACK: все развертывания перегружены")
            raise ValueError("Все развертывания в настоящее время перегружены")
        predictions.sort(key=lambda x: x["predicted_time"])
        logger.debug(
            f"FALLBACK: отсортировано {len(predictions)} предсказаний по времени ответа"
        )
        self._log_prediction_ranking(predictions, "FALLBACK")
        selected = predictions[0]["deployment"]
        selected_id = predictions[0]["model_id"]
        selected_time = predictions[0]["predicted_time"]
        logger.info(
            f"FALLBACK: выбрано развертывание: {selected_id} с предсказанным временем: {selected_time:.3f}с"
        )
        return selected

    async def _generate_ml_predictions(
        self, healthy_deployments: List[Dict[str, Any]], prompt: str
    ) -> List[Dict[str, Any]]:
        """Генерация ML-предсказаний для всех работоспособных развертываний.

        Args:
            healthy_deployments: Список конфигураций развертываний
            prompt: Входной промпт для предсказания

        Returns:
            Список результатов предсказания с информацией о развертывании
        """
        predictions = []
        for i, deployment in enumerate(healthy_deployments):
            model_id = deployment.get("model_info", {}).get("id", "unknown")
            logger.debug(
                f"FALLBACK: анализ развертывания {i + 1}/{len(healthy_deployments)}: {model_id}"
            )
            predicted_time = await self.predictor._predict_response_time_for_deployment(
                deployment, prompt
            )
            logger.debug(f"FALLBACK: результат для {model_id}: {predicted_time:.3f}с")
            if predicted_time != float("inf"):
                predictions.append(
                    {
                        "deployment": deployment,
                        "model_id": model_id,
                        "predicted_time": predicted_time,
                    }
                )
                logger.debug(
                    f"FALLBACK: добавлен {model_id} в кандидаты (предсказано: {predicted_time:.3f}с)"
                )
            else:
                logger.warning(
                    f"FALLBACK: пропущен {model_id} - предсказан как перегруженный (inf)"
                )
        return predictions

    async def async_routing_strategy_init(self) -> None:
        """
        Асинхронная инициализация стратегии маршрутизации.

        Этот метод выполняет проверки работоспособности и валидирует соединение
        с сервисом предсказания асинхронно.

        Raises:
            Exception: Если сервис предсказания недоступен
        """
        logger.info("INIT: начата асинхронная инициализация")
        loop = asyncio.get_event_loop()
        is_healthy = await loop.run_in_executor(
            None, self.predictor.predictor_client.health_check
        )
        if not is_healthy:
            logger.error("PREDICTOR: сервис недоступен")
            raise Exception("PREDICTOR: сервис недоступен")
        try:
            asyncio.create_task(self._start_background_services())
            logger.info("INIT: GPUStack background services запущены")
        except Exception as e:
            logger.warning(
                f"INIT: не удалось запустить GPUStack background services: {e}"
            )
        logger.info("INIT: асинхронная инициализация успешно завершена")

    async def async_get_available_deployment(
        self,
        model: str,
        request_kwargs: Dict,
        messages: Optional[List[Dict[str, str]]] = None,
        input: Optional[Union[str, List]] = None,
        specific_deployment: Optional[bool] = False,
    ) -> Dict[str, Any]:
        """
        Асинхронная версия get_available_deployment.

        Этот метод обеспечивает неблокирующий выбор развертывания с использованием
        нативной асинхронной реализации с интеграцией GPUStack.

        Args:
            model: Идентификатор модели для маршрутизации запросов
            request_kwargs: Дополнительные параметры запроса для решений маршрутизации
            messages: Сообщения чата для моделей завершения чата
            input: Входной текст для моделей эмбеддингов или завершения
            specific_deployment: Получать ли конкретное развертывание

        Returns:
            Словарь конфигурации выбранного развертывания

        Raises:
            ValueError: Если для модели нет доступных работоспособных развертываний
        """
        logger.info(
            f"SELECTION: вызван async_get_available_deployment для модели: {model}"
        )
        return await self.get_available_deployment(
            model, messages, input, specific_deployment, request_kwargs
        )
