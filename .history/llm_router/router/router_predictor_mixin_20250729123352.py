from typing import Dict, List, Optional, Any, Tuple
import asyncio
import time
import traceback

from llm_router.logging_utils import get_logger
from llm_router.predictor.predictor_client import (
    PredictorClientError,
    create_predictor_client_from_env,
)
from llm_router.router.task_type_utils import get_task_type_from_model_name

"""
Интеграция с внешним ML сервисом предсказания времени ответа.

Этот модуль предоставляет методы для взаимодействия с сервисом llm_time_predictor
через HTTP API для получения предсказаний времени ответа для LLM развертываний.
"""

logger = get_logger(__name__)
PREDICTOR_SEMAPHORE = asyncio.Semaphore(3)


class PredictorIntegration:
    """Класс для интеграции с сервисом предсказания.

    Этот класс предоставляет функциональность для интеграции с внешним ML сервисом
    предсказания времени ответа. Включает методы для извлечения промптов из сообщений,
    вызова API предсказателя и обработки расширенных параметров от GPUStack.

    Сервис предсказания используется для оценки времени ответа LLM развертываний
    на основе различных факторов: характеристики промпта, спецификации оборудования,
    конфигурация модели и текущая нагрузка.
    """

    def __init__(
        self,
        router_instance,
        default_task_type: str,
    ):
        """Инициализация класса интеграции с предсказателем.

        Args:
            router_instance: Экземпляр роутера для доступа к методам роутера
            default_task_type: Тип ML-задачи по умолчанию
        """
        self._router = router_instance
        self.default_task_type = default_task_type

        # Инициализируем predictor_client внутри класса
        logger.info("PREDICTOR: инициализация HTTP клиента")
        try:
            self.predictor_client = create_predictor_client_from_env()
            if not self.predictor_client.health_check():
                raise ConnectionError("PREDICTOR: service health check failed")
            logger.info("PREDICTOR: Connected to service successfully")
        except Exception as e:
            logger.error(f"PREDICTOR: Client initialization failed: {e}")
            logger.debug(f"PREDICTOR: Client error traceback: {traceback.format_exc()}")
            raise ConnectionError(f"PREDICTOR: Cannot connect to service: {e}")

    def _extract_prompt_from_messages(
        self, messages: Optional[List[Dict[str, str]]]
    ) -> str:
        """Извлечь текст промпта из сообщений чата.

                Args:
                    messages: Список сообщений чата с ключами 'role' и 'content'.
                        Каждое сообщение должно содержать 'role' (например, 'user', 'assistant')
                        и 'content' (текст сообщения).

                Returns:
                    Объединенная строка промпта из всех сообщений. Если messages None или пустой,
                    возвращает пустую строку.

                Example:
                    >>> messages = [
                    ...     {"role": "user", "content": "Привет"},
                    ...     {"role": "assistant", "content": "Привет!"}
                    ... ]
                    >>> prompt = self._extract_prompt_from_messages(messages)
                    >>> print(prompt)  # "user: Привет
        assistant: Привет!"
        """
        if not messages:
            return ""
        prompt_parts = []
        for msg in messages:
            role = msg.get("role", "")
            content = msg.get("content", "")
            if content:
                prompt_parts.append(f"{role}: {content}" if role else content)
        return "\n".join(prompt_parts)

    def _predict_response_time(
        self,
        task_type: str,
        prompt: str,
        hardware: str,
        model_name: str,
        instances: int = 1,
        concurrent_requests: int = 1,
        **kwargs,
    ) -> float:
        """Получить предсказание от внешнего ML сервиса через HTTP API.

        Этот метод вызывает сервис llm_time_predictor для получения предсказаний времени ответа
        на основе характеристик промпта и конфигурации развертывания.
        Валидирует все входные данные, выполняет API вызов с корректной обработкой ошибок
        и проверяет ответ перед возвратом.

        Args:
            task_type: Тип ML задачи (например, 'chat', 'completion').
            prompt: Входной текст для анализа времени ответа.
            hardware: Спецификация оборудования для развертывания (например, 'A100', '2xV100').
            model_name: Название LLM модели для предсказания.
            instances: Количество доступных экземпляров модели. По умолчанию 1.
            concurrent_requests: Количество конкурентных запросов для симуляции. По умолчанию 1.
            **kwargs: Дополнительные параметры для сервиса предсказания.

        Returns:
            Предсказанное время ответа в секундах. Возвращает float('inf'), если
            модель предсказана как перегруженная.

        Raises:
            PredictorClientError: Если HTTP API вызов не удался или вернул некорректные данные.
            ValueError: Если обязательные параметры отсутствуют или некорректны.

        Example:
            >>> time_pred = self._predict_response_time(
            ...     task_type="chat",
            ...     prompt="Привет, как дела?",
            ...     hardware="A100",
            ...     model_name="llama-7b",
            ...     instances=2,
            ...     concurrent_requests=3
            ... )
            >>> print(f"Predicted time: {time_pred:.2f}s")
        """
        logger.debug("PREDICTOR: начало вызова ML сервиса API")
        logger.debug(
            f"PREDICTOR: URL сервиса: {getattr(self.predictor_client, 'base_url', 'неизвестно')}"
        )
        logger.debug("PREDICTOR: параметры API запроса:")
        logger.debug(f"PREDICTOR:   task_type = '{task_type}'")
        logger.debug(f"PREDICTOR:   model = '{model_name}'")
        logger.debug(f"PREDICTOR:   hardware = '{hardware}'")
        logger.debug(f"PREDICTOR:   instances = {instances}")
        logger.debug(f"PREDICTOR:   concurrent_requests = {concurrent_requests}")
        logger.debug(f"PREDICTOR:   prompt_length = {len(prompt)} символов")
        if kwargs:
            logger.debug(f"PREDICTOR:   дополнительные_параметры = {kwargs}")
        if len(prompt) <= 200:
            logger.debug(f"PREDICTOR:   prompt = '{prompt}'")
        else:
            logger.debug(f"PREDICTOR:   превью_промпта = '{prompt[:200]}...'")
        if not task_type:
            logger.error("PREDICTOR: параметр task_type обязателен")
            raise ValueError("task_type не может быть пустым")
        if not model_name:
            logger.error("PREDICTOR: параметр model_name обязателен")
            raise ValueError("model_name не может быть пустым")
        if instances <= 0:
            logger.warning(
                f"PREDICTOR: некорректное значение instances ({instances}) - использую 1"
            )
            instances = 1
        if concurrent_requests <= 0:
            logger.warning(
                f"PREDICTOR: некорректное значение concurrent_requests ({concurrent_requests}) - использую 1"
            )
            concurrent_requests = 1
        try:
            logger.debug("PREDICTOR: отправка HTTP запроса к ML сервису предсказания")
            call_params = {
                "task_type": task_type,
                "prompt": prompt,
                "hardware": hardware,
                "model": model_name,
                "instances": instances,
                "requests": concurrent_requests,
            }
            logger.debug("PREDICTOR: параметры вызова predictor_client.predict():")
            for key, value in call_params.items():
                if key == "prompt" and len(str(value)) > 100:
                    logger.debug(
                        f"PREDICTOR:   {key} = '{str(value)[:100]}...' ({len(str(value))} символов)"
                    )
                else:
                    logger.debug(f"PREDICTOR:   {key} = {repr(value)}")
            if kwargs:
                logger.debug(f"PREDICTOR:   **kwargs = {kwargs}")
            start_time = time.time()
            logger.debug(f"PREDICTOR: выполнение HTTP вызова в {start_time}")
            predicted_time = self.predictor_client.predict(
                task_type=task_type,
                prompt=prompt,
                hardware=hardware,
                model=model_name,
                instances=instances,
                requests=concurrent_requests,
                **kwargs,
            )
            api_duration = time.time() - start_time
            logger.debug(f"PREDICTOR: HTTP вызов завершен за {api_duration:.3f}с")
            logger.debug("PREDICTOR: ответ API:")
            logger.debug(f"PREDICTOR:   сырой_результат = {repr(predicted_time)}")
            logger.debug(
                f"PREDICTOR:   тип_результата = {type(predicted_time).__name__}"
            )
            if predicted_time is None:
                logger.error("PREDICTOR: API вернул None результат")
                raise ValueError("API вернул None предсказание")
            if not isinstance(predicted_time, (int, float)):
                logger.error(
                    f"PREDICTOR: API вернул не числовой тип: {type(predicted_time)}, сырое значение: {repr(predicted_time)}"
                )
                raise ValueError(
                    f"API вернул не числовое предсказание: {type(predicted_time)}"
                )
            predicted_time_float = float(predicted_time)
            logger.debug(
                f"PREDICTOR:   преобразованный_результат = {predicted_time_float}"
            )
            if predicted_time_float < 0:
                logger.error(
                    f"PREDICTOR: получено отрицательное предсказание: {predicted_time_float}"
                )
                raise ValueError(
                    f"Отрицательное время предсказания: {predicted_time_float}"
                )
            if predicted_time_float == float("inf"):
                logger.warning(
                    "PREDICTOR: API вернул БЕСКОНЕЧНОСТЬ - эндпоинт вероятно перегружен"
                )
                return float("inf")
            if predicted_time_float > 3600:
                logger.warning(
                    f"PREDICTOR: получено очень высокое предсказание: {predicted_time_float}с - это может указывать на проблему"
                )
            logger.info(
                f"PREDICTOR: предсказание успешно: {predicted_time_float:.3f}с для {model_name}"
            )
            return predicted_time_float
        except Exception as e:
            error_type = type(e).__name__
            if hasattr(e, "response") and e.response is not None:
                response = e.response
                status_code = getattr(response, 'status_code', 'неизвестно')
                response_text = getattr(response, 'text', 'неизвестно')
                logger.debug(f"PREDICTOR: статус HTTP ответа: {status_code}")
                logger.debug(f"PREDICTOR: тело HTTP ответа: {response_text}")
            if hasattr(e, "status_code"):
                logger.debug(f"PREDICTOR: код статуса: {e.status_code}")
            if hasattr(e, "args") and e.args:
                logger.debug(f"PREDICTOR: аргументы ошибки: {e.args}")
            logger.debug("PREDICTOR: контекст неудачного запроса:")
            logger.debug(
                f"PREDICTOR:   URL_сервиса = {getattr(self.predictor_client, 'base_url', 'неизвестно')}"
            )
            logger.debug(f"PREDICTOR:   модель = {model_name}")
            logger.debug(f"PREDICTOR:   оборудование = {hardware}")
            logger.debug(f"PREDICTOR:   тип_задачи = {task_type}")
            logger.debug(f"PREDICTOR:   экземпляры = {instances}")
            logger.debug(f"PREDICTOR:   конкурентные_запросы = {concurrent_requests}")
            logger.debug(f"PREDICTOR:   длина_промпта = {len(prompt)}")
            logger.debug("PREDICTOR: полный трейсбек ошибки:")
            logger.debug(traceback.format_exc())
            logger.error(
                f"PREDICTOR: вызов API не удался для модели {model_name} на {hardware}: {e}",
                exc_info=True,
            )
            raise PredictorClientError(
                "Вызов API сервиса предсказания не удался"
            ) from e

    async def _predict_response_time_for_deployment(
        self, deployment: Dict[str, Any], prompt: str
    ) -> float:
        """Предсказать время ответа для конкретного развертывания с балансировкой нагрузки.

        Этот метод объединяет ML предсказания с данными о нагрузке в реальном времени для оценки
        времени ответа. Выполняет обнаружение перегрузки и возвращает соответствующие
        значения предсказания на основе текущего состояния развертывания и конфигурации.
        Дополнен реальными данными GPUStack при их доступности.

        Метод сначала извлекает базовую информацию о развертывании, затем пытается
        получить расширенные параметры от GPUStack если доступно. Проверяет перегрузку развертывания
        используя количество активных запросов и возвращается к ML предсказаниям
        для оценки времени ответа.

        Args:
            deployment: Словарь конфигурации развертывания, содержащий model_info,
                model_name и другие детали развертывания.
            prompt: Входной текст промпта для анализа предсказания.

        Returns:
            Предсказанное время ответа в секундах. Возвращает float('inf'), если
            развертывание обнаружено как перегруженное на основе текущей нагрузки против
            максимального количества конкурентных запросов.

        Raises:
            RuntimeError: Если предсказание не удалось и не может быть восстановлено.

        Example:
            >>> deployment = {
            ...     "model_info": {"id": "model-123", "hardware": "A100"},
            ...     "model_name": "llama-7b"
            ... }
            >>> time_pred = await self._predict_response_time_for_deployment(
            ...     deployment, "Привет мир"
            ... )
            >>> print(f"Predicted time: {time_pred:.2f}s")
        """
        model_info = deployment.get("model_info", {})
        deployment_id = model_info.get("id", "unknown")
        model_name = deployment.get("model_name", "unknown")
        logger.debug(
            f"PREDICTOR: начало предсказания для развертывания {deployment_id}"
        )
        logger.debug(f"PREDICTOR: deployment_id: {deployment_id}")
        logger.debug(f"PREDICTOR: model_name: {model_name}")
        logger.debug(f"PREDICTOR: ключи deployment: {list(deployment.keys())}")
        logger.debug(f"PREDICTOR: ключи model_info: {list(model_info.keys())}")
        logger.debug(f"PREDICTOR: полное развертывание: {deployment}")
        try:
            logger.debug(f"PREDICTOR: определение типа задачи для модели: {model_name}")
            task_type = get_task_type_from_model_name(model_name)
            predictor_params = (
                {}
            )  # Больше не используем дополнительные параметры из конфигурации
            logger.debug(f"PREDICTOR: task_type: {task_type}")
            logger.debug(f"PREDICTOR: predictor_params: {predictor_params}")
            if not task_type:
                logger.warning(
                    f"PREDICTOR: тип задачи не найден для {model_name} - использую по умолчанию: {self.default_task_type}"
                )
                task_type = self.default_task_type
            hardware = model_info.get("hardware", "unknown")
            actual_model_name = model_info.get("model_name", model_name)
            instances = model_info.get("instances", 1)
            logger.debug(
                f"PREDICTOR: {deployment_id} -> начальные значения - модель: {actual_model_name}, оборудование: {hardware}, экземпляры: {instances}"
            )
            logger.debug(f"PREDICTOR: {deployment_id} -> deployment: {deployment}")
            enhanced_params: Dict[str, Any] = deployment.get("gpustack_params", {})
            if not enhanced_params:
                logger.error(
                    f"PREDICTOR: отсутствуют gpustack_params для развертывания {deployment_id}. GPUStack интеграция недоступна или обогащение не было выполнено. Система не может функционировать без данных о железе."
                )
                raise RuntimeError(
                    "gpustack_params отсутствуют — система не может функционировать без GPUStack интеграции"
                )
            logger.debug(f"PREDICTOR: получение данных least-busy для {deployment_id}")
            least_busy_data = self._router.leastbusy.get_active_requests(deployment_id)
            logger.debug(f"PREDICTOR: least_busy_data: {least_busy_data}")
            if least_busy_data is not None:
                current_load = least_busy_data
                logger.debug(
                    f"PREDICTOR: {deployment_id} -> использую least-busy (размер очереди): {current_load}"
                )
                if enhanced_params and "active_requests" in enhanced_params:
                    gpustack_active = enhanced_params["active_requests"]
                    logger.debug(
                        f"PREDICTOR: {deployment_id} -> GPUStack сообщает {gpustack_active} активных запросов (для информации, кэшированные)"
                    )
                    if abs(gpustack_active - least_busy_data) > 2:
                        logger.warning(
                            f"PREDICTOR: {deployment_id} -> значительное расхождение: least-busy очередь={least_busy_data}, GPUStack активные={gpustack_active} (возможно устаревшие)"
                        )
            elif enhanced_params and "active_requests" in enhanced_params:
                current_load = enhanced_params["active_requests"]
                logger.warning(
                    f"PREDICTOR: {deployment_id} -> least-busy недоступны, используем GPUStack активные запросы (могут быть устаревшими): {current_load}"
                )
            else:
                current_load = None
                logger.debug(
                    f"PREDICTOR: {deployment_id} -> данные о нагрузке недоступны"
                )
            if current_load is not None:
                logger.info(
                    f"LEAST_BUSY: {deployment_id} текущая нагрузка: {current_load}"
                )
                concurrent_requests = current_load + 1
                logger.debug(
                    f"PREDICTOR: {deployment_id} -> конкурентные запросы для ML предсказания: {concurrent_requests} (нагрузка {current_load} + 1)"
                )
            else:
                logger.debug(
                    f"PREDICTOR: {deployment_id} -> данные о нагрузке недоступны, использую консервативную оценку"
                )
                concurrent_requests = 1
                logger.debug(
                    f"PREDICTOR: использую резервное concurrent_requests: {concurrent_requests}"
                )
            if enhanced_params:
                hardware = enhanced_params.get("hardware", hardware)
                instances = enhanced_params.get("instances", instances)
                actual_model_name = enhanced_params.get("model_name", actual_model_name)
                logger.debug(
                    f"PREDICTOR: {deployment_id} -> применены enhanced_params: hardware={hardware}, instances={instances}, model={actual_model_name}"
                )
            else:
                if hardware == "unknown" and "hardware" in predictor_params:
                    hardware = predictor_params["hardware"]
                if instances == 1 and "instances" in predictor_params:
                    instances = predictor_params["instances"]
                if "model_name" in predictor_params:
                    actual_model_name = predictor_params["model_name"]
                elif actual_model_name == "unknown":
                    actual_model_name = model_name
            logger.debug(f"PREDICTOR: {deployment_id} -> вызов ML сервиса")
            logger.debug(
                f"PREDICTOR: {deployment_id} -> запрос: задача={task_type}, оборудование={hardware}, модель={actual_model_name}"
            )
            logger.debug(
                f"PREDICTOR: {deployment_id} -> запрос: экземпляры={instances}, конкурентные={concurrent_requests}, длина_промпта={len(prompt)}"
            )
            explicitly_handled = {"hardware", "model_name", "instances", "requests"}
            additional_params = {
                k: v for k, v in predictor_params.items() if k not in explicitly_handled
            }
            if additional_params:
                logger.debug(
                    f"PREDICTOR: {deployment_id} -> передача дополнительных параметров в API: {list(additional_params.keys())}"
                )
                logger.debug(
                    f"PREDICTOR: {deployment_id} -> дополнительные параметры: {additional_params}"
                )
            else:
                logger.debug(
                    f"PREDICTOR: {deployment_id} -> нет дополнительных параметров для передачи в API"
                )
            async with PREDICTOR_SEMAPHORE:
                base_prediction = await asyncio.to_thread(
                    self._predict_response_time,
                    task_type,
                    prompt,
                    hardware,
                    actual_model_name,
                    instances,
                    concurrent_requests,
                    **additional_params,
                )
            logger.debug(
                f"PREDICTOR: базовое предсказание от ML сервиса: {base_prediction:.3f}с"
            )
            if base_prediction <= 0:
                logger.warning(
                    f"PREDICTOR: {deployment_id} -> некорректное предсказание ({base_prediction}) - использую резервное 30с"
                )
                base_prediction = 30.0
            if base_prediction == float("inf"):
                logger.warning(
                    f"PREDICTOR: {deployment_id} -> ML сервис вернул БЕСКОНЕЧНОСТЬ"
                )
                return float("inf")
            if base_prediction > 60:
                logger.warning(
                    f"PREDICTOR: {deployment_id} -> высокое предсказание: {base_prediction:.3f}с"
                )
            logger.info(
                f"PREDICTOR: результат предсказания для {deployment_id}: {base_prediction:.3f}с"
            )
            return base_prediction
        except Exception as e:
            logger.debug(
                f"PREDICTOR: {deployment_id} -> трейсбек ошибки: {traceback.format_exc()}"
            )
            logger.debug(f"PREDICTOR: тип ошибки: {type(e).__name__}")
            logger.debug(f"PREDICTOR: аргументы ошибки: {e.args}")
            logger.error(
                f"PREDICTOR: предсказание не удалось для развертывания {deployment_id}: {e}",
                exc_info=True,
            )
            raise RuntimeError(
                f"Предсказание не удалось для развертывания {deployment_id}"
            ) from e
