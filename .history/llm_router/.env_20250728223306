# Настройки для подключения к LiteLLM API
LITELLM_API_BASE=http://localhost:4000
LITELLM_MASTER_KEY=sk-1234
LITELLM_SALT_KEY=sk-1234
LITELLM_LOG=DEBUG

# Настройки для GPUStack
GPUSTACK_URL=http://localhost:80
GPUSTACK_INTERNAL=http://host.docker.internal:80

GPUSTACK_TOKEN='96ba136c23f4eca2f36432be90a9ce33'
GPUSTACK_PASSWORD='Rxq3^iBK!HYu'
GPUSTACK_KEY='gpustack_aa6be0984059ad94_6d57e9b6dc955cb00a3d7acb81edc572'

# Настройки для сервиса предиктора времени ответа
PREDICT_URL=http://localhost:8007
PREDICT_URL_INTERNAL=http://host.docker.internal:8007
PREDICT_TIMEOUT=5

# Настройки базы данных
DATABASE_URL="********************************************/litellm"
DATABASE_URL_HOST="postgresql://llmproxy:dbpassword9090@localhost:5432/litellm"

# Настройки миграции litellm
DISABLE_SCHEMA_UPDATE=True

# Параметры миграции аналитики
USE_ANALYTICS_MIGRATE=True
ANALYTICS_MIGRATION_DIR=
DISABLE_ANALYTICS_SCHEMA_UPDATE=
ANALYTICS_ENABLED=true

