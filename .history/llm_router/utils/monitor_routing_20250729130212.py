#!/usr/bin/env python3
"""
Real-time Custom Router Monitoring Script.

This script monitors and analyzes router decisions in real-time by parsing
Docker container logs and extracting routing-related information.
"""
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional
import argparse
import asyncio
import json
import logging
import re
import subprocess
import sys
import time
import traceback

from llm_router.logging_utils import get_logger


sys.path.append(str(Path(__file__).parent.parent))
logger = get_logger(__name__)


class RoutingMonitor:
    """Monitor and analyze router operations in real-time."""

    def __init__(self, container_name: Optional[str] = None) -> None:
        """
        Initialize routing monitor.

        Args:
            container_name: Docker container name to monitor for routing decisions.
                          If None, will auto-detect the LiteLLM container.
        """
        self.container_name = container_name or self._detect_litellm_container()
        self.routing_decisions: List[Dict[str, Any]] = []
        self.predictor_calls: List[Dict[str, Any]] = []
        self.performance_metrics: Dict[str, Any] = {}
        logger.info(
            f"MONITOR: routing monitor initialized for container: {self.container_name}"
        )
        logger.debug(
            f"MONITOR: monitor data structures initialized - routing_decisions: {len(self.routing_decisions)}, predictor_calls: {len(self.predictor_calls)}"
        )

    def _detect_litellm_container(self) -> str:
        """
        Auto-detect LiteLLM container name from running containers.

        Returns:
            Name of the LiteLLM container.

        Raises:
            RuntimeError: When no suitable container is found.
        """
        logger.debug(f"MONITOR: starting container detection process")
        try:
            logger.debug(f"MONITOR: executing docker ps command to list containers")
            result = subprocess.run(
                ["docker", "ps", "--format", "{{.Names}}"],
                capture_output=True,
                text=True,
                check=True,
            )
            container_names = result.stdout.strip().split("\n")
            logger.debug(
                f"MONITOR: found {len(container_names)} running containers: {container_names}"
            )
            litellm_patterns = ["litellm", "llm_router", "filin-litellm", "router"]
            logger.debug(
                f"MONITOR: searching for LiteLLM containers using patterns: {litellm_patterns}"
            )
            for container_name in container_names:
                container_lower = container_name.lower()
                for pattern in litellm_patterns:
                    if pattern in container_lower:
                        logger.info(
                            f"MONITOR: auto-detected LiteLLM container: {container_name}"
                        )
                        logger.debug(f"MONITOR: container matched pattern: {pattern}")
                        return container_name
            default_name = "llm_router-litellm-1"
            logger.warning(
                f"MONITOR: no LiteLLM container auto-detected, using default: {default_name}"
            )
            logger.debug(f"MONITOR: available containers were: {container_names}")
            return default_name
        except subprocess.CalledProcessError as e:
            logger.error(f"MONITOR: failed to detect containers: {e}")
            logger.debug(
                f"MONITOR: docker command error details: {(e.stderr if hasattr(e, 'stderr') else 'N/A')}"
            )
            default_name = "llm_router-litellm-1"
            logger.warning(f"MONITOR: using default container name: {default_name}")
            return default_name
        except FileNotFoundError:
            logger.error(
                "MONITOR: docker not found - ensure Docker is installed and available in PATH"
            )
            raise RuntimeError("Docker is not available")

    def parse_routing_log(self, log_line: str) -> Optional[Dict[str, Any]]:
        """
        Parse log line to extract routing information.

        Analyzes log entries for routing decisions, time predictions, task types,
        predictor loading events, and routing errors.

        Args:
            log_line: Raw log line from container output.

        Returns:
            Dictionary containing parsed routing information, or None if line
            doesn't contain relevant routing data.
        """
        logger.debug(
            f"MONITOR: parsing log line: {log_line[:100]}{('...' if len(log_line) > 100 else '')}"
        )
        try:
            if "Selected endpoint:" in log_line:
                match = re.search("Selected endpoint: (\\S+)", log_line)
                if match:
                    parsed_data = {
                        "type": "endpoint_selection",
                        "endpoint": match.group(1),
                        "timestamp": self._extract_timestamp(log_line),
                    }
                    logger.debug(
                        f"MONITOR: extracted endpoint selection: {parsed_data}"
                    )
                    return parsed_data
            if "Predicted time:" in log_line:
                match = re.search("Predicted time: ([\\d.]+)", log_line)
                if match:
                    parsed_data = {
                        "type": "time_prediction",
                        "predicted_time": float(match.group(1)),
                        "timestamp": self._extract_timestamp(log_line),
                    }
                    logger.debug(f"MONITOR: extracted time prediction: {parsed_data}")
                    return parsed_data
            if "Task type determined:" in log_line:
                match = re.search("Task type determined: (\\w+)", log_line)
                if match:
                    parsed_data = {
                        "type": "task_type",
                        "task_type": match.group(1),
                        "timestamp": self._extract_timestamp(log_line),
                    }
                    logger.debug(f"MONITOR: extracted task type: {parsed_data}")
                    return parsed_data
            if "Predictor loaded successfully" in log_line:
                match = re.search("(\\w+) predictor loaded successfully", log_line)
                if match:
                    parsed_data = {
                        "type": "predictor_loaded",
                        "predictor_type": match.group(1),
                        "timestamp": self._extract_timestamp(log_line),
                    }
                    logger.debug(f"MONITOR: extracted predictor loaded: {parsed_data}")
                    return parsed_data
            if "ERROR" in log_line and (
                "routing" in log_line.lower() or "predictor" in log_line.lower()
            ):
                parsed_data = {
                    "type": "routing_error",
                    "error_message": log_line.strip(),
                    "timestamp": self._extract_timestamp(log_line),
                }
                logger.debug(f"MONITOR: extracted routing error: {parsed_data}")
                return parsed_data
        except Exception as e:
            logger.debug(
                f"MONITOR: log parsing error for line: {log_line[:100]}... - error: {e}"
            )
            logger.debug(f"MONITOR: parsing error type: {type(e).__name__}")
        return None

    def _extract_timestamp(self, log_line: str) -> str:
        """
        Extract timestamp from log line using regex patterns.

        Args:
            log_line: Raw log line containing timestamp.

        Returns:
            Extracted timestamp in ISO format, or current time if not found.
        """
        logger.debug(f"MONITOR: extracting timestamp from: {log_line[:50]}...")
        timestamp_match = re.search(
            "(\\d{4}-\\d{2}-\\d{2}[T ]\\d{2}:\\d{2}:\\d{2})", log_line
        )
        if timestamp_match:
            timestamp = timestamp_match.group(1)
            logger.debug(f"MONITOR: extracted timestamp: {timestamp}")
            return timestamp
        fallback_timestamp = datetime.now().isoformat()
        logger.debug(
            f"MONITOR: no timestamp found, using current time: {fallback_timestamp}"
        )
        return fallback_timestamp

    async def monitor_container_logs(self, duration_minutes: int = 10) -> None:
        """
        Monitor container logs in real-time for specified duration.

        Streams Docker logs continuously and processes routing-related entries.
        Uses non-blocking I/O to handle log streaming efficiently.

        Args:
            duration_minutes: Duration of monitoring session in minutes.

        Raises:
            FileNotFoundError: When Docker is not available on the system.
            subprocess.SubprocessError: When Docker command fails.
        """
        logger.info(
            f"MONITOR: starting log monitoring for container '{self.container_name}' for {duration_minutes} minutes"
        )
        logger.debug(f"MONITOR: duration: {duration_minutes * 60} seconds")
        try:
            logger.debug(f"MONITOR: initializing Docker logs subprocess")
            process = subprocess.Popen(
                ["docker", "logs", "-f", "--tail", "100", self.container_name],
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1,
            )
            start_time = time.time()
            end_time = start_time + duration_minutes * 60
            logger.info(f"MONITOR: log monitoring started - waiting for log entries")
            logger.debug(f"MONITOR: monitoring window: {duration_minutes} minutes from now")
            lines_processed = 0
            routing_lines_found = 0
            
            if process.stdout is None:
                logger.error("MONITOR: failed to capture container stdout")
                raise RuntimeError("Docker process stdout is not available")
                
            while time.time() < end_time:
                try:
                    line = process.stdout.readline()
                    if line:
                        line = line.strip()
                        if line:
                            lines_processed += 1
                            logger.debug(
                                f"MONITOR: processing line {lines_processed}: {line[:100]}..."
                            )
                            parsed_info = self.parse_routing_log(line)
                            if parsed_info:
                                self._process_routing_info(parsed_info)
                                routing_lines_found += 1
                            if any(
                                (
                                    keyword in line.lower()
                                    for keyword in [
                                        "routing",
                                        "predictor",
                                        "endpoint",
                                        "selected",
                                        "predicted",
                                    ]
                                )
                            ):
                                logger.debug(f"MONITOR: routing activity line: {line}")
                    if process.poll() is not None:
                        logger.warning(
                            f"MONITOR: docker monitoring process terminated unexpectedly"
                        )
                        logger.debug(
                            f"MONITOR: process return code: {process.returncode}"
                        )
                        break
                except Exception as e:
                    logger.error(f"MONITOR: log reading error: {e}")
                    logger.debug(f"MONITOR: log reading error type: {type(e).__name__}")
                    break
                await asyncio.sleep(0.1)
            logger.debug(f"MONITOR: terminating Docker logs process")
            process.terminate()
            process.wait()
            logger.info(
                f"MONITOR: log monitoring completed - processed {lines_processed} lines, found {routing_lines_found} routing events"
            )
        except FileNotFoundError:
            logger.error(
                "MONITOR: Docker not found - ensure Docker is installed and available in PATH"
            )
            raise RuntimeError("Docker is not available")
        except Exception as e:
            logger.error(f"MONITOR: monitoring error: {e}")
            logger.debug(f"MONITOR: monitoring error type: {type(e).__name__}")
            raise

    def _process_routing_info(self, info: Dict[str, Any]) -> None:
        """
        Process and store routing information from parsed logs.

        Categorizes routing events and maintains separate collections for
        different types of routing data to enable detailed analysis.

        Args:
            info: Parsed routing information dictionary with type and metadata.
        """
        info_type = info["type"]
        logger.debug(f"MONITOR: processing routing info type: {info_type}")
        logger.debug(f"MONITOR: full routing info: {info}")
        if info_type == "endpoint_selection":
            self.routing_decisions.append(info)
            logger.info(f"MONITOR: endpoint selected - {info['endpoint']}")
            logger.debug(
                f"MONITOR: total routing decisions: {len(self.routing_decisions)}"
            )
        elif info_type == "time_prediction":
            self.predictor_calls.append(info)
            logger.info(f"MONITOR: time prediction - {info['predicted_time']:.3f}s")
            logger.debug(f"MONITOR: total predictor calls: {len(self.predictor_calls)}")
        elif info_type == "task_type":
            logger.info(f"MONITOR: task type determined: {info['task_type']}")
        elif info_type == "predictor_loaded":
            logger.info(
                f"MONITOR: predictor loaded successfully - {info['predictor_type']}"
            )
        elif info_type == "routing_error":
            logger.error(f"MONITOR: routing error detected: {info['error_message']}")

    def analyze_routing_patterns(self) -> Dict[str, Any]:
        """
        Analyze routing patterns from collected monitoring data.

        Calculates endpoint usage statistics, time prediction metrics,
        and identifies the most frequently used routing decisions.

        Returns:
            Dictionary containing comprehensive routing analysis including
            endpoint usage counts, prediction statistics, and patterns.
        """
        logger.info(f"MONITOR: starting routing pattern analysis")
        logger.debug(
            f"MONITOR: analysis data - routing decisions: {len(self.routing_decisions)}, predictor calls: {len(self.predictor_calls)}"
        )
        if not self.routing_decisions:
            logger.warning(f"MONITOR: no routing data available for analysis")
            return {"message": "No routing data available for analysis"}
        endpoint_usage = {}
        for decision in self.routing_decisions:
            endpoint = decision["endpoint"]
            endpoint_usage[endpoint] = endpoint_usage.get(endpoint, 0) + 1
        logger.debug(f"MONITOR: endpoint usage analysis: {endpoint_usage}")
        prediction_stats = {}
        if self.predictor_calls:
            times = [call["predicted_time"] for call in self.predictor_calls]
            prediction_stats = {
                "total_predictions": len(times),
                "avg_predicted_time": sum(times) / len(times),
                "min_predicted_time": min(times),
                "max_predicted_time": max(times),
            }
            logger.debug(f"MONITOR: prediction statistics: {prediction_stats}")
        else:
            logger.warning(f"MONITOR: no prediction data available for analysis")
        analysis_result = {
            "total_routing_decisions": len(self.routing_decisions),
            "endpoint_usage": endpoint_usage,
            "prediction_stats": prediction_stats,
            "most_used_endpoint": (
                max(endpoint_usage.items(), key=lambda x: x[1])[0]
                if endpoint_usage
                else None
            ),
        }
        logger.info(
            f"MONITOR: pattern analysis completed - {analysis_result['total_routing_decisions']} decisions analyzed"
        )
        return analysis_result

    def generate_report(self) -> str:
        """
        Generate comprehensive monitoring report.

        Creates a formatted report summarizing routing decisions, endpoint usage,
        and time prediction statistics from the monitoring session.

        Returns:
            Formatted string report with routing analysis and statistics.
        """
        logger.info(f"MONITOR: generating monitoring report")
        analysis = self.analyze_routing_patterns()
        report = f"\nROUTING MONITORING REPORT\n{'=' * 50}\n\nGeneral Statistics:\n- Total routing decisions: {analysis.get('total_routing_decisions', 0)}\n- Total time predictions: {analysis.get('prediction_stats', {}).get('total_predictions', 0)}\n\nEndpoint Usage:\n"
        endpoint_usage = analysis.get("endpoint_usage", {})
        for endpoint, count in sorted(
            endpoint_usage.items(), key=lambda x: x[1], reverse=True
        ):
            percentage = (
                count / analysis["total_routing_decisions"] * 100
                if analysis["total_routing_decisions"] > 0
                else 0
            )
            report += f"- {endpoint}: {count} times ({percentage:.1f}%)\n"
        if analysis.get("most_used_endpoint"):
            report += f"\nMost used endpoint: {analysis['most_used_endpoint']}\n"
        prediction_stats = analysis.get("prediction_stats", {})
        if prediction_stats:
            report += f"\nTime Prediction Statistics:\n- Average predicted time: {prediction_stats.get('avg_predicted_time', 0):.3f}s\n- Minimum time: {prediction_stats.get('min_predicted_time', 0):.3f}s\n- Maximum time: {prediction_stats.get('max_predicted_time', 0):.3f}s\n"
        logger.info(f"MONITOR: monitoring report generated successfully")
        logger.debug(f"MONITOR: report length: {len(report)} characters")
        return report

    def save_monitoring_data(self, filename: Optional[str] = None) -> str:
        """
        Save monitoring data to JSON file.

        Exports all collected routing decisions, predictor calls, and analysis
        results to a timestamped JSON file for further processing or archival.

        Args:
            filename: Optional custom filename. If not provided, generates
                     timestamp-based filename.

        Returns:
            Name of the file where data was saved.

        Raises:
            IOError: When file cannot be written to disk.
        """
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"routing_monitoring_{timestamp}.json"
        logger.info(f"MONITOR: saving monitoring data to file: {filename}")
        data = {
            "timestamp": datetime.now().isoformat(),
            "routing_decisions": self.routing_decisions,
            "predictor_calls": self.predictor_calls,
            "analysis": self.analyze_routing_patterns(),
        }
        logger.debug(
            f"MONITOR: data to save - routing_decisions: {len(data['routing_decisions'])}, predictor_calls: {len(data['predictor_calls'])}"
        )
        try:
            with open(filename, "w", encoding="utf-8") as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            logger.info(f"MONITOR: monitoring data saved to {filename}")
            logger.debug(f"MONITOR: file size: {len(json.dumps(data))} bytes")
            return filename
        except IOError as e:
            logger.error(f"MONITOR: failed to save monitoring data: {e}")
            logger.debug(f"MONITOR: save error type: {type(e).__name__}")
            raise


async def main() -> None:
    """
    Main function to run monitoring script.

    Parses command line arguments and initiates the monitoring process.
    """
    parser = argparse.ArgumentParser(description="Monitor LiteLLM Custom Router logs.")
    parser.add_argument(
        "--container-name", help="Name of the LiteLLM Docker container to monitor."
    )
    parser.add_argument(
        "--duration",
        type=int,
        default=10,
        help="Duration of monitoring in minutes (default: 10).",
    )
    parser.add_argument(
        "--output", help="Optional output filename to save monitoring data."
    )
    args = parser.parse_args()
    logger.info(f"MONITOR: starting main monitoring script execution")
    logger.debug(
        f"MONITOR: CLI args: container_name={args.container_name}, duration={args.duration}, output={args.output}"
    )
    try:
        monitor = RoutingMonitor(container_name=args.container_name)
        await monitor.monitor_container_logs(duration_minutes=args.duration)
        analysis_report = monitor.generate_report()
        print(analysis_report)
        if args.output:
            saved_file = monitor.save_monitoring_data(filename=args.output)
            print(f"Monitoring data saved to {saved_file}")
    except Exception as e:
        logger.error(f"MONITOR: an error occurred during monitoring: {e}")
        logger.debug(
            f"MONITOR: main execution error traceback: {traceback.format_exc()}"
        )
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
