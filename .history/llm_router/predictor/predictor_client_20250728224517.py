#!/usr/bin/env python3
from typing import Optional, Dict, Any
from urllib.parse import urljoin
import logging
import os
import traceback

from llm_router.common.base_http_client import (
    BaseHTTPClient,
    HTTPClientError,
    HTTPClientTimeoutError,
    HTTPClientConnectionError,
)
from llm_router.logging_utils import get_logger
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
import requests

"""
HTTP Client for LLM Time Predictor Service.

This module provides a client for communicating with the external predictor service
that estimates response times for LLM requests based on various parameters.
"""

logger = get_logger(__name__)


class PredictorClientError(HTTPClientError):
    """Exception raised for predictor client errors."""

    pass


class PredictorClient(BaseHTTPClient):
    """
    HTTP client for LLM time predictor service.

    This client communicates with the external predictor service to get
    response time predictions for different LLM deployments based on
    hardware specifications, model types, and request parameters.

    The client supports:
    - Health checking of the predictor service
    - Prediction requests for chat and code tasks
    - Context manager for automatic session cleanup
    - Configurable timeouts and error handling

    Args:
        base_url: Base URL of the predictor service
        timeout: Request timeout in seconds

    Raises:
        PredictorClientError: For all predictor-related errors
    """

    def __init__(self, base_url: str, timeout: int = 5) -> None:
        """
        Initialize predictor client.

        Args:
            base_url: Base URL of the predictor service (e.g., http://localhost:8008)
            timeout: Request timeout in seconds
        """
        super().__init__(
            base_url=base_url,
            timeout=timeout,
            max_retries=1,
            logger_name="llm-router.predictor_client",
        )
        logger.info(f"PREDICTOR: Client initialized with URL: {self.base_url}")
        logger.debug(f"PREDICTOR: Timeout configuration: {timeout}s")

    def _create_session(self) -> requests.Session:
        """
        Create HTTP session with predictor-specific retry strategy.

        Override base class to use minimal retries for predictor service
        and handle connection errors appropriately for Docker networking.
        """
        session = requests.Session()
        retry_strategy = Retry(total=0, connect=0, read=0, status=0, backoff_factor=0)
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        session.headers.update(
            {
                "Content-Type": "application/json",
                "Accept": "application/json",
                "User-Agent": f"{self.__class__.__name__}/1.0",
            }
        )
        return session

    def _setup_authentication(self) -> Dict[str, str]:
        """
        Setup authentication headers.

        Predictor service doesn't require authentication currently.

        Returns:
            Empty dict as no authentication is needed
        """
        return {}

    def health_check(self) -> bool:
        """
        Check if predictor service is healthy by making a simple prediction request.

        Returns:
            bool: True if service is healthy, False otherwise
        """
        logger.debug("PREDICTOR: Starting health check with prediction request")
        try:
            # Make a simple prediction request to test service availability
            data = {
                "text_input": "test",
                "hardware": "1xH100",
                "model_name": "test-model",
                "concurrent_requests": 1,
                "model_instances": 1,
            }
            response = self.post("/predict/code", data=data, timeout=5)
            logger.info("PREDICTOR: Service health check successful")
            return True
        except Exception as e:
            logger.error(f"PREDICTOR: Health check request failed: {e}")
            logger.debug(f"PREDICTOR: Health check error type: {type(e).__name__}")
            return False

    def predict(
        self,
        task_type: str,
        prompt: str,
        hardware: str,
        model: str,
        instances: int = 1,
        requests: int = 1,
        length: Optional[int] = None,
    ) -> float:
        """
        Predict response time for given parameters.

        Args:
            task_type: Type of task ('chat' or 'code')
            prompt: Input prompt text
            hardware: Hardware description (e.g., 'gpu_a100', 'gpu_v100')
            model: Model name (e.g., 'chat_instruct', 'chat_coder')
            instances: Number of model instances (optional)
            requests: Number of concurrent requests (optional)
            length: Text length override (optional)

        Returns:
            float: Predicted response time in seconds

        Raises:
            PredictorClientError: If prediction fails
        """
        logger.debug("PREDICTOR: Starting prediction request")
        logger.debug(
            f"PREDICTOR: Input parameters - task_type: '{task_type}', hardware: '{hardware}', model: '{model}'"
        )
        logger.debug(
            f"PREDICTOR: Input parameters - instances: {instances}, requests: {requests}, prompt_length: {len(prompt)}"
        )
        if task_type not in ["chat", "code"]:
            log_msg = (
                f"PREDICTOR: Invalid task_type: {task_type}. Must be 'chat' or 'code'"
            )
            logger.error(log_msg)
            raise PredictorClientError(
                f"Invalid task_type: {task_type}. Must be 'chat' or 'code'"
            )
        data = {
            "text_input": prompt,
            "hardware": hardware,
            "model_name": model,
            "concurrent_requests": requests,
            "model_instances": instances,
        }
        if length is not None:
            data["length"] = length
            logger.debug(f"PREDICTOR: Using length override: {length}")
        logger.debug(f"PREDICTOR: Request data: {dict(data)}")
        try:
            endpoint = f"/predict/{task_type}"
            logger.info(f"PREDICTOR: Making API call for {model} on {hardware}")
            response_data = self.post(endpoint, data=data)
            logger.debug(f"PREDICTOR: API response data: {response_data}")
            predicted_time = response_data.get(
                "predicted_time_seconds"
            ) or response_data.get("predicted_time")
            if predicted_time is None:
                log_msg = f"PREDICTOR: Missing 'predicted_time_seconds' or 'predicted_time' in API response: {response_data}"
                logger.error(log_msg)
                raise PredictorClientError(
                    "Missing 'predicted_time_seconds' or 'predicted_time' in response"
                )
            logger.info(
                f"PREDICTOR: Prediction successful - {predicted_time}s for {task_type} task on {model}"
            )
            logger.debug(f"PREDICTOR: Prediction value type: {type(predicted_time)}")
            return float(predicted_time)
        except HTTPClientTimeoutError as e:
            log_msg = f"PREDICTOR: Request timeout after {self.timeout}s for endpoint {endpoint}: {e}"
            logger.error(log_msg, exc_info=True)
            raise PredictorClientError(f"Request timeout after {self.timeout}s") from e
        except HTTPClientConnectionError as e:
            log_msg = f"PREDICTOR: Connection error to {self.base_url}: {e}"
            logger.error(log_msg, exc_info=True)
            raise PredictorClientError("Connection error to predictor service") from e
        except HTTPClientError as e:
            if e.status_code == 400:
                log_msg = (
                    f"PREDICTOR: Bad request (400): {e}, response: {e.response_data}"
                )
                logger.error(log_msg, exc_info=True)
                raise PredictorClientError(f"Bad request (400): {e}") from e
            elif e.status_code == 500:
                log_msg = (
                    f"PREDICTOR: Server error (500): {e}, response: {e.response_data}"
                )
                logger.error(log_msg, exc_info=True)
                raise PredictorClientError(f"Server error (500): {e}") from e
            elif e.status_code == 503:
                log_msg = f"PREDICTOR: Service unavailable (503): {e}, response: {e.response_data}"
                logger.error(log_msg, exc_info=True)
                raise PredictorClientError(f"Service unavailable (503): {e}") from e
            else:
                log_msg = f"PREDICTOR: HTTP error {e.status_code}: {e}, response: {e.response_data}"
                logger.error(log_msg, exc_info=True)
                raise PredictorClientError(f"HTTP error {e.status_code}: {e}") from e
        except Exception as e:
            log_msg = f"PREDICTOR: Unexpected error during prediction: {e}"
            logger.error(log_msg, exc_info=True)
            raise PredictorClientError("Prediction request failed") from e

    def get_service_info(self) -> Optional[Dict[str, Any]]:
        """
        Get service information from health endpoint.

        Returns:
            Dict with service info or None if failed
        """
        logger.debug("PREDICTOR: Requesting service info")
        try:
            info_data = self.get("/health")
            logger.debug(f"PREDICTOR: Service info retrieved: {info_data}")
            return info_data
        except Exception as e:
            logger.error(f"PREDICTOR: Error getting service info: {e}")
            logger.debug(f"PREDICTOR: Service info error type: {type(e).__name__}")
            return None


def create_predictor_client_from_env() -> PredictorClient:
    """
    Create predictor client using environment variables with context awareness.

    Automatically detects execution context (Docker vs host) and uses appropriate URL.
    Supports ServiceConfig for context-aware URL resolution with fallback to
    environment variables for backward compatibility.

    Returns:
        PredictorClient: Configured client instance

    Environment Variables:
        PREDICT_URL: Predictor service URL (fallback)
        PREDICT_TIMEOUT: Request timeout in seconds (default: 5)
        PREDICT_URL_INTERNAL: Internal URL for Docker environments

    Notes:
        - Uses ServiceConfig for automatic Docker/host detection
        - Falls back to environment variables if ServiceConfig unavailable
        - Provides comprehensive logging for troubleshooting
    """
    logger.debug("FACTORY: Creating predictor client from environment")
    logger.warning("FACTORY: Temporarily bypassing ServiceConfig for testing")
    predict_url = os.getenv("PREDICT_URL", "http://localhost:8007")

    # Check if running in Docker environment
    if os.path.exists("/.dockerenv") or os.getenv("RUNNING_IN_DOCKER"):
        logger.debug("FACTORY: Docker detected, checking for internal URL")

        # Try to use PREDICT_URL_INTERNAL first if available
        internal_url = os.getenv("PREDICT_URL_INTERNAL")
        if internal_url:
            logger.info(f"FACTORY: Using internal Docker URL: {internal_url}")
            predict_url = internal_url
        else:
            # Fallback to host.docker.internal replacement
            logger.debug(
                "FACTORY: No internal URL found, using host.docker.internal for host services"
            )
            if "localhost" in predict_url:
                host_url = predict_url.replace("localhost", "host.docker.internal")
                logger.info(f"FACTORY: Using host.docker.internal URL: {host_url}")
                predict_url = host_url
            elif "127.0.0.1" in predict_url:
                host_url = predict_url.replace("127.0.0.1", "host.docker.internal")
                logger.info(f"FACTORY: Using host.docker.internal URL: {host_url}")
                predict_url = host_url
    predict_timeout = int(os.getenv("PREDICT_TIMEOUT", "5"))
    logger.info(
        f"FACTORY: Creating client with direct URL (bypass test): {predict_url}"
    )
    logger.debug(f"FACTORY: Timeout configuration: {predict_timeout}s")
    logger.debug("FACTORY: Instantiating PredictorClient")
    return PredictorClient(base_url=predict_url, timeout=predict_timeout)


if __name__ == "__main__":
    import sys
    import argparse
    import json

    parser = argparse.ArgumentParser(description="Predictor Client CLI")
    parser.add_argument(
        "--info", action="store_true", help="Get service information only"
    )
    parser.add_argument("--test", action="store_true", help="Run basic prediction test")
    parser.add_argument("--test-chat", action="store_true", help="Run chat task tests")
    parser.add_argument("--test-code", action="store_true", help="Run code task tests")
    parser.add_argument(
        "--test-custom", action="store_true", help="Run custom prediction tests"
    )

    args = parser.parse_args()

    client = create_predictor_client_from_env()

    try:
        # Always check health first
        if not client.health_check():
            print("Predictor service is not healthy")
            logger.error("PREDICTOR: service health check failed")
            sys.exit(1)

        print("Predictor service is healthy")
        logger.info("PREDICTOR: service health check passed")

        # Handle specific commands
        if args.info:
            try:
                info = client.get_service_info()
                if info:
                    print("Service Information:")
                    print(json.dumps(info, indent=2))
                else:
                    print("Could not retrieve service information")
                    sys.exit(1)
            except Exception as e:
                print(f"Error getting service info: {e}")
                logger.error(f"PREDICTOR: could not retrieve service info: {e}")
                sys.exit(1)

        elif args.test:
            try:
                predicted_time = client.predict(
                    task_type="chat",
                    prompt="Test prompt for timing prediction",
                    hardware="gpu_a100",
                    model="chat_instruct",
                    instances=1,
                    requests=1,
                )
                print(f"Prediction successful: {predicted_time:.3f} seconds")
                print(f"   Task: chat")
                print(f"   Hardware: gpu_a100")
                print(f"   Model: chat_instruct")
                logger.info(
                    f"PREDICTOR: test prediction successful: {predicted_time:.3f}s"
                )
            except Exception as e:
                print(f"Prediction failed: {e}")
                logger.error(f"PREDICTOR: test prediction failed: {e}")
                sys.exit(1)

        elif args.test_chat:
            test_cases = [
                {"hardware": "gpu_a100", "model": "chat_instruct"},
                {"hardware": "gpu_v100", "model": "chat_instruct"},
                {"hardware": "gpu_a100", "model": "chat_coder"},
            ]

            for i, case in enumerate(test_cases, 1):
                print(f"Test {i}: {case['hardware']} + {case['model']}")
                try:
                    predicted_time = client.predict(
                        task_type="chat",
                        prompt="Hello, how are you today? Can you help me with a question?",
                        hardware=case["hardware"],
                        model=case["model"],
                        instances=1,
                        requests=1,
                    )
                    print(f"  Prediction: {predicted_time:.3f} seconds")
                except Exception as e:
                    print(f"  Failed: {e}")
                print()

        elif args.test_code:
            test_cases = [
                {"hardware": "gpu_a100", "model": "chat_coder"},
                {"hardware": "gpu_v100", "model": "chat_coder"},
                {"hardware": "gpu_a100", "model": "chat_instruct"},
            ]

            code_prompt = """def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

# Optimize this function for better performance"""

            for i, case in enumerate(test_cases, 1):
                print(f"Test {i}: {case['hardware']} + {case['model']}")
                try:
                    predicted_time = client.predict(
                        task_type="code",
                        prompt=code_prompt,
                        hardware=case["hardware"],
                        model=case["model"],
                        instances=1,
                        requests=1,
                    )
                    print(f"  Prediction: {predicted_time:.3f} seconds")
                except Exception as e:
                    print(f"  Failed: {e}")
                print()

        elif args.test_custom:
            # Predefined test cases for Docker environment
            custom_tests = [
                {
                    "task_type": "chat",
                    "prompt": "What is machine learning and how does it work?",
                    "hardware": "gpu_a100",
                    "model": "chat_instruct",
                    "instances": 1,
                    "requests": 1,
                },
                {
                    "task_type": "code",
                    "prompt": "def sort_list(items):\n    # Implement efficient sorting\n    pass",
                    "hardware": "gpu_v100",
                    "model": "chat_coder",
                    "instances": 2,
                    "requests": 3,
                },
            ]

            for i, test in enumerate(custom_tests, 1):
                print(f"Custom Test {i}:")
                print(f"  Task: {test['task_type']}")
                print(f"  Hardware: {test['hardware']}")
                print(f"  Model: {test['model']}")
                print(f"  Instances: {test['instances']}")
                print(f"  Requests: {test['requests']}")

                try:
                    predicted_time = client.predict(**test)
                    print(f"  Prediction: {predicted_time:.3f} seconds")
                except Exception as e:
                    print(f"  Failed: {e}")
                print()

        else:
            # Default behavior - health check and basic info
            try:
                info = client.get_service_info()
                if info:
                    print(f"Service info: {info}")
                    logger.debug(f"PREDICTOR: service info retrieved: {info}")
            except Exception as e:
                logger.warning(f"PREDICTOR: could not retrieve service info: {e}")

            try:
                predicted_time = client.predict(
                    task_type="chat",
                    prompt="Test prompt for timing prediction",
                    hardware="gpu_a100",
                    model="chat_instruct",
                    instances=1,
                    requests=1,
                )
                print(f"Prediction successful: {predicted_time}s")
                logger.info(
                    f"PREDICTOR: test prediction successful: {predicted_time:.3f}s"
                )
            except Exception as e:
                print(f"Prediction failed: {e}")
                logger.error(f"PREDICTOR: test prediction failed: {e}")
                sys.exit(1)

    finally:
        client.close()
        logger.info("PREDICTOR: Client session closed")
