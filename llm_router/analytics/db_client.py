#!/usr/bin/env python3
"""
Клиент базы данных аналитики.

Клиент предоставляет высокоуровневые методы для взаимодействия с базой данных аналитики.
Использует собственный AnalyticsPrismaClient для всех операций с БД.
Критично важно что все операции записи никогда не прерывают основной поток LLM запросов.
"""
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, Any, Optional, List
import json
import sys

from .prisma_client import get_analytics_prisma_client, AnalyticsPrismaClientError
from .sql_builder import build_upsert, build_insert
from llm_router.logging_utils import get_logger


logger = get_logger(__name__)


class AnalyticsDBError(Exception):
    """Исключение при ошибках операций с аналитической базой данных."""

    pass


class AnalyticsDBClient:
    """Клиент базы данных аналитики с использованием AnalyticsPrismaClient.

    Предоставляет высокоуровневый интерфейс для сохранения данных аналитики LLM роутинга
    используя собственный Prisma клиент для полной изоляции от LiteLLM.
    Реализует in-memory кэширование ID для производительности.

    Клиент готов к использованию сразу после создания экземпляра.
    Подключение к БД происходит синхронно в конструкторе.

    Attributes:
        prisma_client: Экземпляр AnalyticsPrismaClient
        _model_id_cache: Кэш ID моделей для быстрого lookup
        _deployment_id_cache: Кэш ID deployment'ов для быстрого lookup
    """

    def __init__(
        self,
        database_url: Optional[str] = None,
        prisma_client=None,
        litellm_prisma_client=None,
    ):
        """Инициализирует клиент базы данных с AnalyticsPrismaClient.

        Клиент готов к использованию сразу после создания экземпляра.
        Подключение к БД происходит синхронно в конструкторе.

        Args:
            database_url: URL базы данных. Если None, будет использован из переменной окружения.
            prisma_client: Готовый Prisma клиент для использования. Если None, создается новый.
            litellm_prisma_client: LiteLLM PrismaClient instance. Used if prisma_client is None.

        Raises:
            AnalyticsDBError: Если подключение к БД невозможно
        """
        try:
            if prisma_client is not None:
                self.prisma_client = prisma_client
                logger.info(
                    "DB_CLIENT: Клиент аналитики создан с переданным Prisma клиентом"
                )
            else:
                self.prisma_client = get_analytics_prisma_client(
                    database_url=database_url,
                    litellm_prisma_client=litellm_prisma_client,
                )
                logger.info(
                    "DB_CLIENT: Клиент аналитики с AnalyticsPrismaClient создан"
                )
            self._model_id_cache: Dict[str, int] = {}
            self._deployment_id_cache: Dict[str, int] = {}
        except Exception as e:
            logger.error(f"DB_CLIENT: Ошибка создания клиента: {e}")
            raise AnalyticsDBError("Создание клиента базы данных провалилось") from e

    async def close(self) -> None:
        """Закрывает соединения с базой данных.

        Отключается от БД и очищает кэши.
        """
        try:
            await self.prisma_client.disconnect()
            self._model_id_cache.clear()
            self._deployment_id_cache.clear()
            logger.info("DB_CLIENT: Клиент аналитики закрыт")
        except Exception as e:
            logger.error(f"DB_CLIENT: Ошибка при закрытии клиента: {e}")

    async def get_model_id_by_name(self, model_name: str) -> Optional[int]:
        """Получает ID модели по имени с кэшированием для производительности.

        Использует in-memory кэш для минимизации запросов к БД. При первом обращении
        выполняет SELECT и кэширует результат. Последующие вызовы возвращают результат
        из кэша без обращения к БД.

        Args:
            model_name: Имя модели для поиска

        Returns:
            ID модели или None если модель не найдена
        """
        if model_name in self._model_id_cache:
            return self._model_id_cache[model_name]
        try:
            query = f"""SELECT id FROM "LiteLLM_Models" WHERE name = '{model_name}'"""
            result = await self.prisma_client.query_raw(query)
            model_id = result[0]["id"] if result else None
            if model_id:
                self._model_id_cache[model_name] = model_id
                logger.debug(
                    f"DB_CLIENT: Закэшировал model_id {model_id} для {model_name}"
                )
            return model_id
        except Exception as e:
            logger.error(f"DB_CLIENT: Ошибка получения model_id для {model_name}: {e}")
            return None

    async def get_deployment_id_by_unique_key(
        self,
        deployment_id: str,
        model_id: int,
        endpoint_url: str,
        provider: str,
        hardware_info: Optional[str],
        local_path: str,
    ) -> Optional[int]:
        """Получает ID deployment по уникальной комбинации полей.

        Deployment определяется уникальной комбинацией всех ключевых полей согласно
        unique constraint в схеме. Использует кэширование по составному ключу для
        производительности при множественных lookup'ах одного deployment.

        Args:
            deployment_id: LiteLLM ID deployment (НЕ уникален сам по себе)
            model_id: ID связанной модели
            endpoint_url: URL API endpoint
            provider: Провайдер API
            hardware_info: JSON строка с характеристиками железа (может быть None)
            local_path: Физический путь к файлу модели

        Returns:
            Внутренний ID deployment или None если не найден

        Raises:
            AnalyticsDBError: Если возникла ошибка базы данных
        """
        hardware_info_str = hardware_info or "null"
        cache_key = f"{deployment_id}:{model_id}:{endpoint_url}:{provider}:{hardware_info_str}:{local_path}"
        if cache_key in self._deployment_id_cache:
            return self._deployment_id_cache[cache_key]
        try:
            query = f"""
                SELECT id FROM "LiteLLM_Deployments" 
                WHERE deployment_id = '{deployment_id}' 
                AND model_id = {model_id}
                AND endpoint_url = '{endpoint_url}'
                AND provider = '{provider}'
                AND hardware_info = '{hardware_info_str}'
                AND local_path = '{local_path}'
                LIMIT 1
                """
            result = await self.prisma_client.query_raw(query)
            internal_id = result[0]["id"] if result else None
            if internal_id:
                self._deployment_id_cache[cache_key] = internal_id
                logger.debug(
                    f"DB_CLIENT: Закэшировал deployment_id {internal_id} для {deployment_id}"
                )
            return internal_id
        except Exception as e:
            logger.error(
                f"DB_CLIENT: Ошибка получения deployment_id для {deployment_id}: {e}"
            )
            return None

    async def upsert_model(self, model_data: Dict[str, Any]) -> Optional[int]:
        """Создает или обновляет модель в базе данных с обновлением кэша.

        Выполняет upsert операцию для записи модели. При конфликте по name обновляет
        существующую запись. Автоматически обновляет кэш ID после успешной операции.

        Args:
            model_data: Словарь данных модели содержащий name, provider, model_type,
                       context_length, capabilities, is_active

        Returns:
            ID созданной или обновленной модели, None при ошибке

        Raises:
            AnalyticsDBError: Если возникла ошибка базы данных или отсутствуют обязательные поля
        """
        required_fields = ["name"]
        for field in required_fields:
            if field not in model_data:
                raise AnalyticsDBError(f"Отсутствует обязательное поле: {field}")
        try:
            name = model_data["name"]
            query = build_upsert(
                table="LiteLLM_Models",
                conflict_fields=["name"],
                update_fields=[
                    "provider",
                    "model_type",
                    "context_length",
                    "capabilities",
                    "is_active",
                ],
                returning="id",
                name=name,
                provider=model_data.get("provider", "unknown"),
                model_type=model_data.get("model_type", "chat"),
                context_length=model_data.get("context_length"),
                capabilities=model_data.get("capabilities", "{}"),
                is_active=model_data.get("is_active", True),
                created_at=model_data.get("created_at", datetime.now(timezone.utc)),
            )
            result = await self.prisma_client.query_raw(query)
            model_id = result[0]["id"] if result else None
            if model_id:
                self._model_id_cache[name] = model_id
                logger.debug(f"DB_CLIENT: Upsert модели: {name} (ID: {model_id})")
                return model_id
            else:
                return await self.get_model_id_by_name(name)
        except Exception as e:
            logger.error(f"DB_CLIENT: Не удалось выполнить upsert модели: {e}")
            return None

    async def upsert_deployment(self, deployment_data: Dict[str, Any]) -> Optional[int]:
        """Создает или обновляет deployment с обязательной валидацией local_path.

        Выполняет upsert deployment с валидацией всех КРИТИЧНО важных полей.
        local_path обязателен для корректной работы ML предиктора. Автоматически
        получает model_id через lookup и обновляет кэш после успешной операции.

        Args:
            deployment_data: Словарь данных deployment содержащий deployment_id,
                           model_name, local_path (ОБЯЗАТЕЛЬНО), deployment_name,
                           endpoint_url, provider, region, hardware_info, max_concurrency

        Returns:
            ID созданного или обновленного deployment, None при ошибке

        Raises:
            AnalyticsDBError: Если возникла ошибка базы данных или отсутствуют обязательные поля
        """
        required_fields = ["deployment_id", "model_name", "local_path"]
        for field in required_fields:
            if field not in deployment_data or not deployment_data[field]:
                raise AnalyticsDBError(f"Отсутствует обязательное поле: {field}")
        try:
            model_name = deployment_data["model_name"]
            model_id = await self.get_model_id_by_name(model_name)
            if not model_id:
                logger.error(f"DB_CLIENT: Модель не найдена: {model_name}")
                return None
            deployment_id = deployment_data["deployment_id"]
            endpoint_url = deployment_data.get("endpoint_url", "")
            provider = deployment_data.get("provider", "unknown")
            hardware_info_str = deployment_data.get("hardware_info_str", "null")
            local_path = deployment_data["local_path"]
            logger.debug(f"DB_CLIENT: Full deployment_data: {deployment_data}")
            query = build_upsert(
                table="LiteLLM_Deployments",
                conflict_fields=[
                    "deployment_id",
                    "model_id",
                    "endpoint_url",
                    "provider",
                    "hardware_info",
                    "local_path",
                ],
                update_fields=[
                    "deployment_name",
                    "region",
                    "max_concurrency",
                    "is_active",
                    "updated_at",
                ],
                returning="id",
                deployment_id=deployment_id,
                model_id=model_id,
                deployment_name=deployment_data.get("deployment_name", deployment_id),
                endpoint_url=endpoint_url,
                provider=provider,
                local_path=local_path,
                region=deployment_data.get("region"),
                hardware_info=hardware_info_str,
                max_concurrency=deployment_data.get("max_concurrency"),
                is_active=deployment_data.get("is_active", True),
                created_at=deployment_data.get(
                    "created_at", datetime.now(timezone.utc)
                ),
                updated_at=datetime.now(timezone.utc),
            )
            result = await self.prisma_client.query_raw(query)
            deployment_internal_id = result[0]["id"] if result else None
            if deployment_internal_id:
                cache_key = f"{deployment_id}:{model_id}:{endpoint_url}:{provider}:{hardware_info_str}:{local_path}"
                self._deployment_id_cache[cache_key] = deployment_internal_id
                logger.debug(
                    f"DB_CLIENT: Upsert deployment: {deployment_id} (ID: {deployment_internal_id})"
                )
                return deployment_internal_id
            else:
                return await self.get_deployment_id_by_unique_key(
                    deployment_id,
                    model_id,
                    endpoint_url,
                    provider,
                    hardware_info_str,
                    local_path,
                )
        except Exception as e:
            logger.error(f"DB_CLIENT: Не удалось выполнить upsert deployment: {e}")
            return None

    async def insert_prompt_log(
        self, request_id: str, prompt_data: Dict[str, Any]
    ) -> bool:
        """Записывает лог параметров промпта с FK lookup для model_id.

        Создает запись в LiteLLM_PromptLogs используя model_id FK вместо прямого model_name.
        Выполняет валидацию наличия модели и логирует предупреждения при отсутствии FK связей.
        НЕ сохраняет prompt_text - он доступен в LiteLLM таблицах.

        Args:
            request_id: Уникальный идентификатор запроса для корреляции
            prompt_data: Словарь данных промпта содержащий model_name,
                        messages, input_data, temperature, max_tokens, top_p, stream,
                        task_type (из конфигурации), user_id

        Returns:
            True при успешной записи, False при ошибке

        Raises:
            AnalyticsDBError: Если возникла ошибка базы данных или отсутствуют обязательные поля
        """
        if not request_id:
            raise AnalyticsDBError("request_id является обязательным полем")
        if not prompt_data:
            logger.error(f"DB_CLIENT: prompt_data отсутствует для request {request_id}")
            return False
        try:
            model_name = prompt_data.get("model_name")
            if not model_name:
                logger.error(
                    f"DB_CLIENT: model_name отсутствует в prompt_data для request {request_id}"
                )
                logger.error(f"DB_CLIENT: prompt_data keys: {list(prompt_data.keys())}")
                logger.debug(f"DB_CLIENT: prompt_data: {prompt_data}")
                return False
            model_id = await self.get_model_id_by_name(model_name)
            if not model_id:
                logger.error(
                    f"DB_CLIENT: Модель не найдена в базе данных: '{model_name}' для request {request_id}"
                )
                try:
                    available_models_query = (
                        'SELECT name FROM "LiteLLM_Models" ORDER BY name'
                    )
                    available_models = await self.prisma_client.query_raw(
                        available_models_query
                    )
                    model_names = (
                        [m["name"] for m in available_models]
                        if available_models
                        else []
                    )
                    logger.error(f"DB_CLIENT: Доступные модели в БД: {model_names}")
                except Exception as list_error:
                    logger.error(
                        f"DB_CLIENT: Не удалось получить список моделей: {list_error}"
                    )
                return False
            query = build_insert(
                table="LiteLLM_PromptLogs",
                request_id=request_id,
                model_id=model_id,
                prompt_length=prompt_data.get("prompt_length", 0),
                input_data=prompt_data.get("input_data"),
                temperature=prompt_data.get("temperature"),
                max_tokens=prompt_data.get("max_tokens"),
                top_p=prompt_data.get("top_p"),
                stream=prompt_data.get("stream", False),
                task_type=prompt_data.get("task_type", "chat"),
                user_id=prompt_data.get("user_id"),
                created_at=datetime.now(timezone.utc),
            )
            await self.prisma_client.execute_raw(query)
            logger.debug(f"DB_CLIENT: Вставлен prompt лог: {request_id}")
            return True
        except Exception as e:
            logger.error(f"DB_CLIENT: Не удалось вставить prompt лог: {e}")
            return False

    async def insert_predictor_log(self, predictor_data: Dict[str, Any]) -> bool:
        """Записывает лог анализа предиктора с полным FK lookup по всем полям deployment.

        Выполняет поиск deployment по уникальной комбинации полей и валидирует
        все временные метрики анализа.

        Args:
            predictor_data: Словарь данных анализа предиктора содержащий predictor_analysis_id,
                           request_id, selected_deployment (с полями для поиска),
                           analysis_started_at, analysis_completed_at, deployments_analyzed_count,
                           selection_method, predicted_time, и другие метрики

        Returns:
            True при успешной записи, False при ошибке

        Raises:
            AnalyticsDBError: Если возникла ошибка базы данных
        """
        selected_deployment = predictor_data.get("selected_deployment", {})
        if not selected_deployment:
            logger.error(
                "DB_CLIENT: selected_deployment data missing in predictor_data"
            )
            return False
        try:
            model_id = await self.get_model_id_by_name(
                selected_deployment.get("model_name")
            )
            if not model_id:
                logger.warning(
                    f"DB_CLIENT: Модель не найдена для predictor лога: {selected_deployment.get('model_name')}"
                )
                return False
            deployment_internal_id = await self.get_deployment_id_by_unique_key(
                selected_deployment.get("deployment_id"),
                model_id,
                selected_deployment.get("endpoint_url"),
                selected_deployment.get("provider"),
                selected_deployment.get("hardware_info_str"),
                selected_deployment.get("local_path"),
            )
            if not deployment_internal_id:
                logger.warning(
                    f"DB_CLIENT: Deployment не найден для predictor лога: {selected_deployment.get('deployment_id')}"
                )
                return False
            query = build_insert(
                table="LiteLLM_PredictorLogs",
                predictor_analysis_id=predictor_data["predictor_analysis_id"],
                request_id=predictor_data["request_id"],
                selected_deployment_id=deployment_internal_id,
                analysis_started_at=predictor_data["analysis_started_at"],
                analysis_completed_at=predictor_data["analysis_completed_at"],
                analysis_duration_ms=predictor_data.get("analysis_duration_ms"),
                deployments_analyzed_count=predictor_data["deployments_analyzed_count"],
                predictor_service_url=predictor_data.get("predictor_service_url"),
                predictor_total_response_time=predictor_data.get(
                    "predictor_total_response_time"
                ),
                predictor_errors_count=predictor_data.get("predictor_errors_count", 0),
                selection_method=predictor_data["selection_method"],
                predicted_time=predictor_data.get("predicted_time"),
                load_balancing_weight=predictor_data.get("load_balancing_weight"),
                selected_hybrid_score=predictor_data.get("selected_hybrid_score"),
                selected_current_load=predictor_data.get("selected_current_load"),
                least_busy_available=predictor_data.get("least_busy_available", False),
                created_at=predictor_data.get("created_at", datetime.now(timezone.utc)),
            )
            await self.prisma_client.execute_raw(query)
            logger.debug(
                f"DB_CLIENT: Вставлен predictor лог: {predictor_data['predictor_analysis_id']}"
            )
            return True
        except Exception as e:
            logger.error(f"DB_CLIENT: Не удалось вставить predictor лог: {e}")
            return False

    async def insert_predictor_log_simple(
        self, request_id: str, predictor_data: Dict[str, Any]
    ) -> Optional[int]:
        """Записывает лог анализа предиктора с упрощенным FK lookup и возвращает ID записи.

        Упрощенная версия insert_predictor_log которая работает напрямую с LiteLLM deployment_id
        без необходимости передавать полную информацию о deployment. Возвращает ID созданной
        записи для использования в FK связях с endpoint analyses.

        Args:
            request_id: Уникальный идентификатор запроса для корреляции
            predictor_data: Словарь данных анализа предиктора содержащий
                           selected_deployment_id (LiteLLM ID), analysis_started_at,
                           analysis_completed_at, deployments_analyzed_count, selection_method,
                           predicted_time и другие метрики анализа

        Returns:
            ID созданной записи predictor log или None при ошибке

        Raises:
            AnalyticsDBError: Если возникла ошибка базы данных
        """
        if not request_id:
            logger.error("DB_CLIENT: request_id является обязательным полем")
            return None
        if not predictor_data:
            logger.error(
                f"DB_CLIENT: predictor_data отсутствует для request {request_id}"
            )
            return None
        try:
            litellm_deployment_id = predictor_data.get("selected_deployment_id")
            if not litellm_deployment_id:
                logger.error(
                    f"DB_CLIENT: selected_deployment_id отсутствует для request {request_id}"
                )
                return None
            deployment_internal_id = await self.get_deployment_id_by_litellm_id(
                litellm_deployment_id
            )
            if not deployment_internal_id:
                logger.error(
                    f"DB_CLIENT: Deployment не найден для LiteLLM ID: {litellm_deployment_id}"
                )
                return None
            prompt_check_query = f"""SELECT 1 FROM "LiteLLM_PromptLogs" WHERE request_id = '{request_id}' LIMIT 1"""
            prompt_exists = await self.prisma_client.query_raw(prompt_check_query)
            if not prompt_exists:
                logger.error(
                    f"DB_CLIENT: request_id {request_id} не найден в LiteLLM_PromptLogs - FK constraint нарушится"
                )
                return None
            query = build_insert(
                table="LiteLLM_PredictorLogs",
                returning="id",
                request_id=request_id,
                selected_deployment_id=deployment_internal_id,
                analysis_started_at=predictor_data.get(
                    "analysis_started_at", datetime.now(timezone.utc)
                ),
                analysis_completed_at=predictor_data.get(
                    "analysis_completed_at", datetime.now(timezone.utc)
                ),
                analysis_duration_ms=predictor_data.get("analysis_duration_ms"),
                deployments_analyzed_count=predictor_data.get(
                    "deployments_analyzed_count", 0
                ),
                predictor_service_url=predictor_data.get("predictor_service_url"),
                predictor_total_response_time=predictor_data.get(
                    "predictor_total_response_time"
                ),
                predictor_errors_count=predictor_data.get("predictor_errors_count", 0),
                selection_method=predictor_data.get("selection_method", "unknown"),
                predicted_time=predictor_data.get("predicted_time"),
                load_balancing_weight=predictor_data.get("load_balancing_weight"),
                selected_hybrid_score=predictor_data.get("selected_hybrid_score"),
                selected_current_load=predictor_data.get("selected_current_load"),
                least_busy_available=predictor_data.get("least_busy_available", False),
                created_at=datetime.now(timezone.utc),
            )
            result = await self.prisma_client.query_raw(query)
            predictor_log_id = result[0]["id"] if result else None
            if predictor_log_id:
                logger.debug(
                    f"DB_CLIENT: Вставлен predictor лог (simple) с ID {predictor_log_id}: {request_id}"
                )
            return predictor_log_id
        except Exception as e:
            logger.error(f"DB_CLIENT: Не удалось вставить predictor лог (simple): {e}")
            return None

    async def insert_response_log(self, response_data: Dict[str, Any]) -> bool:
        """Записывает лог результатов выполнения запроса с FK lookup для deployment_id.

        Создает запись в LiteLLM_ResponseLogs используя внутренний deployment_id FK и
        связывая с predictor анализом через request_id. Автоматически вычисляет метрики
        точности предсказаний если доступны данные предиктора.

        Args:
            response_data: Словарь данных ответа содержащий request_id, deployment (с полями
                          для поиска), api_call_started_at, completion_time, actual_response_time,
                          token метрики, success, error данные, и предсказанное время

        Returns:
            True при успешной записи, False при ошибке

        Raises:
            AnalyticsDBError: Если возникла ошибка базы данных
        """
        deployment_info = response_data.get("deployment", {})
        if not deployment_info:
            logger.error("DB_CLIENT: deployment data missing in response_data")
            return False
        try:
            model_id = await self.get_model_id_by_name(
                deployment_info.get("model_name")
            )
            if not model_id:
                logger.warning(
                    f"DB_CLIENT: Модель не найдена для response лога: {deployment_info.get('model_name')}"
                )
                return False
            deployment_internal_id = await self.get_deployment_id_by_unique_key(
                deployment_info.get("deployment_id"),
                model_id,
                deployment_info.get("endpoint_url"),
                deployment_info.get("provider"),
                deployment_info.get("hardware_info_str"),
                deployment_info.get("local_path"),
            )
            if not deployment_internal_id:
                logger.warning(
                    f"DB_CLIENT: Deployment не найден для response лога: {deployment_info.get('deployment_id')}"
                )
                return False
            predicted_time = response_data.get("predicted_time")
            actual_time = response_data.get("actual_response_time")
            prediction_accuracy = None
            prediction_error = None
            if (
                predicted_time is not None
                and actual_time is not None
                and (actual_time > 0)
            ):
                prediction_error = actual_time - predicted_time
                prediction_accuracy = abs(prediction_error) / actual_time
            query = build_insert(
                table="LiteLLM_ResponseLogs",
                request_id=response_data["request_id"],
                deployment_id=deployment_internal_id,
                api_call_started_at=response_data["api_call_started_at"],
                completion_time=response_data["completion_time"],
                actual_response_time=response_data["actual_response_time"],
                time_to_first_token=response_data.get("time_to_first_token"),
                is_stream_response=response_data.get("is_stream_response", False),
                stream_jitter=response_data.get("stream_jitter"),
                input_tokens=response_data.get("input_tokens"),
                output_tokens=response_data.get("output_tokens"),
                total_tokens=response_data.get("total_tokens"),
                tokens_per_second=response_data.get("tokens_per_second"),
                response_finish_reason=response_data.get("response_finish_reason"),
                predicted_time=predicted_time,
                success=response_data.get("success", True),
                error_type=response_data.get("error_type"),
                error_message=response_data.get("error_message"),
                http_status_code=response_data.get("http_status_code"),
                prediction_accuracy=prediction_accuracy,
                prediction_error=prediction_error,
                created_at=response_data.get("created_at", datetime.now(timezone.utc)),
            )
            await self.prisma_client.execute_raw(query)
            logger.debug(f"DB_CLIENT: Вставлен response лог: {request_id}")
            return True
        except Exception as e:
            logger.error(f"DB_CLIENT: Не удалось вставить response лог: {e}")
            return False

    async def insert_response_log_simple(
        self, request_id: str, response_data: Dict[str, Any]
    ) -> bool:
        """Записывает лог результатов выполнения запроса с упрощенным FK lookup по LiteLLM deployment_id.

        Упрощенная версия insert_response_log которая работает напрямую с LiteLLM deployment_id
        без необходимости передавать полную информацию о deployment.

        Args:
            request_id: Уникальный идентификатор запроса для корреляции
            response_data: Словарь данных ответа содержащий deployment_id (LiteLLM ID),
                          api_call_started_at, completion_time, actual_response_time, token метрики,
                          success, error данные, prediction_accuracy, prediction_error, predicted_time

        Returns:
            True при успешной записи, False при ошибке

        Raises:
            AnalyticsDBError: Если возникла ошибка базы данных
        """
        if not request_id:
            logger.error("DB_CLIENT: request_id является обязательным полем")
            return False
        if not response_data:
            logger.error(
                f"DB_CLIENT: response_data отсутствует для request {request_id}"
            )
            return False
        try:
            litellm_deployment_id = response_data.get("deployment_id")
            if not litellm_deployment_id:
                logger.error(
                    f"DB_CLIENT: deployment_id отсутствует для request {request_id}"
                )
                return False
            deployment_internal_id = await self.get_deployment_id_by_litellm_id(
                litellm_deployment_id
            )
            if not deployment_internal_id:
                logger.error(
                    f"DB_CLIENT: Deployment не найден для LiteLLM ID: {litellm_deployment_id}"
                )
                check_query = f'SELECT deployment_id FROM "LiteLLM_Deployments" LIMIT 5'
                existing_deployments = await self.prisma_client.query_raw(check_query)
                logger.debug(
                    f"DB_CLIENT: Существующие deployment_id в БД: {[d['deployment_id'] for d in existing_deployments]}"
                )
                return False
            prompt_check_query = f"""SELECT 1 FROM "LiteLLM_PromptLogs" WHERE request_id = '{request_id}' LIMIT 1"""
            prompt_exists = await self.prisma_client.query_raw(prompt_check_query)
            if not prompt_exists:
                logger.error(
                    f"DB_CLIENT: request_id {request_id} не найден в LiteLLM_PromptLogs - FK constraint нарушится"
                )
                return False
            query = build_insert(
                table="LiteLLM_ResponseLogs",
                request_id=request_id,
                deployment_id=deployment_internal_id,
                api_call_started_at=response_data.get(
                    "api_call_started_at", datetime.now(timezone.utc)
                ),
                completion_time=response_data.get(
                    "completion_time", datetime.now(timezone.utc)
                ),
                actual_response_time=response_data.get("actual_response_time", 0.0),
                time_to_first_token=response_data.get("time_to_first_token"),
                is_stream_response=response_data.get("is_stream_response", False),
                stream_jitter=response_data.get("stream_jitter"),
                input_tokens=response_data.get("input_tokens"),
                output_tokens=response_data.get("output_tokens"),
                total_tokens=response_data.get("total_tokens"),
                tokens_per_second=response_data.get("tokens_per_second"),
                response_finish_reason=response_data.get("response_finish_reason"),
                predicted_time=response_data.get("predicted_time"),
                success=response_data.get("success", True),
                error_type=response_data.get("error_type"),
                error_message=response_data.get("error_message"),
                http_status_code=response_data.get("http_status_code"),
                prediction_accuracy=response_data.get("prediction_accuracy"),
                prediction_error=response_data.get("prediction_error"),
                created_at=datetime.now(timezone.utc),
            )
            await self.prisma_client.execute_raw(query)
            logger.debug(f"DB_CLIENT: Вставлен response лог (simple): {request_id}")
            return True
        except Exception as e:
            logger.error(f"DB_CLIENT: Не удалось вставить response лог (simple): {e}")
            return False

    async def insert_endpoint_analysis_simple(
        self, predictor_log_id: int, endpoint_data: Dict[str, Any]
    ) -> bool:
        """Записывает детальный анализ endpoint с упрощенным FK lookup по LiteLLM deployment_id.

        Упрощенная версия insert_endpoint_analysis которая работает напрямую с LiteLLM deployment_id
        без необходимости передавать полную информацию о deployment.

        Args:
            predictor_log_id: ID записи predictor log для FK связи
            endpoint_data: Словарь данных анализа endpoint содержащий
                          deployment_id (LiteLLM ID), predictor_input_data,
                          predictor_response_time, predictor_error, и другие метрики анализа

        Returns:
            True при успешной записи, False при ошибке
        """
        if not predictor_log_id:
            logger.error("DB_CLIENT: predictor_log_id является обязательным полем")
            return False
        if not endpoint_data:
            logger.error(
                f"DB_CLIENT: endpoint_data отсутствует для predictor_log_id {predictor_log_id}"
            )
            return False
        try:
            litellm_deployment_id = endpoint_data.get("deployment_id")
            if not litellm_deployment_id:
                logger.error(
                    f"DB_CLIENT: deployment_id отсутствует для predictor_log_id {predictor_log_id}"
                )
                return False
            predictor_check_query = f'SELECT 1 FROM "LiteLLM_PredictorLogs" WHERE id = {predictor_log_id} LIMIT 1'
            predictor_exists = await self.prisma_client.query_raw(predictor_check_query)
            if not predictor_exists:
                logger.error(
                    f"DB_CLIENT: predictor_log_id {predictor_log_id} не найден в LiteLLM_PredictorLogs - FK constraint нарушится"
                )
                return False
            deployment_id = await self.get_deployment_id_by_litellm_id(
                litellm_deployment_id
            )
            if not deployment_id:
                logger.error(
                    f"DB_CLIENT: Deployment не найден для LiteLLM ID: {litellm_deployment_id}"
                )
                return False
            query = build_insert(
                table="LiteLLM_EndpointAnalysis",
                predictor_log_id=predictor_log_id,
                deployment_id=deployment_id,
                predictor_input_data=endpoint_data.get("predictor_input_data", {}),
                predictor_response_time=endpoint_data.get("predictor_response_time"),
                predictor_error=endpoint_data.get("predictor_error"),
                predictor_duration_ms=endpoint_data.get("predictor_duration_ms"),
                current_load=endpoint_data.get("current_load"),
                load_source=endpoint_data.get("load_source"),
                hybrid_score=endpoint_data.get("hybrid_score"),
                final_rank=endpoint_data.get("final_rank"),
                was_selected=endpoint_data.get("was_selected", False),
                created_at=datetime.now(timezone.utc),
            )
            await self.prisma_client.execute_raw(query)
            logger.debug(
                f"DB_CLIENT: Endpoint analysis inserted for deployment: {litellm_deployment_id}"
            )
            return True
        except Exception as e:
            logger.error(f"DB_CLIENT: Error inserting endpoint analysis: {e}")
            return False

    async def insert_endpoint_analysis(self, endpoint_data: Dict[str, Any]) -> bool:
        """Записывает детальный анализ конкретного endpoint с FK lookups.

        Создает запись в LiteLLM_EndpointAnalysis для каждого анализируемого endpoint
        в рамках predictor анализа. Связывает с predictor анализом и deployment через FK.

        Args:
            endpoint_data: Словарь данных анализа endpoint содержащий endpoint_analysis_id,
                          predictor_analysis_id, deployment (с полями для поиска),
                          predictor_input_data, predictor_response_time, predictor_error,
                          current_load, load_source, hybrid_score, final_rank, was_selected

        Returns:
            True при успешной записи, False при ошибке
        """
        deployment_info = endpoint_data.get("deployment", {})
        if not deployment_info:
            logger.error("DB_CLIENT: deployment data missing in endpoint_data")
            return False
        try:
            model_id = await self.get_model_id_by_name(
                deployment_info.get("model_name")
            )
            if not model_id:
                logger.warning(
                    f"DB_CLIENT: Модель не найдена для endpoint analysis: {deployment_info.get('model_name')}"
                )
                return False
            deployment_internal_id = await self.get_deployment_id_by_unique_key(
                deployment_info.get("deployment_id"),
                model_id,
                deployment_info.get("endpoint_url"),
                deployment_info.get("provider"),
                deployment_info.get("hardware_info_str"),
                deployment_info.get("local_path"),
            )
            if not deployment_internal_id:
                logger.warning(
                    f"DB_CLIENT: Deployment не найден для endpoint analysis: {deployment_info.get('deployment_id')}"
                )
                return False
            query = build_insert(
                table="LiteLLM_EndpointAnalysis",
                endpoint_analysis_id=endpoint_data["endpoint_analysis_id"],
                predictor_analysis_id=endpoint_data["predictor_analysis_id"],
                deployment_id=deployment_internal_id,
                predictor_input_data=endpoint_data.get("predictor_input_data"),
                predictor_response_time=endpoint_data.get("predictor_response_time"),
                predictor_error=endpoint_data.get("predictor_error"),
                predictor_duration_ms=endpoint_data.get("predictor_duration_ms"),
                current_load=endpoint_data.get("current_load"),
                load_source=endpoint_data.get("load_source"),
                hybrid_score=endpoint_data.get("hybrid_score"),
                final_rank=endpoint_data.get("final_rank"),
                was_selected=endpoint_data.get("was_selected", False),
                created_at=endpoint_data.get("created_at", datetime.now(timezone.utc)),
            )
            await self.prisma_client.execute_raw(query)
            logger.debug(
                f"DB_CLIENT: Вставлен endpoint analysis: {endpoint_analysis_id}"
            )
            return True
        except Exception as e:
            logger.error(f"DB_CLIENT: Не удалось вставить endpoint analysis: {e}")
            return False

    async def execute_query(
        self, query: str, params: Optional[List[Any]] = None
    ) -> List[Dict[str, Any]]:
        """Выполняет произвольный SQL запрос и возвращает результаты.

        Args:
            query: Строка SQL запроса
            params: Параметры запроса (не используются, для совместимости)

        Returns:
            Список словарей результатов

        Raises:
            AnalyticsDBError: При ошибке выполнения запроса
        """
        try:
            return await self.prisma_client.query_raw(query)
        except Exception as e:
            logger.error(f"DB_CLIENT: Произвольный запрос провалился: {e}")
            raise AnalyticsDBError(f"Произвольный запрос провалился: {e}")

    async def get_prompt_text_from_litellm(self, request_id: str) -> Optional[str]:
        """Получает полный текст промпта из LiteLLM_SpendLogs по request_id.

        Извлекает данные из поля messages в LiteLLM_SpendLogs и преобразует в читаемый текст.
        Для chat completion объединяет все сообщения в один текст.

        Args:
            request_id: Уникальный ID запроса для поиска в LiteLLM_SpendLogs

        Returns:
            Полный текст промпта или None если не найден

        Raises:
            AnalyticsDBError: Если возникла ошибка базы данных
        """
        try:
            query = f"""
                SELECT messages, call_type
                FROM "LiteLLM_SpendLogs" 
                WHERE request_id = '{request_id}'
                """
            result = await self.prisma_client.query_raw(query)
            if not result:
                logger.debug(
                    f"DB_CLIENT: Запрос {request_id} не найден в LiteLLM_SpendLogs"
                )
                return None
            messages = result[0].get("messages")
            call_type = result[0].get("call_type", "chat")
            if not messages:
                logger.debug(
                    f"DB_CLIENT: Поле messages пустое для запроса {request_id}"
                )
                return None
            if isinstance(messages, str):
                import json

                try:
                    messages = json.loads(messages)
                except json.JSONDecodeError:
                    logger.warning(
                        f"DB_CLIENT: Не удалось распарсить messages для {request_id}"
                    )
                    return None
            if call_type == "chat" and isinstance(messages, list):
                prompt_parts = []
                for msg in messages:
                    if isinstance(msg, dict) and "content" in msg:
                        role = msg.get("role", "user")
                        content = msg.get("content", "")
                        prompt_parts.append(f"{role}: {content}")
                prompt_text = "\n".join(prompt_parts)
            elif call_type == "embedding" and isinstance(messages, dict):
                prompt_text = messages.get("input", "")
            elif isinstance(messages, str):
                prompt_text = messages
            else:
                prompt_text = str(messages)
            logger.debug(
                f"DB_CLIENT: Извлечен prompt_text длиной {len(prompt_text)} для {request_id}"
            )
            return prompt_text
        except Exception as e:
            logger.error(
                f"DB_CLIENT: Ошибка получения prompt_text для {request_id}: {e}"
            )
            return None

    async def get_full_request_context(
        self, request_id: str
    ) -> Optional[Dict[str, Any]]:
        """Получает полный контекст запроса из обеих таблиц - Analytics и LiteLLM.

        Объединяет данные из LiteLLM_PromptLogs (Analytics) и LiteLLM_SpendLogs (LiteLLM)
        для получения полной картины запроса включая prompt_text.

        Args:
            request_id: Уникальный ID запроса

        Returns:
            Словарь с полным контекстом запроса или None если не найден

        Raises:
            AnalyticsDBError: Если возникла ошибка базы данных
        """
        try:
            query = f"""
                SELECT 
                    -- Analytics данные
                    pl.prompt_length,
                    pl.task_type,
                    pl.temperature,
                    pl.max_tokens,
                    pl.request_id,
                    
                    -- PredictorLogs данные  
                    pred.selection_method,
                    pred.predicted_time,
                    pred.least_busy_available,
                    pred.selected_deployment_id,
                    pred.selected_hybrid_score,
                    pred.analysis_started_at,
                    pred.analysis_completed_at,
                    pred.predictor_errors_count,
                    
                    -- ResponseLogs данные
                    resp.actual_response_time,
                    resp.prediction_accuracy,
                    resp.success,
                    resp.error_type,
                    resp.deployment_id as response_deployment_id,
                    resp.api_call_started_at,
                    resp.api_call_completed_at,
                    
                    -- EndpointAnalysis данные
                    ea.deployment_id as endpoint_deployment_id,
                    ea.predictor_response_time,
                    ea.load_score,
                    ea.final_rank,
                    ea.was_selected,
                    ea.predictor_error,
                    
                    -- Deployments данные  
                    d.deployment_id as deployment_name,
                    d.endpoint_url,
                    d.provider,
                    d.hardware_info,
                    
                    -- Models данные  
                    m.name as model_name,
                    m.provider as model_provider
                FROM "LiteLLM_PromptLogs" pl
                JOIN "LiteLLM_PredictorLogs" pred ON pl.request_id = pred.request_id
                LEFT JOIN "LiteLLM_ResponseLogs" resp ON pl.request_id = resp.request_id
                LEFT JOIN "LiteLLM_EndpointAnalysis" ea ON pred.predictor_analysis_id = ea.predictor_analysis_id
                LEFT JOIN "LiteLLM_Deployments" d ON ea.deployment_id = d.deployment_id
                LEFT JOIN "LiteLLM_Models" m ON pl.model_name = m.name
                WHERE pl.request_id = '{request_id}'
                ORDER BY ea.final_rank
                """
            result = await self.prisma_client.query_raw(query)
            if not result:
                logger.debug(
                    f"DB_CLIENT: Запрос {request_id} не найден в объединенных таблицах"
                )
                return None
            context = result[0]
            prompt_text = await self.get_prompt_text_from_litellm(request_id)
            if prompt_text:
                context["prompt_text"] = prompt_text
                context["prompt_text_length"] = len(prompt_text)
            logger.debug(f"DB_CLIENT: Получен полный контекст для запроса {request_id}")
            return context
        except Exception as e:
            logger.error(
                f"DB_CLIENT: Ошибка получения полного контекста для {request_id}: {e}"
            )
            return None

    async def get_deployment_id_by_litellm_id(
        self, litellm_deployment_id: str
    ) -> Optional[int]:
        """Получает внутренний ID deployment по LiteLLM deployment_id с кэшированием.

        Упрощенный метод для быстрого lookup deployment ID по LiteLLM идентификатору.
        Предполагает что LiteLLM deployment_id уникален в рамках текущей конфигурации.
        Кэширует результаты для производительности.

        Args:
            litellm_deployment_id: LiteLLM идентификатор deployment (например, chat-instruct1)

        Returns:
            Внутренний ID deployment или None если не найден

        Raises:
            AnalyticsDBError: Если возникла ошибка базы данных
        """
        cache_key = f"litellm:{litellm_deployment_id}"
        if cache_key in self._deployment_id_cache:
            return self._deployment_id_cache[cache_key]
        try:
            query = f"""
                SELECT id FROM "LiteLLM_Deployments" 
                WHERE deployment_id = '{litellm_deployment_id}' 
                ORDER BY updated_at DESC 
                LIMIT 1
                """
            result = await self.prisma_client.query_raw(query)
            deployment_id = result[0]["id"] if result else None
            if deployment_id:
                self._deployment_id_cache[cache_key] = deployment_id
                logger.debug(
                    f"DB_CLIENT: Закэшировал deployment_id {deployment_id} для LiteLLM ID {litellm_deployment_id}"
                )
            return deployment_id
        except Exception as e:
            logger.error(
                f"DB_CLIENT: Ошибка получения deployment_id для LiteLLM ID {litellm_deployment_id}: {e}"
            )
            return None


_analytics_db_client: Optional[AnalyticsDBClient] = None


def get_analytics_db_client(database_url: Optional[str] = None) -> AnalyticsDBClient:
    """Получает глобальный экземпляр клиента аналитической базы данных.

    Args:
        database_url: URL базы данных. Если None, будет использован из переменной окружения.

    Returns:
        Общий экземпляр AnalyticsDBClient
    """
    global _analytics_db_client
    if _analytics_db_client is None:
        _analytics_db_client = AnalyticsDBClient(database_url)
    return _analytics_db_client


async def initialize_analytics_db(database_url: Optional[str] = None) -> None:
    """Инициализирует глобальный клиент аналитической базы данных.

    Примечание: Клиент готов к использованию сразу после создания.
    Эта функция оставлена для совместимости с существующим кодом.

    Args:
        database_url: URL базы данных. Если None, будет использован из переменной окружения.
    """
    client = get_analytics_db_client(database_url)
    logger.info(
        "DB_CLIENT: Глобальный клиент аналитической базы данных готов к использованию"
    )


async def cleanup_analytics_db() -> None:
    """Очищает глобальный клиент аналитической базы данных."""
    global _analytics_db_client
    if _analytics_db_client:
        await _analytics_db_client.close()
        _analytics_db_client = None
        logger.info("DB_CLIENT: Глобальный клиент аналитической базы данных очищен")
