# Руководство по началу работы

Добро пожаловать в LLM Router - интеллектуальную систему маршрутизации для LiteLLM с машинным обучением и гибридной балансировкой нагрузки.

## Обзор проекта

### Что такое LLM Router?

LLM Router - это интеллектуальная система маршрутизации, которая расширяет возможности LiteLLM, предоставляя:

- **Машинное обучение для предсказания времени ответа** - анализ каждого запроса и предсказание производительности
- **Гибридную балансировку нагрузки** - комбинирование ML-предсказаний с данными о реальной нагрузке
- **Интеграцию с GPUStack** - использование данных о физическом оборудовании для улучшения точности
- **Детальную аналитику** - полное логирование и анализ решений маршрутизации

### Решаемые проблемы

**Проблема**: Стандартный LiteLLM Router распределяет запросы случайно или используя простые алгоритмы без учета:
- Реальной производительности каждой конечной точки для конкретных типов запросов
- Сложности и характеристик запроса
- Спецификаций оборудования (GPU vs CPU, память, архитектура)

**Решение**: Интеллектуальный роутер с гибридной балансировкой, который:
- Анализирует каждый запрос (длина, сложность, тип задачи)
- Предсказывает время ответа для каждой конечной точки через HTTP API
- Комбинирует ML-предсказания с данными о реальной нагрузке
- Выбирает оптимальную конечную точку используя гибридный алгоритм оценки

## Быстрая настройка (5 минут)

### Предварительные требования

- Docker и Docker Compose
- Python 3.13+ (для локальной разработки)
- Доступ к GPUStack API (опционально)
- ML Predictor сервис (опционально)

### Шаг 1: Клонирование и настройка

```bash
# Клонирование репозитория
git clone <repository-url>
cd llm_router

# Копирование конфигурации
cp .env.example .env
```

### Шаг 2: Конфигурация окружения

Отредактируйте файл `.env`:

```bash
# Основные настройки LiteLLM
LITELLM_MASTER_KEY=sk-1234
LITELLM_SALT_KEY=sk-1234
LITELLM_LOG=DEBUG

# ML Predictor сервис (опционально)
PREDICT_URL=http://localhost:8008
PREDICT_TIMEOUT=5

# GPUStack интеграция (опционально)
GPUSTACK_URL=http://localhost:80
GPUSTACK_KEY=your-gpustack-api-key
GPUSTACK_TOKEN=your-gpustack-token

# База данных
DATABASE_URL=********************************************/litellm

# Аналитика
ANALYTICS_ENABLED=true
USE_ANALYTICS_MIGRATE=true
```

### Шаг 3: Запуск системы

```bash
# Сборка и запуск всех сервисов
./run.sh docker up --build

# Проверка статуса
./run.sh docker status

# Просмотр логов
./run.sh docker logs litellm
```

### Шаг 4: Проверка работоспособности

```bash
# Тест health endpoints
./run.sh test endpoint

# Тест конкретной модели
./run.sh test model chat_instruct

# Тест всех моделей
./run.sh test models
```

## Основные концепции

### Архитектура системы

LLM Router состоит из нескольких ключевых компонентов:

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   LiteLLM       │    │   ML Predictor   │    │   GPUStack      │
│   Proxy         │◄──►│   Service        │    │   Integration   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────────────────────────────────────────────────────┐
│                    LLM Router Core                              │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ Custom Router   │  │ Hybrid Scoring  │  │ Analytics       │ │
│  │ Strategy        │  │ Engine          │  │ System          │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
         │
         ▼
┌─────────────────┐
│   PostgreSQL    │
│   Analytics DB  │
└─────────────────┘
```

### Гибридная балансировка нагрузки

Система использует гибридный алгоритм оценки:

```
hybrid_score = (1 - weight) × ml_prediction + weight × load_penalty
```

Где:
- **ml_prediction** - предсказание времени ответа от ML сервиса (70%)
- **load_penalty** - штраф за текущую нагрузку на deployment (30%)
- **weight** - настраиваемый вес балансировки (по умолчанию 0.3)

### Типы маршрутизации

1. **ML-based routing** - использование предсказаний машинного обучения
2. **Least-busy routing** - выбор наименее загруженного deployment
3. **Hybrid routing** - комбинирование ML и load-based подходов
4. **Fallback routing** - резервные механизмы при недоступности сервисов

### Интеграции

#### ML Predictor
- HTTP API для предсказания времени ответа
- Анализ характеристик запроса (длина, сложность, тип)
- Возврат предсказаний для всех доступных deployments

#### GPUStack
- Данные о физическом оборудовании (GPU типы, память, архитектура)
- Информация о моделях и их размещении
- Кэширование данных с 96%+ эффективностью

#### Analytics System
- Асинхронное логирование всех решений маршрутизации
- PostgreSQL для хранения аналитических данных
- Детальные метрики производительности и точности

## Первые шаги

### 1. Изучение команд

```bash
# Просмотр всех доступных команд
./run.sh help

# Команды по категориям
./run.sh docker help     # Управление контейнерами
./run.sh config help     # Конфигурация
./run.sh test help       # Тестирование
./run.sh debug help      # Отладка
```

### 2. Проверка конфигурации

```bash
# Валидация конфигурации
./run.sh config validate

# Проверка статуса конфигурации
./run.sh config check

# Тест маршрутизации
./run.sh config test-routing
```

### 3. Мониторинг системы

```bash
# Статус сервисов
./run.sh docker status

# Мониторинг маршрутизации
./run.sh monitor routing

# Проверка аналитики
./run.sh analytics health check
```

### 4. Тестирование функциональности

```bash
# Базовые тесты
./run.sh test endpoint

# Тесты интеграций
./run.sh config test-predictor
./run.sh config test-gpustack

# Комплексные тесты
./run.sh test models
```

## Следующие шаги

После успешной настройки рекомендуем:

1. **Изучить архитектуру** - [Архитектура системы](ARCHITECTURE.md)
2. **Настроить аналитику** - [Руководство по аналитике](ANALYTICS_GUIDE.md)
3. **Изучить команды** - [Руководство по эксплуатации](OPERATIONS_GUIDE.md)
4. **Настроить тестирование** - [Руководство по тестированию](TESTING_GUIDE.md)

## Полезные ссылки

- [Архитектура системы](ARCHITECTURE.md) - детальное описание компонентов
- [Руководство по эксплуатации](OPERATIONS_GUIDE.md) - команды и конфигурация
- [Справочник API](API_REFERENCE.md) - документация API
- [Устранение неполадок](TROUBLESHOOTING.md) - решение проблем

## Поддержка

При возникновении проблем:
1. Проверьте [руководство по устранению неполадок](TROUBLESHOOTING.md)
2. Используйте команды отладки: `./run.sh debug status`
3. Проверьте логи: `./run.sh docker logs litellm`

---

*Следующий раздел: [Архитектура системы](ARCHITECTURE.md)*