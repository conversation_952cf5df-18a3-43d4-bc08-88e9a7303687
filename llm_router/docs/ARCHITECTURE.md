# Архитектура системы

Данный документ описывает полную архитектуру LLM Router - интеллектуальной системы маршрутизации с машинным обучением и гибридной балансировкой нагрузки.

## Обзор архитектуры

### Высокоуровневая архитектура

```mermaid
graph TB
    subgraph "Клиентские приложения"
        Client[Клиент]
        API[API Запросы]
    end
    
    subgraph "LLM Router System"
        LiteLLM[LiteLLM Proxy]
        Router[Custom Router Strategy]
        Analytics[Analytics System]
    end
    
    subgraph "Внешние сервисы"
        Predictor[ML Predictor Service]
        GPUStack[GPUStack API]
        Models[LLM Models/Deployments]
    end
    
    subgraph "Хранилище данных"
        PostgreSQL[(PostgreSQL)]
        Redis[(Redis Cache)]
    end
    
    Client --> API
    API --> LiteLLM
    LiteLLM --> Router
    Router --> Predictor
    Router --> GPUStack
    Router --> Models
    Analytics --> PostgreSQL
    Router --> Analytics
    GPUStack --> Redis
    
    style LiteLLM fill:#e1f5fe
    style Router fill:#fff3e0
    style Analytics fill:#e8f5e8
    style Predictor fill:#fce4ec
    style GPUStack fill:#f3e5f5
```

### Основные компоненты

1. **LiteLLM Proxy** - точка входа для всех запросов
2. **Custom Router Strategy** - ядро интеллектуальной маршрутизации
3. **ML Predictor Service** - сервис предсказания времени ответа
4. **GPUStack Integration** - интеграция с данными о физическом оборудовании
5. **Analytics System** - система сбора и анализа данных
6. **Cache Layer** - слой кэширования для оптимизации производительности

## Архитектура кодовой базы

### Структура проекта

```
llm_router/
├── __init__.py                     # Инициализация пакета
├── start_litellm_with_router.py    # Точка входа
├── litellm_patch.py               # Патч для LiteLLM
├── logging_utils.py               # Утилиты логирования
│
├── router/                        # Ядро системы маршрутизации
│   ├── custom_router.py           # Основная стратегия маршрутизации
│   ├── router_factory.py          # Фабрика для создания роутеров
│   ├── deployment_selector_mixin.py # Логика выбора deployment
│   ├── router_predictor_mixin.py   # Интеграция с ML Predictor
│   ├── gpustack_router_mixin.py    # Интеграция с GPUStack
│   └── least_busy_integration.py   # Отслеживание нагрузки
│
├── router_litellm/               # Интеграция с LiteLLM
│   ├── litellm_manager.py        # Управление LiteLLM
│   ├── litellm_config_api.py     # API конфигурации
│   └── litellm_auth.py           # Аутентификация
│
├── router_gpustack/              # Интеграция с GPUStack
│   ├── gpustack_integration.py   # Основной клиент
│   ├── gpustack_cache_manager.py # Управление кэшем
│   └── gpustack_auth.py          # Аутентификация
│
├── predictor/                    # ML Predictor интеграция
│   └── predictor_client.py       # HTTP клиент
│
├── analytics/                    # Система аналитики
│   ├── router_analytics_logger.py # Основной логгер
│   ├── data_builder.py           # Построение данных
│   ├── db_client.py              # Клиент БД
│   └── sql_builder.py            # Построение SQL
│
├── config/                       # Управление конфигурацией
│   ├── config_manager.py         # Основной менеджер
│   ├── service_config.py         # Конфигурация сервисов
│   └── host_utils.py             # Утилиты хоста
│
└── common/                       # Общие утилиты
    ├── base_auth_checker.py      # Базовая аутентификация
    ├── base_http_client.py       # Базовый HTTP клиент
    └── example_usage.py          # Примеры использования
```

### Паттерны архитектуры

#### Mixin Architecture
Функциональность роутера разделена на специализированные миксины:

```python
class LLMTimePredictorRoutingStrategy(
    RouterInitializationMixin,
    RouterPredictorMixin,
    GPUStackRouterMixin,
    DeploymentSelectorMixin,
    RouterUtilsMixin,
    CustomRoutingStrategyBase
):
    """Основная стратегия маршрутизации с ML предсказаниями"""
```

#### Factory Pattern
Создание стратегий маршрутизации через фабрику:

```python
def create_routing_strategy(config: Dict) -> CustomRoutingStrategyBase:
    """Создание стратегии маршрутизации на основе конфигурации"""
    return LLMTimePredictorRoutingStrategy(config)
```

#### Integration Pattern
Чистое разделение между внешними сервисами:
- Асинхронные HTTP клиенты для внешних сервисов
- Механизмы fallback при недоступности сервисов
- Изоляция ошибок интеграции

## Поток данных

### Процесс маршрутизации запроса

```mermaid
sequenceDiagram
    participant C as Клиент
    participant L as LiteLLM Proxy
    participant R as Router Strategy
    participant P as ML Predictor
    participant G as GPUStack
    participant M as LLM Model
    participant A as Analytics
    
    C->>L: HTTP запрос
    L->>R: Выбор deployment
    
    Note over R: Анализ запроса
    R->>P: Запрос предсказаний¹
    R->>G: Получение данных GPU²
    
    Note over R: Гибридная оценка
    R->>R: Вычисление hybrid_score³
    R-->>L: Выбранный deployment
    
    L->>M: Перенаправление запроса
    M-->>L: Ответ модели
    L-->>C: HTTP ответ
    
    R->>A: Логирование решения⁴
    A->>A: Асинхронная запись в БД
```

**Примечания:**
¹ **ML Predictor запрос**: Отправка характеристик запроса (длина, тип задачи, сложность) для получения предсказаний времени ответа для всех доступных deployments

² **GPUStack данные**: Получение информации о физическом оборудовании (тип GPU, доступная память, архитектура) для улучшения точности ML предсказаний

³ **Hybrid scoring**: Вычисление итогового балла по формуле `hybrid_score = (1 - weight) × ml_prediction + weight × load_penalty`, где weight = 0.3

⁴ **Analytics logging**: Асинхронная запись всех данных о решении маршрутизации в PostgreSQL для последующего анализа и улучшения алгоритмов

### Архитектура данных

```mermaid
erDiagram
    REQUEST_LOG {
        uuid id PK
        timestamp created_at
        string request_type
        int request_length
        string selected_deployment
        float prediction_time
        float actual_time
        json metadata
    }
    
    DEPLOYMENT_METRICS {
        uuid id PK
        string deployment_name
        timestamp measured_at
        float avg_response_time
        int active_requests
        float gpu_utilization
        json hardware_specs
    }
    
    ROUTING_DECISIONS {
        uuid id PK
        uuid request_id FK
        string deployment_name
        float ml_score
        float load_score
        float hybrid_score
        boolean selected
        json decision_factors
    }
    
    REQUEST_LOG ||--o{ ROUTING_DECISIONS : "has_decisions"
    DEPLOYMENT_METRICS ||--o{ ROUTING_DECISIONS : "influences"
```

## Гибридная балансировка нагрузки

### Алгоритм гибридной оценки

Система использует комбинированный подход для выбора оптимального deployment:

```python
def calculate_hybrid_score(
    ml_prediction: float,
    current_load: int,
    max_load: int,
    weight: float = 0.3
) -> float:
    """
    Вычисление гибридного балла для deployment
    
    Args:
        ml_prediction: Предсказание времени ответа от ML (секунды)
        current_load: Текущее количество активных запросов
        max_load: Максимальная нагрузка для deployment
        weight: Вес load_penalty в итоговом балле (0.0-1.0)
    
    Returns:
        Гибридный балл (меньше = лучше)
    """
    # Нормализация ML предсказания (0-1)
    normalized_ml = min(ml_prediction / 10.0, 1.0)
    
    # Вычисление штрафа за нагрузку (0-1)
    load_penalty = current_load / max_load if max_load > 0 else 0
    
    # Гибридный балл
    hybrid_score = (1 - weight) * normalized_ml + weight * load_penalty
    
    return hybrid_score
```

### Стратегии fallback

1. **ML Predictor недоступен** → переход на least-busy алгоритм
2. **GPUStack недоступен** → использование кэшированных данных
3. **Все внешние сервисы недоступны** → round-robin маршрутизация
4. **Deployment перегружен** → автоматическое исключение из ротации

## Интеграция машинного обучения

### ML Predictor Service

```mermaid
graph LR
    subgraph "Request Analysis"
        A[Request Input] --> B[Text Length]
        A --> C[Task Type Detection]
        A --> D[Complexity Analysis]
    end
    
    subgraph "Feature Engineering"
        B --> E[Feature Vector]
        C --> E
        D --> E
        F[GPU Specs] --> E
        G[Model Info] --> E
    end
    
    subgraph "ML Pipeline"
        E --> H[Trained Model]
        H --> I[Response Time Predictions]
    end
    
    subgraph "Output"
        I --> J[Per-Deployment Predictions]
        J --> K[Confidence Scores]
    end
    
    style H fill:#fce4ec
    style E fill:#e8f5e8
    style J fill:#e1f5fe
```

### Типы предсказаний

1. **Chat задачи** - диалоговые запросы, короткие ответы
2. **Code задачи** - генерация и анализ кода
3. **Long-form задачи** - длинные тексты, документы
4. **Reasoning задачи** - логические рассуждения, математика

### Интеграция с GPUStack

GPUStack предоставляет данные о физическом оборудовании для улучшения точности ML предсказаний:

```json
{
  "worker_id": "gpu-worker-1",
  "gpu_specs": {
    "model": "NVIDIA H100",
    "memory_total": "80GB",
    "memory_available": "75GB",
    "compute_capability": "9.0"
  },
  "model_info": {
    "model_name": "llama-3.1-70b-instruct",
    "model_size": "70B",
    "quantization": "fp16",
    "context_length": 8192
  },
  "performance_metrics": {
    "tokens_per_second": 45.2,
    "avg_response_time": 2.3,
    "current_load": 3
  }
}
```

## Система кэширования

### Архитектура кэша

```mermaid
graph TB
    subgraph "Cache Layers"
        L1[In-Memory Cache<br/>TTL: 30s]
        L2[Redis Cache<br/>TTL: 5min]
        L3[Database<br/>Persistent]
    end
    
    subgraph "Cache Types"
        GPU[GPUStack Data<br/>96%+ hit rate]
        PRED[ML Predictions<br/>85%+ hit rate]
        CONF[Configuration<br/>99%+ hit rate]
    end
    
    Request --> L1
    L1 --> L2
    L2 --> L3
    
    L1 --> GPU
    L1 --> PRED
    L1 --> CONF
    
    style L1 fill:#e8f5e8
    style L2 fill:#fff3e0
    style L3 fill:#e1f5fe
```

### Стратегии кэширования

1. **GPUStack данные** - кэширование на 5 минут с высоким hit rate
2. **ML предсказания** - кэширование на 30 секунд для похожих запросов
3. **Конфигурация** - кэширование до изменения конфигурации
4. **Метрики производительности** - кэширование на 1 минуту

### Управление TTL

```python
CACHE_TTL_CONFIG = {
    'gpustack_workers': 300,      # 5 минут
    'ml_predictions': 30,         # 30 секунд
    'deployment_metrics': 60,     # 1 минута
    'configuration': 3600,        # 1 час
}
```

## Мониторинг и наблюдаемость

### Метрики системы

1. **Производительность маршрутизации**
   - Время принятия решения
   - Точность предсказаний
   - Hit rate кэша

2. **Качество балансировки**
   - Распределение нагрузки
   - Использование ресурсов
   - Время ответа по deployments

3. **Надежность интеграций**
   - Доступность ML Predictor
   - Доступность GPUStack
   - Частота fallback сценариев

### Логирование

```python
# Структурированное логирование решений маршрутизации
{
    "timestamp": "2024-01-15T10:30:00Z",
    "request_id": "req_123456",
    "routing_decision": {
        "selected_deployment": "gpu-h100-1",
        "ml_prediction": 2.3,
        "load_penalty": 0.2,
        "hybrid_score": 1.87,
        "confidence": 0.92
    },
    "alternatives": [
        {
            "deployment": "gpu-a6000-1",
            "hybrid_score": 2.15,
            "reason_not_selected": "higher_score"
        }
    ],
    "performance": {
        "decision_time_ms": 15,
        "cache_hits": ["gpustack_data", "ml_model"],
        "external_calls": ["predictor_api"]
    }
}
```

## Безопасность и аутентификация

### Уровни безопасности

1. **API аутентификация** - ключи доступа для всех внешних интеграций
2. **Внутренняя аутентификация** - токены для межсервисного взаимодействия
3. **Шифрование данных** - TLS для всех HTTP соединений
4. **Изоляция сервисов** - контейнеризация и сетевая изоляция

### Конфигурация безопасности

```yaml
security:
  litellm:
    master_key: "${LITELLM_MASTER_KEY}"
    salt_key: "${LITELLM_SALT_KEY}"
  
  gpustack:
    api_key: "${GPUSTACK_KEY}"
    token: "${GPUSTACK_TOKEN}"
    
  predictor:
    timeout: 5
    retry_attempts: 3
    
  database:
    ssl_mode: "require"
    connection_pool_size: 10
```

## Масштабирование и производительность

### Горизонтальное масштабирование

1. **Stateless архитектура** - роутер не хранит состояние между запросами
2. **Внешний кэш** - Redis для разделения кэша между инстансами
3. **Асинхронная аналитика** - неблокирующая запись в БД
4. **Connection pooling** - переиспользование соединений

### Оптимизации производительности

1. **Async/await** - асинхронная обработка всех внешних вызовов
2. **Batch processing** - группировка запросов к внешним сервисам
3. **Intelligent caching** - предиктивное кэширование часто используемых данных
4. **Circuit breaker** - защита от каскадных отказов

---

*Следующий раздел: [Руководство по эксплуатации](OPERATIONS_GUIDE.md)*