# Архитектура LLM Router

## Обзор проекта

**LLM Router** - это система интеллектуальной маршрутизации запросов к языковым моделям с гибридной балансировкой нагрузки, которая комбинирует ML-предсказания времени ответа с real-time данными о загруженности endpoints для оптимального выбора deployment'ов.

### Ключевые возможности

- **ML-предсказания** времени ответа через HTTP API к внешнему predictor service
- **Гибридная балансировка** нагрузки (ML predictions + least-busy tracking)  
- **GPUStack интеграция** для получения физических данных о GPU и моделях
- **Система аналитики** - детальное логирование решений роутинга и анализ производительности
- **Кастомные настройки** роутера с обходом ограничений LiteLLM
- **Комплексное тестирование** и валидация всей системы
- **Мониторинг** и диагностика производительности в реальном времени
- **Простая интеграция** с существующими LiteLLM deployments
- **Отказоустойчивость** с множественными fallback механизмами

## Полная архитектура системы

### Диаграмма архитектуры

```mermaid
graph TB
    subgraph "HTTP Client Layer"
        USER["HTTP Client<br/>POST /v1/chat/completions<br/>OpenAI Compatible API<br/>Messages<br/>Model<br/>Parameters"]
    end
    
    subgraph "LiteLLM Proxy Layer"
        PROXY["LiteLLM Proxy Server<br/>litellm_manager.py<br/>Request Processing<br/>Model Management<br/>Configuration API<br/>Authentication"]
    end
    
    subgraph "LiteLLM Core Router"
        LR["LiteLLM Router Core<br/>Built-in Router Engine<br/>Health Checks<br/>Deployment Discovery<br/>Custom Strategy<br/>Integration"]
    end
    
    subgraph "Custom Router Strategy"
        CR["LLMTimePredictorRoutingStrategy<br/>custom_router.py<br/>Main Decision Engine<br/>Orchestrator<br/>Intelligent Deployment<br/>Selection<br/>Health Check Management<br/>Fallback Mechanisms"]
    end
    
    subgraph "ML Prediction System"
        ML_PREDICTOR["ML Predictor Service<br/>predictor_client.py<br/>+<br/>predictor_integration.py<br/>External HTTP API<br/>Response Time<br/>Prediction<br/>Task Analysis:<br/>chat/code<br/>Hardware Analysis<br/>Model Path Extraction<br/>Performance<br/>Optimization"]
    end
    
    subgraph "Load Monitoring System"
        LOAD_MONITOR["Least-Busy Monitor<br/>least_busy_monitor.py<br/>Real-time Active<br/>Request Tracking<br/>LiteLLM Callback<br/>Integration<br/>Request Counter<br/>Management<br/>Load Balancing Data"]
    end
    
    subgraph "GPUStack Integration System"
        GPU_INTEGRATION["GPUStack Data Service<br/>gpustack_router_mixin.py<br/>+<br/>gpustack_integration.py<br/>Hardware Specs<br/>Worker Information<br/>Physical Machine Load<br/>GPU Utilization<br/>Model Instance Status<br/>Cache Management"]
    end
    
    subgraph "Hybrid Scoring Engine"
        HYBRID_SCORER["Hybrid Scoring Algorithm<br/>deployment_selector.py<br/>ML Predictions<br/>+<br/>Load Balancing<br/>+<br/>Hardware Data<br/>Final Deployment Ranking<br/>Selection"]
    end
    
    subgraph "Analytics Data Builder"
        DATA_BUILDER["Analytics Data Builder<br/>analytics/<br/>data_builder.py<br/>Router Analysis<br/>Metadata Creation<br/>Structured Data<br/>for Callbacks"]
    end
    
    subgraph "Target Deployments"
        DEPLOYMENT["Selected Deployment<br/>GPUStack Workers:<br/>3xH100 / A6000 / RTX4090<br/>LLM Model Endpoints<br/>Optimal Performance<br/>Target<br/>Hardware-Optimized<br/>Routing<br/>Dynamic Load<br/>Distribution"]
    end
    
    subgraph "LiteLLM Callback System"
        CALLBACK_SYSTEM["Success/Failure<br/>Callbacks<br/>LiteLLM Event<br/>Processing<br/>Request Lifecycle<br/>Tracking<br/>Metadata Extraction"]
    end
    
    subgraph "Analytics Metadata Storage"
        METADATA_STORE["Router Analysis<br/>Metadata<br/>request_kwargs<br/>['metadata']<br/>['router_analysis']<br/>Complete Analysis<br/>Data Storage<br/>Predictor Results<br/>+<br/>Load Data<br/>+<br/>Selection"]
    end
    
    subgraph "Analytics Processing System"
        ANALYTICS_LOGGER["RouterAnalyticsLogger<br/>analytics/<br/>router_analytics_logger.py<br/>Data Processing<br/>Validation<br/>Database Integration<br/>Queue Management"]
    end
    
    subgraph "Async Processing Queue"
        ASYNC_QUEUE["Analytics Queue<br/>asyncio.Queue<br/>Non-blocking Database<br/>Operations<br/>Performance Isolation"]
    end
    
    subgraph "PostgreSQL Database"
        DATABASE["Analytics Database<br/>LiteLLM_PromptLogs<br/>LiteLLM_PredictorLogs<br/>LiteLLM_ResponseLogs<br/>LiteLLM_EndpointAnalysis<br/>LiteLLM_Models<br/>LiteLLM_Deployments"]
    end
    
    subgraph "External Services"
        EXTERNAL_PREDICTOR["External ML<br/>Predictor API<br/>HTTP API Service<br/>Response Time<br/>Prediction Engine<br/>Deployed Separately"]
        
        EXTERNAL_GPUSTACK["External GPUStack API<br/>HTTP API Service<br/>Physical Machine<br/>Management<br/>Worker<br/>Model Instance Data"]
        
        REDIS_CACHE["Redis Cache (Optional)<br/>Router Cache Backend<br/>Least-Busy Data<br/>Storage<br/>LiteLLM Cache System"]
    end
    
    %% Main request flow
    USER --> PROXY
    PROXY --> LR
    LR --> CR
    
    %% Data collection phase (parallel queries)
    CR --> ML_PREDICTOR
    CR --> LOAD_MONITOR
    CR --> GPU_INTEGRATION
    
    %% External API connections
    ML_PREDICTOR -.-> EXTERNAL_PREDICTOR
    GPU_INTEGRATION -.-> EXTERNAL_GPUSTACK
    LOAD_MONITOR -.-> REDIS_CACHE
    
    %% Hybrid scoring phase
    ML_PREDICTOR --> HYBRID_SCORER
    LOAD_MONITOR --> HYBRID_SCORER
    GPU_INTEGRATION --> HYBRID_SCORER
    
    %% Analytics data preparation
    CR --> DATA_BUILDER
    HYBRID_SCORER --> DATA_BUILDER
    DATA_BUILDER --> METADATA_STORE
    
    %% Deployment selection and execution
    HYBRID_SCORER --> DEPLOYMENT
    DEPLOYMENT --> CALLBACK_SYSTEM
    
    %% Analytics processing flow (asynchronous)
    CALLBACK_SYSTEM --> ANALYTICS_LOGGER
    METADATA_STORE --> ANALYTICS_LOGGER
    ANALYTICS_LOGGER --> ASYNC_QUEUE
    ASYNC_QUEUE --> DATABASE
    
    %% Load balancing callback integration
    CALLBACK_SYSTEM -.-> LOAD_MONITOR
    
    %% Response flow
    CALLBACK_SYSTEM --> USER
    DEPLOYMENT -.-> USER
    
    %% Styling
    style USER fill:#e1f5fe
    style PROXY fill:#f3e5f5
    style LR fill:#e8eaf6
    style CR fill:#fff3e0
    style ML_PREDICTOR fill:#e8f5e8
    style LOAD_MONITOR fill:#e8f5e8
    style GPU_INTEGRATION fill:#ffeb3b
    style HYBRID_SCORER fill:#ff9800
    style DATA_BUILDER fill:#f1f8e9
    style DEPLOYMENT fill:#4caf50
    style CALLBACK_SYSTEM fill:#e8f4f8
    style ANALYTICS_LOGGER fill:#e8f5e8
    style ASYNC_QUEUE fill:#e3f2fd
    style METADATA_STORE fill:#f3e5f5
    style DATABASE fill:#9c27b0
    style EXTERNAL_PREDICTOR fill:#ffcdd2
    style EXTERNAL_GPUSTACK fill:#ffcdd2
    style REDIS_CACHE fill:#fff9c4
```

## Поток данных в системе

### Детальная последовательность обработки запроса

```mermaid
sequenceDiagram
    participant Client as "HTTP Client"
    participant Proxy as "LiteLLM Proxy"
    participant Router as "LiteLLM Router"
    participant CustomRouter as "Custom Router"
    participant InputCB as "Input Callbacks"
    participant LeastBusy as "Least-Busy Monitor"
    participant Cache as "Router Cache"
    participant GPUStack as "GPUStack API"
    participant Predictor as "ML Predictor API"
    participant Deployment as "Selected Deployment"
    participant Callbacks as "Success Callbacks"
    participant Analytics as "Analytics Logger"
    participant Queue as "Async Queue"
    participant DB as "PostgreSQL Database"
    
    %% Request initiation
    Client->>Proxy: POST /v1/chat/completions<br/>{model, messages, stream}
    Proxy->>Proxy: Generate litellm_call_id<br/>Create request context
    
    %% Pre-API call tracking
    Proxy->>InputCB: log_pre_api_call()<br/>Track request start
    InputCB->>LeastBusy: increment_active_requests()
    LeastBusy->>Cache: Update request counts<br/>{deployment_id: count+1}
    
    %% Custom routing decision
    Proxy->>Router: Route request
    Router->>CustomRouter: get_available_deployment()<br/>(model, messages, request_kwargs)
    
    %% Get healthy deployments
    CustomRouter->>CustomRouter: _get_healthy_deployments_for_model()
    CustomRouter->>CustomRouter: Found healthy deployments:<br/>deployment-1, deployment-2, deployment-3
    
    %% GPUStack data collection (parallel)
    CustomRouter->>GPUStack: GET /v1/workers<br/>GET /v1/models<br/>GET /v1/model-instances
    GPUStack-->>CustomRouter: Physical data¹
    
    %% ML prediction analysis  
    CustomRouter->>Predictor: POST /predict²
    Predictor-->>CustomRouter: ML predictions³
    
    %% Load balancing data
    CustomRouter->>Cache: _get_least_busy_data_if_available()
    Cache-->>CustomRouter: Active request counts⁴
    
    %% Hybrid scoring calculation
    CustomRouter->>CustomRouter: Calculate hybrid scores⁵
    
    %% Analytics data preparation
    CustomRouter->>CustomRouter: Create router analysis metadata⁶
    CustomRouter->>CustomRouter: Add metadata to request_kwargs
    
    %% Return selected deployment
    CustomRouter-->>Router: Selected deployment⁷
    Router-->>Proxy: Forward selected deployment
    
    %% Execute request to selected deployment
    Proxy->>Deployment: HTTP request to selected deployment
    Deployment-->>Proxy: LLM response
    
    %% Post-request callbacks
    Proxy->>Callbacks: success_callback(response_obj, kwargs)
    
    %% Analytics processing (parallel, non-blocking)
    Callbacks->>Analytics: Process router analysis data⁸
    Analytics->>Analytics: Combine router + LiteLLM response data
    Analytics->>Queue: Enqueue analytics data
    
    %% Database logging (asynchronous, independent)
    Queue->>DB: INSERT INTO LiteLLM_Models
    Queue->>DB: INSERT INTO LiteLLM_Deployments  
    Queue->>DB: INSERT INTO LiteLLM_PromptLogs
    Queue->>DB: INSERT INTO LiteLLM_PredictorLogs
    Queue->>DB: INSERT INTO LiteLLM_EndpointAnalysis
    Queue->>DB: INSERT INTO LiteLLM_ResponseLogs
    
    %% Load balancing cleanup
    Callbacks->>LeastBusy: log_success_event()
    LeastBusy->>Cache: Update request counts
    
    %% Response to client
    Proxy-->>Client: HTTP Response
```

**¹ Физические данные:**
- Характеристики GPU: RTX4090, H100, A6000
- Пути моделей: qwen2.5-*.gguf  
- Информация о воркерах: gpu-node-001, etc.

**² POST /predict запрос:**
```json
{
  "task_type": "chat",
  "prompt_length": 150,
  "deployments": [
    {"id": "deploy-1", "hardware": "3xH100"},
    {"id": "deploy-2", "hardware": "A6000"},
    {"id": "deploy-3", "hardware": "RTX4090"}
  ]
}
```

**³ ML предсказания:**
```json
{
  "deploy-1": 2.5,
  "deploy-2": 1.8,
  "deploy-3": 3.2
}
```

**⁴ Количество активных запросов:**
```json
{
  "deploy-1": 2,
  "deploy-2": 0,
  "deploy-3": 1
}
```

**⁵ Расчет гибридных scores:**
- weight = 0.3 (load_balancing_weight)
- deploy-1: (2.5 × 0.7) + (2 × 0.3) = 2.35
- deploy-2: (1.8 × 0.7) + (0 × 0.3) = 1.26
- deploy-3: (3.2 × 0.7) + (1 × 0.3) = 2.54
- **ВЫБРАН: deploy-2** (наименьший score)

**⁶ Метаданные анализа роутера:**
- ML предсказания для всех deployment'ов
- Гибридные scores и ранжирование
- Обоснование выбора
- Физические данные GPUStack  
- Время запроса и параметры

**⁷ Выбранный deployment:**
- deploy-2 с дополненными метаданными

**⁸ Извлечение данных анализа роутера:**
- Результаты анализа предиктора
- Детали гибридного scoring
- Данные о производительности endpoints
- Обоснование выбора

> **Примечание:** Обработка аналитики выполняется независимо от основного потока запросов. Ошибки в аналитике не влияют на доставку ответа клиенту.

## Архитектура кодовой базы

### Структура проекта

```
llm_router/                     # Главная директория проекта
├── __init__.py                # Инициализация пакета и экспорты
├── __main__.py                # Точка входа для python -m llm_router
├── start_litellm_with_router.py # Главный startup скрипт
├── litellm_patch.py           # Интеграционный патч LiteLLM
├── logging_utils.py           # Централизованная конфигурация логирования
├── docker-compose.yml         # Полный стек развертывания
├── Dockerfile                 # Многоэтапная сборка контейнера
├── run.sh                     # Главный интерфейс команд (44+ команд)
└── entrypoint.sh              # Скрипт входа контейнера
```

### Основные модули системы

#### Router System (`router/`)
- **custom_router.py**: Главная реализация `LLMTimePredictorRoutingStrategy`
- **router_factory.py**: Фабричный паттерн для создания стратегий маршрутизации
- **deployment_selector_mixin.py**: Гибридный алгоритм скоринга и выбора deployment
- **router_predictor_mixin.py**: Интеграционный миксин ML predictor
- **gpustack_router_mixin.py**: Интеграционный миксин данных GPUStack
- **router_initialization_mixin.py**: Настройка и конфигурация роутера
- **router_utils_mixin.py**: Общие утилиты и вспомогательные методы
- **least_busy_integration.py**: Интеграция отслеживания нагрузки в реальном времени

#### Слои интеграции

**LiteLLM Integration (`router_litellm/`)**
- **litellm_manager.py**: Управление сервером прокси LiteLLM
- **litellm_config_api.py**: Интеграция API конфигурации
- **litellm_auth.py**: Обработка аутентификации
- **litellm.config.yaml**: Главный файл конфигурации LiteLLM

**GPUStack Integration (`router_gpustack/`)**
- **gpustack_integration.py**: Главный клиент API GPUStack
- **gpustack_auth.py**: Аутентификация с поддержкой KEY/TOKEN/PASSWORD
- **gpustack_cache_manager.py**: Управление кэшем для данных GPUStack
- **gpustack_physical_machines_cache.py**: Кэширование данных физических машин (96%+ эффективность)
- **gpustack_info.py**: Утилиты информации GPUStack
- **list_gpustack_models.py**: Обнаружение и листинг моделей

**ML Predictor Integration (`predictor/`)**
- **predictor_client.py**: HTTP клиент для внешнего сервиса ML predictor
- **__init__.py**: Экспорты модуля predictor

### Система аналитики (`analytics/`)
- **router_analytics_logger.py**: Главная система логирования аналитики
- **data_builder.py**: Создание структур данных аналитики
- **db_client.py**: Управление подключением к базе данных
- **sql_builder.py**: Построение SQL запросов
- **prisma_client.py**: Интеграция Prisma ORM
- **schema.prisma**: Определение схемы базы данных
- **migrations/**: Файлы миграций базы данных
- **export/**: Утилиты экспорта данных

### Управление конфигурацией (`config/`)
- **config_manager.py**: Главное управление конфигурацией
- **config_manager_models.py**: Модели данных конфигурации
- **service_config.py**: Конфигурация специфичная для сервисов
- **host_utils.py**: Утилиты хоста и окружения
- **configure_routing_parameters.py**: Настройка параметров маршрутизации
- **update_config.py**: Утилиты обновления конфигурации

### Архитектурные паттерны

#### Mixin Architecture
Функциональность роутера разделена на специализированные миксины:

```python
class LLMTimePredictorRoutingStrategy(
    CustomRoutingStrategyBase,      # Базовый класс от LiteLLM
    PredictorIntegrationMixin,      # ML predictor интеграция
    DeploymentSelectorMixin,        # Логика выбора deployment
    GPUStackRouterMixin            # GPUStack интеграция
):
    """Главный роутер с композицией через mixins."""
```

#### Factory Pattern
Создание стратегий маршрутизации через фабрику:

```python
def create_routing_strategy(config: Dict) -> CustomRoutingStrategyBase:
    """Создание стратегии маршрутизации на основе конфигурации"""
    return LLMTimePredictorRoutingStrategy(config)
```

#### Integration Pattern
Чистое разделение между внешними сервисами:
- Асинхронные HTTP клиенты для внешних сервисов
- Механизмы fallback при недоступности сервисов
- Изоляция ошибок интеграции

#### Analytics Pattern
- Асинхронная очередь для аналитики предотвращает блокировку
- Структурированное логирование данных с PostgreSQL
- Анализ и отчетность на основе метаданных

## Детальная архитектура компонентов

### 1. Custom Router Core (custom_router.py)

**LLMTimePredictorRoutingStrategy** - основной класс стратегии роутинга:

```python
class LLMTimePredictorRoutingStrategy(
    CustomRoutingStrategyBase,      # Базовый класс от LiteLLM
    PredictorIntegrationMixin,      # ML predictor интеграция  
    DeploymentSelectorMixin,        # Логика выбора deployment
    GPUStackRouterMixin             # GPUStack интеграция
):
```

**Ключевые методы:**
- `get_available_deployment()` - главная точка входа для выбора deployment
- `_select_deployment_with_predictor()` - логика выбора с ML предсказаниями
- `_generate_ml_predictions()` - получение предсказаний от ML сервиса
- `_get_task_type_for_deployment()` - определение типа задачи по имени модели

### 2. ML Prediction System

**PredictorClient** (`predictor_client.py`):
- HTTP клиент для общения с внешним ML сервисом
- Методы: `predict()`, `health_check()`, `get_service_info()`
- Обработка ошибок и retry логика
- Таймауты и connection pooling

**PredictorIntegrationMixin** (`predictor_integration.py`):
- Подготовка данных для отправки в predictor
- Извлечение физических путей моделей из GPUStack
- Подготовка hardware information
- Обработка ответов predictor

### 3. Load Monitoring System

**LeastBusyMonitor** (`least_busy_monitor.py`):
- Отслеживание активных запросов через callbacks LiteLLM
- Интеграция с Router.cache для хранения счетчиков
- Методы: `log_pre_api_call()`, `log_success_event()`, `log_failure_event()`

**Структура данных в кэше:**
```json
// Ключ: "{model_group}_request_count"
{
    "deployment-1": 3,  // 3 активных запроса
    "deployment-2": 1,  // 1 активный запрос  
    "deployment-3": 0   // нет активных запросов
}
```

### 4. GPUStack Integration Layer

**GPUStackAuth** (`gpustack_auth.py`):
- Поддержка KEY, TOKEN, PASSWORD аутентификации
- Асинхронные HTTP запросы с aiohttp
- Централизованная обработка ошибок

**GPUStackDataFetcher** (`gpustack_integration.py`):
- Параллельные запросы к 3 endpoints: workers, models, model-instances
- Rate limiting (минимум 30 секунд между обновлениями)
- Преобразование данных в унифицированные структуры

**GPUStackPhysicalMachinesCache** (`gpustack_physical_machines_cache.py`):
- TTL управление (по умолчанию 5 минут)
- Backend агностик: Redis/InMemory/Disk кэши
- Статистика эффективности: 96%+ hit rate
- Performance logging

**GPUStackRouterMixin** (`gpustack_router_mixin.py`):
- Интеграция GPUStack данных в router workflow
- Обогащение deployment данными
- Health checks и мониторинг доступности

### 5. Analytics System

**RouterAnalyticsLogger** (`analytics/router_analytics_logger.py`):
- Обработка metadata из custom router
- Асинхронная запись в PostgreSQL через очереди
- Создание нормализованных структур данных

**Data Builder** (`analytics/data_builder.py`):
- Создание router analysis metadata
- Извлечение данных из различных источников
- Подготовка данных для записи в БД

**Async Queue System:**
- Non-blocking обработка аналитических данных
- Graceful degradation при ошибках
- Независимость от основного потока запросов

### 6. Database Schema

**Основные таблицы:**
- `LiteLLM_PromptLogs` - промпты и параметры запросов
- `LiteLLM_PredictorLogs` - результаты ML анализа  
- `LiteLLM_EndpointAnalysis` - детальные данные по каждому endpoint
- `LiteLLM_ResponseLogs` - реальная производительность
- `LiteLLM_Models` / `LiteLLM_Deployments` - справочники

**Связи:** Все таблицы связаны через `request_id` (litellm_call_id)

## Архитектура данных

### Схема базы данных

```mermaid
erDiagram
    LiteLLM_PromptLogs {
        uuid request_id PK
        timestamp created_at
        string model_name
        text prompt_text
        int prompt_tokens
        float temperature
        int max_tokens
        json metadata
    }
    
    LiteLLM_PredictorLogs {
        uuid id PK
        uuid request_id FK
        string deployment_id
        float predicted_time
        float confidence_score
        string task_type
        json predictor_metadata
    }
    
    LiteLLM_EndpointAnalysis {
        uuid id PK
        uuid request_id FK
        string deployment_id
        float hybrid_score
        int load_penalty
        float ml_score
        boolean selected
        json hardware_specs
    }
    
    LiteLLM_ResponseLogs {
        uuid id PK
        uuid request_id FK
        timestamp response_time
        int completion_tokens
        float actual_duration
        float prediction_accuracy
        json response_metadata
    }
    
    LiteLLM_Models {
        string model_name PK
        string model_type
        json model_config
        timestamp created_at
    }
    
    LiteLLM_Deployments {
        string deployment_id PK
        string model_name FK
        string worker_name
        json hardware_config
        string status
        timestamp last_seen
    }
    
    LiteLLM_PromptLogs ||--o{ LiteLLM_PredictorLogs : "has_predictions"
    LiteLLM_PromptLogs ||--o{ LiteLLM_EndpointAnalysis : "has_analysis"
    LiteLLM_PromptLogs ||--|| LiteLLM_ResponseLogs : "has_response"
    LiteLLM_Models ||--o{ LiteLLM_Deployments : "deployed_as"
    LiteLLM_Deployments ||--o{ LiteLLM_EndpointAnalysis : "analyzed_in"
```

## Конфигурация системы

### Упрощенная структура конфигурации

Система теперь использует константы вместо динамических настроек для упрощения конфигурации:

```python
# В constants.py
GPUSTACK_ENABLED: bool = True
GPUSTACK_CACHE_TTL: int = 30  # секунды
LOAD_BALANCING_WEIGHT: float = 0.3  # 30% load, 70% ML prediction
DEFAULT_TASK_TYPE: str = "chat"

# Маппинг имен моделей на типы задач
MODEL_NAME_TO_TASK_TYPE_MAPPING = {
    "chat_instruct": "chat",
    "chat_coder": "code"
}
```

```yaml
# В litellm.config.yaml - только модели, без router_settings
model_list:
  - model_name: chat_instruct
    litellm_params:
      model: openai/chat_instruct
    model_info:
      id: instruct-endpoint-1
  - model_name: chat_coder
    litellm_params:
      model: openai/chat_coder
    model_info:
      id: coder-endpoint-1
```

### Environment Variables

```bash
# ML Predictor Service
PREDICTOR_BASE_URL="http://predictor:8000"
PREDICTOR_API_KEY="your-api-key"
PREDICTOR_TIMEOUT=30

# GPUStack Integration  
GPUSTACK_BASE_URL="http://gpustack:80"
GPUSTACK_AUTH_TYPE="KEY"
GPUSTACK_API_KEY="your-gpustack-key"

# Analytics System
DATABASE_URL="postgresql://user:pass@localhost:5432/analytics"
ANALYTICS_ENABLED=true
ANALYTICS_USE_ASYNC_QUEUE=true

# Caching
REDIS_URL="redis://localhost:6379/0"
CACHE_TTL=300  # 5 minutes
```

## Процесс принятия решений

### Hybrid Scoring Algorithm

```python
def calculate_hybrid_score(ml_prediction: float, active_requests: int, weight: float) -> float:
    """
    weight = load_balancing_weight (0.0 - 1.0)
    
    weight = 0.0 -> 100% ML prediction
    weight = 1.0 -> 100% load balancing  
    weight = 0.3 -> 70% ML + 30% load (default)
    """
    prediction_component = ml_prediction * (1.0 - weight)
    load_component = active_requests * weight
    
    return prediction_component + load_component
```

### Selection Process

1. **Получение healthy deployments** для запрашиваемой модели
2. **GPUStack data collection** - физические данные о GPU и моделях
3. **ML prediction** - запрос к predictor service для всех deployments
4. **Load data collection** - текущие активные запросы из кэша
5. **Hybrid scoring** - расчет композитного score для каждого deployment
6. **Ranking** - сортировка по score (меньше = лучше)
7. **Selection** - выбор deployment с минимальным score
8. **Metadata creation** - сохранение всех данных анализа для аналитики

### Стратегии fallback

1. **ML Predictor недоступен** → переход на least-busy алгоритм
2. **GPUStack недоступен** → использование кэшированных данных
3. **Все внешние сервисы недоступны** → round-robin маршрутизация
4. **Deployment перегружен** → автоматическое исключение из ротации

## Интеграция машинного обучения

### ML Predictor Service

```mermaid
graph LR
    subgraph "Request Analysis"
        A[Request Input] --> B[Text Length]
        A --> C[Task Type Detection]
        A --> D[Complexity Analysis]
    end
    
    subgraph "Feature Engineering"
        B --> E[Feature Vector]
        C --> E
        D --> E
        F[GPU Specs] --> E
        G[Model Info] --> E
    end
    
    subgraph "ML Pipeline"
        E --> H[Trained Model]
        H --> I[Response Time Predictions]
    end
    
    subgraph "Output"
        I --> J[Per-Deployment Predictions]
        J --> K[Confidence Scores]
    end
    
    style H fill:#fce4ec
    style E fill:#e8f5e8
    style J fill:#e1f5fe
```

### Типы предсказаний

1. **Chat задачи** - диалоговые запросы, короткие ответы
2. **Code задачи** - генерация и анализ кода
3. **Long-form задачи** - длинные тексты, документы
4. **Reasoning задачи** - логические рассуждения, математика

### Интеграция с GPUStack

GPUStack предоставляет данные о физическом оборудовании для улучшения точности ML предсказаний:

```json
{
  "worker_id": "gpu-worker-1",
  "gpu_specs": {
    "model": "NVIDIA H100",
    "memory_total": "80GB",
    "memory_available": "75GB",
    "compute_capability": "9.0"
  },
  "model_info": {
    "model_name": "llama-3.1-70b-instruct",
    "model_size": "70B",
    "quantization": "fp16",
    "context_length": 8192
  },
  "performance_metrics": {
    "tokens_per_second": 45.2,
    "avg_response_time": 2.3,
    "current_load": 3
  }
}
```

## Механизм интеграции: роль litellm_patch.py

Интеграция кастомного роутера с LiteLLM происходит автоматически при импорте модуля `llm_router.litellm_patch`. Этот патч "на лету" перехватывает инициализацию стандартного `litellm.Router` и, если в конфигурации указано `routing_strategy: "custom"`, подменяет стандартную логику на экземпляр `LLMTimePredictorRoutingStrategy`. Это позволяет использовать продвинутую маршрутизацию без необходимости изменять исходный код LiteLLM.

### Ответственность компонентов

| Компонент | Ответственность | Синхронность | Ключевые методы |
| :--- | :--- | :--- | :--- |
| **`LLMTimePredictorRoutingStrategy`** | Оркестрация всего процесса выбора развертывания | `async` | `get_available_deployment()` |
| **`GPUStackRouterMixin`** | Обогащение данными GPUStack, кэширование | `async` | `enrich_deployments_with_gpustack_data()` |
| **`PredictorIntegrationMixin`** | Параллельные вызовы ML Predictor API | `async` | `_predict_response_time_for_deployment()` |
| **`DeploymentSelectorMixin`** | Сбор least-busy данных, гибридный скоринг | `sync` | `_select_deployment_with_hybrid_scoring()` |
| **`RouterAnalyticsMixin`** | Упаковка аналитических данных в metadata | `sync` | `create_router_analysis_data()` |
| **`RouterAnalyticsLogger`** | Извлечение данных из callbacks и запись в очередь | `async` | `async_log_success_event()` |
| **`AsyncQueue Worker`** | Фоновая запись из очереди в PostgreSQL | `async` | Background processing |

## Система кэширования

### Архитектура кэша

```mermaid
graph TB
    subgraph "Cache Layers"
        L1[In-Memory Cache<br/>TTL: 30s]
        L2[Redis Cache<br/>TTL: 5min]
        L3[Database<br/>Persistent]
    end
    
    subgraph "Cache Types"
        GPU[GPUStack Data<br/>96%+ hit rate]
        PRED[ML Predictions<br/>85%+ hit rate]
        CONF[Configuration<br/>99%+ hit rate]
    end
    
    Request --> L1
    L1 --> L2
    L2 --> L3
    
    L1 --> GPU
    L1 --> PRED
    L1 --> CONF
    
    style L1 fill:#e8f5e8
    style L2 fill:#fff3e0
    style L3 fill:#e1f5fe
```

### Стратегии кэширования

1. **GPUStack данные** - кэширование на 5 минут с высоким hit rate
2. **ML предсказания** - кэширование на 30 секунд для похожих запросов
3. **Конфигурация** - кэширование до изменения конфигурации
4. **Метрики производительности** - кэширование на 1 минуту

### Управление TTL

```python
CACHE_TTL_CONFIG = {
    'gpustack_workers': 300,      # 5 минут
    'ml_predictions': 30,         # 30 секунд
    'deployment_metrics': 60,     # 1 минута
    'configuration': 3600,        # 1 час
}
```

## Архитектурные обоснования

### Почему выбрана Mixin-архитектура в CustomRouter?

**Решение**: `LLMTimePredictorRoutingStrategy` наследуется от `GPUStackRouterMixin`

**Обоснование**:
- **Композиция функционала**: Позволяет независимо разрабатывать логику интеграции с предиктором, GPUStack и алгоритмами выбора
- **Тестируемость**: Каждый mixin можно тестировать изолированно
- **Расширяемость**: Легко добавлять новые источники данных (например, Kubernetes metrics) без перегрузки основного класса
- **Принцип единственной ответственности**: Каждый mixin отвечает за свою область (GPUStack, Predictor, Analytics)

### Почему Async Queue в RouterAnalyticsLogger?

**Решение**: Асинхронная очередь для записи в базу данных аналитики

**Обоснование**:
- **Изоляция производительности**: Полная изоляция основного потока обработки запросов от задержек при записи в БД
- **Отказоустойчивость**: Проблемы с аналитикой (медленная БД, отключение) никогда не влияют на время ответа для пользователя
- **Буферизация**: Возможность группировать записи для более эффективного взаимодействия с БД
- **Мониторинг**: Размер очереди служит метрикой производительности системы аналитики

### Почему Hybrid Scoring в DeploymentSelector?

**Решение**: Комбинирование ML предсказаний и least-busy данных

**Обоснование**:
- **Точность**: ML предсказания дают хорошую базовую оценку времени ответа
- **Актуальность**: Least-busy данные отражают текущую нагрузку в реальном времени
- **Балансировка нагрузки**: Предотвращает перегрузку быстрых endpoints за счет перенаправления на менее загруженные
- **Fallback стратегия**: При недоступности одного из источников система продолжает работать

## Мониторинг и диагностика

### Ключевые логи

```bash
# Анализ выбора endpoints
./run.sh logs litellm | grep "SELECTION:"

# Статистика кэша GPUStack
./run.sh logs litellm | grep "CACHE:"

# ML predictor анализ
./run.sh logs litellm | grep "PREDICTOR:"

# Least-busy tracking
./run.sh logs litellm | grep "LEAST_BUSY:"

# События аналитики
./run.sh logs litellm | grep "ANALYTICS:"
```

### Health Checks

```bash
# Проверка всей системы
./run.sh config validate

# Тест роутинга
./run.sh config test-routing

# Проверка predictor service
./run.sh config test-predictor

# Статус GPUStack интеграции
./run.sh config test-gpustack
```

### Метрики системы

1. **Производительность маршрутизации**
   - Время принятия решения
   - Точность предсказаний
   - Hit rate кэша

2. **Качество балансировки**
   - Распределение нагрузки
   - Использование ресурсов
   - Время ответа по deployments

3. **Надежность интеграций**
   - Доступность ML Predictor
   - Доступность GPUStack
   - Частота fallback сценариев

### Структурированное логирование

```python
# Структурированное логирование решений маршрутизации
{
    "timestamp": "2024-01-15T10:30:00Z",
    "request_id": "req_123456",
    "routing_decision": {
        "selected_deployment": "gpu-h100-1",
        "ml_prediction": 2.3,
        "load_penalty": 0.2,
        "hybrid_score": 1.87,
        "confidence": 0.92
    },
    "alternatives": [
        {
            "deployment": "gpu-a6000-1",
            "hybrid_score": 2.15,
            "reason_not_selected": "higher_score"
        }
    ],
    "performance": {
        "decision_time_ms": 15,
        "cache_hits": ["gpustack_data", "ml_model"],
        "external_calls": ["predictor_api"]
    }
}
```

## Производительность и масштабирование

### Оптимизации

1. **Кэширование GPUStack данных** - 96%+ hit rate, TTL 5 минут
2. **Асинхронная аналитика** - non-blocking через очереди
3. **Connection pooling** - для HTTP клиентов (predictor, GPUStack)
4. **Параллельные запросы** - к GPUStack endpoints
5. **Least-busy в памяти** - прямой доступ к Router.cache

### Масштабирование

- **Горизонтальное**: Поддержка множественных LiteLLM instances
- **Вертикальное**: Оптимизация memory usage и CPU
- **Database**: PostgreSQL с индексами для аналитических запросов
- **Cache**: Redis cluster для high availability

### Нефункциональные требования

**Analytics System**:
- Генерация отчета `summary` за последние 24 часа на 1 млн записей: < 5 секунд
- Analytics Lag (время от HTTP запроса до записи в БД): < 5 секунд
- Размер async queue: < 1000 элементов в нормальных условиях

**Router Core**:
- Добавление аналитики не должно увеличивать время ответа Router на > 1%
- Hybrid scoring должен выполняться за < 100ms

## Безопасность и аутентификация

### Уровни безопасности

1. **API аутентификация** - ключи доступа для всех внешних интеграций
2. **Внутренняя аутентификация** - токены для межсервисного взаимодействия
3. **Шифрование данных** - TLS для всех HTTP соединений
4. **Изоляция сервисов** - контейнеризация и сетевая изоляция

### Конфигурация безопасности

```yaml
security:
  litellm:
    master_key: "${LITELLM_MASTER_KEY}"
    salt_key: "${LITELLM_SALT_KEY}"
  
  gpustack:
    api_key: "${GPUSTACK_KEY}"
    token: "${GPUSTACK_TOKEN}"
    
  predictor:
    timeout: 5
    retry_attempts: 3
    
  database:
    ssl_mode: "require"
    connection_pool_size: 10
```

## Управление скриптами и командами

### Архитектура Scripts Directory

```
llm-router/scripts/                 # MASTER SCRIPTS ARCHITECTURE
├── analytics/                      # Analytics система (полностью реализовано)
│   ├── main.sh (11KB)             # Command dispatcher для analytics
│   ├── migrate.sh (37KB)          # Миграции БД - основной модуль
│   ├── database.sh (20KB)         # Database operations
│   ├── data.sh (25KB)             # Data operations и validation
│   ├── health.sh (12KB)           # Health checks и monitoring
│   ├── test.sh (16KB)             # E2E testing framework
│   ├── maintenance.sh (15KB)      # Maintenance operations
│   ├── reports.sh (2.1KB)         # Reports (ПЛАНИРУЕТСЯ к расширению)
│   ├── analytics_utils.sh (8.6KB) # Общие утилиты
│   └── analytics_queries.sql (12KB) # SQL запросы
├── gpustack/                       # GPUStack интеграция
│   ├── credentials.sh (15KB)      # Управление аутентификацией
│   ├── models.sh (8.8KB)          # Управление моделями
│   └── info.sh (7.8KB)            # Информация о ресурсах
├── docker/                         # Docker operations
│   ├── services.sh (14KB)         # Управление сервисами (основной)
│   ├── custom.sh (9.9KB)          # Custom docker operations
│   ├── logs.sh (6.4KB)            # Логирование и мониторинг
│   └── sync.sh (4.6KB)            # Синхронизация конфигураций
├── config/                         # Configuration management  
│   ├── validation.sh (8.8KB)     # Валидация конфигураций
│   ├── settings.sh (6.8KB)       # Настройки системы
│   ├── manager.sh (6.2KB)        # Config manager operations
│   └── models.sh (5.2KB)         # Model configurations
├── testing/                        # Testing infrastructure
│   ├── router.sh (15KB)           # Router testing (основной)
│   ├── health.sh (9.6KB)         # Health testing
│   ├── models.sh (8.0KB)         # Model testing
│   └── auth.sh (4.1KB)           # Authentication testing
├── common/                         # Общие библиотеки (критически важно)
│   ├── functions.sh (16KB)        # Общие функции - core library
│   ├── constants.sh (12KB)        # Константы проекта
│   ├── paths.sh (4.5KB)          # Path management
│   └── loader.sh (1.8KB)         # Module loading system
├── monitoring/                     # Мониторинг
│   └── routing.sh (2.7KB)        # Router monitoring
└── debug/                          # Отладка
    └── router.sh (3.9KB)         # Router debugging
```

### Master CLI Entry Point: run.sh

**Файл:** `run.sh` (37KB, 1095 строк) - центральный диспетчер команд

```bash
#!/bin/bash
# Главный entry point для всех операций системы

main() {
    local category="$1"
    shift
    
    case "$category" in
        analytics)  handle_analytics_command "$@" ;;    # Analytics система
        gpustack)   handle_gpustack_command "$@" ;;     # GPUStack операции
        docker)     handle_docker_command "$@" ;;       # Docker управление
        config)     handle_config_command "$@" ;;       # Configuration
        test)       handle_test_command "$@" ;;         # Testing
        monitor)    handle_monitor_command "$@" ;;      # Monitoring
        debug)      handle_debug_command "$@" ;;        # Debugging
        *)          show_help ;;
    esac
}
```

---

*Следующий раздел: [Руководство по эксплуатации](OPERATIONS_GUIDE.md)*