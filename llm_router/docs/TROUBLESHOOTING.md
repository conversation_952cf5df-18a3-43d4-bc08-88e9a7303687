# Руководство по устранению неполадок

Данное руководство содержит решения для наиболее частых проблем, диагностические инструменты и процедуры восстановления системы LLM Router.

## Частые проблемы

### Проблемы запуска системы

#### Проблема: Сервисы не запускаются

**Симптомы:**
- `./run.sh docker up` завершается с ошибкой
- Контейнеры постоянно перезапускаются
- Ошибки в логах при запуске

**Диагностика:**
```bash
# Проверка статуса сервисов
./run.sh docker status

# Просмотр логов
./run.sh docker logs litellm
./run.sh docker logs db

# Проверка конфигурации
./run.sh config validate
```

**Решения:**

1. **Проверить переменные окружения:**
   ```bash
   # Убедиться, что .env файл существует и заполнен
   ls -la .env
   cat .env | grep -v "^#" | grep -v "^$"
   ```

2. **Проверить порты:**
   ```bash
   # Убедиться, что порты не заняты
   netstat -tulpn | grep -E ":(4000|5432|6379|9090)"
   
   # Освободить порты если нужно
   sudo fuser -k 4000/tcp
   ```

3. **Проверить Docker ресурсы:**
   ```bash
   # Проверить доступное место на диске
   df -h
   
   # Проверить Docker ресурсы
   docker system df
   
   # Очистить неиспользуемые ресурсы
   docker system prune -f
   ```

4. **Пересоздать сервисы:**
   ```bash
   ./run.sh docker down
   ./run.sh docker up --build --force-recreate
   ```

#### Проблема: База данных не подключается

**Симптомы:**
- Ошибки подключения к PostgreSQL
- "Connection refused" в логах
- Миграции не применяются

**Диагностика:**
```bash
# Проверка статуса БД
./run.sh docker logs db

# Тест подключения
./run.sh config test-database

# Проверка сетевого подключения
docker exec llm_router_litellm_1 ping db
```

**Решения:**

1. **Проверить конфигурацию БД:**
   ```bash
   # Проверить переменные окружения
   echo $DATABASE_URL
   echo $POSTGRES_PASSWORD
   
   # Проверить формат DATABASE_URL
   # Правильный формат: postgresql://user:password@host:port/database
   ```

2. **Перезапустить БД:**
   ```bash
   ./run.sh docker restart db
   
   # Дождаться готовности БД
   sleep 30
   ./run.sh config test-database
   ```

3. **Очистить данные БД (осторожно!):**
   ```bash
   ./run.sh docker down
   docker volume rm llm_router_postgres_data
   ./run.sh docker up -d db
   
   # Дождаться запуска и применить миграции
   sleep 30
   ./run.sh analytics migrate up
   ```

### Проблемы маршрутизации

#### Проблема: ML Predictor недоступен

**Симптомы:**
- Роутер использует fallback стратегию
- Ошибки "Predictor unavailable" в логах
- Медленная маршрутизация

**Диагностика:**
```bash
# Тест ML Predictor
./run.sh config test-predictor

# Проверка конфигурации
echo $PREDICT_URL
echo $PREDICT_TIMEOUT

# Проверка сетевого подключения
curl -f $PREDICT_URL/health || echo "Predictor недоступен"
```

**Решения:**

1. **Проверить URL и доступность:**
   ```bash
   # Проверить правильность URL
   curl -v $PREDICT_URL/health
   
   # Проверить DNS разрешение
   nslookup predictor-service
   ```

2. **Увеличить таймауты:**
   ```bash
   # В .env файле
   PREDICT_TIMEOUT=30
   PREDICT_RETRY_ATTEMPTS=5
   
   # Перезапустить сервис
   ./run.sh docker restart litellm
   ```

3. **Настроить fallback:**
   ```bash
   # В litellm.config.yaml
   router_settings:
     routing_strategy_args:
       fallback_strategy: "least-busy"
       fallback_timeout: 5
   ```

#### Проблема: GPUStack интеграция не работает

**Симптомы:**
- Ошибки аутентификации GPUStack
- Пустые данные о GPU
- Низкая эффективность кэша

**Диагностика:**
```bash
# Тест GPUStack интеграции
./run.sh config test-gpustack

# Проверка учетных данных
./run.sh gpustack credentials test

# Проверка статистики кэша
./run.sh debug cache
```

**Решения:**

1. **Проверить учетные данные:**
   ```bash
   # Проверить переменные окружения
   echo $GPUSTACK_URL
   echo $GPUSTACK_KEY
   echo $GPUSTACK_TOKEN
   
   # Тест прямого подключения
   curl -H "Authorization: Bearer $GPUSTACK_KEY" \
        $GPUSTACK_URL/api/v1/workers
   ```

2. **Обновить токены:**
   ```bash
   # Обновить GPUStack токен
   ./run.sh update gpustack token
   
   # Или обновить все настройки GPUStack
   ./run.sh update gpustack
   ```

3. **Очистить кэш:**
   ```bash
   # Очистить кэш GPUStack
   ./run.sh debug cache clear gpustack
   
   # Перезапустить с очисткой кэша
   ./run.sh docker restart litellm
   ```

### Проблемы производительности

#### Проблема: Медленные ответы

**Симптомы:**
- Высокое время ответа (>10 секунд)
- Таймауты запросов
- Жалобы пользователей на скорость

**Диагностика:**
```bash
# Мониторинг производительности
./run.sh monitor performance

# Проверка нагрузки
./run.sh monitor resources

# Анализ времени маршрутизации
./run.sh debug routing
```

**Решения:**

1. **Оптимизировать конфигурацию:**
   ```bash
   # Увеличить параллелизм
   MAX_CONCURRENT_REQUESTS=200
   
   # Оптимизировать кэширование
   CACHE_TTL_GPUSTACK=600
   CACHE_TTL_PREDICTIONS=60
   
   # Уменьшить вес ML предсказаний
   # В litellm.config.yaml:
   hybrid_weight: 0.2  # Больше веса least-busy
   ```

2. **Масштабировать ресурсы:**
   ```bash
   # Увеличить ресурсы БД
   ANALYTICS_DB_POOL_SIZE=20
   ANALYTICS_DB_MAX_OVERFLOW=40
   
   # Увеличить размер пула Redis
   REDIS_POOL_SIZE=20
   ```

3. **Оптимизировать аналитику:**
   ```bash
   # Увеличить размер батча
   ANALYTICS_BATCH_SIZE=500
   
   # Уменьшить частоту записи
   ANALYTICS_FLUSH_INTERVAL=60
   ```

#### Проблема: Высокое потребление памяти

**Симптомы:**
- OOM (Out of Memory) ошибки
- Медленная работа системы
- Частые перезапуски контейнеров

**Диагностика:**
```bash
# Мониторинг памяти
docker stats

# Проверка использования памяти
./run.sh monitor resources

# Анализ кэша
./run.sh debug cache stats
```

**Решения:**

1. **Оптимизировать кэш:**
   ```bash
   # Уменьшить размер кэша
   CACHE_MAX_SIZE=5000
   
   # Уменьшить TTL
   CACHE_TTL_GPUSTACK=300
   CACHE_TTL_PREDICTIONS=30
   ```

2. **Ограничить размер очереди:**
   ```bash
   # Уменьшить размер очереди аналитики
   ANALYTICS_MAX_QUEUE_SIZE=5000
   
   # Увеличить частоту сброса
   ANALYTICS_FLUSH_INTERVAL=15
   ```

3. **Увеличить лимиты Docker:**
   ```yaml
   # В docker-compose.yml
   services:
     litellm:
       deploy:
         resources:
           limits:
             memory: 2G
           reservations:
             memory: 1G
   ```

### Проблемы аналитики

#### Проблема: Данные не записываются в БД

**Симптомы:**
- Пустые таблицы аналитики
- Ошибки в логах аналитики
- Переполнение очереди

**Диагностика:**
```bash
# Проверка здоровья аналитики
./run.sh analytics health check

# Проверка очереди
./run.sh debug analytics queue

# Проверка подключения к БД
./run.sh config test-database
```

**Решения:**

1. **Проверить настройки аналитики:**
   ```bash
   # Убедиться, что аналитика включена
   echo $ANALYTICS_ENABLED  # должно быть true
   
   # Проверить настройки батчинга
   echo $ANALYTICS_BATCH_SIZE
   echo $ANALYTICS_FLUSH_INTERVAL
   ```

2. **Перезапустить обработчик аналитики:**
   ```bash
   # Очистить очередь
   ./run.sh analytics queue clear
   
   # Перезапустить обработчик
   ./run.sh analytics queue restart
   ```

3. **Проверить миграции:**
   ```bash
   # Проверить статус миграций
   ./run.sh analytics migrate status
   
   # Применить недостающие миграции
   ./run.sh analytics migrate up
   ```

## Диагностика системы

### Команды диагностики

#### Общая диагностика

```bash
# Полная диагностика системы
./run.sh debug status

# Проверка всех компонентов
./run.sh debug validate

# Проверка сетевых соединений
./run.sh debug connectivity
```

#### Диагностика производительности

```bash
# Анализ производительности кэша
./run.sh debug cache

# Мониторинг least-busy tracking
./run.sh debug least-busy

# Отладка решений маршрутизации
./run.sh debug routing
```

#### Диагностика логов

```bash
# Детальные логи отладки
./run.sh debug logs

# Трассировка запросов
./run.sh debug trace

# Метрики производительности
./run.sh debug metrics
```

### Анализ логов

#### Структура логов

```bash
# Основные файлы логов
logs/
├── litellm/
│   ├── system.log          # Общие логи системы
│   ├── routing.log         # Решения маршрутизации
│   ├── error.log          # Ошибки
│   └── performance.log    # Метрики производительности
├── analytics/
│   ├── database.log       # Операции с БД
│   └── queue.log         # Очередь обработки
└── integrations/
    ├── predictor.log     # ML Predictor
    ├── gpustack.log      # GPUStack
    └── cache.log         # Кэширование
```

#### Полезные команды для анализа логов

```bash
# Поиск ошибок за последний час
./run.sh docker logs litellm --since 1h | grep ERROR

# Анализ решений маршрутизации
./run.sh docker logs litellm | grep "routing_decision"

# Поиск медленных запросов
./run.sh docker logs litellm | grep "response_time" | awk '$NF > 5'

# Статистика ошибок
./run.sh docker logs litellm | grep ERROR | cut -d' ' -f4 | sort | uniq -c

# Мониторинг в реальном времени
./run.sh docker logs -f litellm | grep -E "(ERROR|WARNING|routing_decision)"
```

### Мониторинг системы

#### Health checks

```bash
# Проверка всех health endpoints
curl http://localhost:4000/health
curl http://localhost:4000/health/readiness  
curl http://localhost:4000/health/liveness

# Проверка компонентов
./run.sh monitor health
./run.sh monitor routing
./run.sh monitor performance
```

#### Метрики Prometheus

```bash
# Доступ к метрикам Prometheus
curl http://localhost:9090/metrics

# Основные метрики для мониторинга:
# - llm_router_requests_total
# - llm_router_request_duration_seconds
# - llm_router_routing_decisions_total
# - llm_router_ml_prediction_accuracy
# - llm_router_cache_hit_rate
```

## Проблемы производительности

### Оптимизация конфигурации

#### Настройки для высокой нагрузки

```bash
# .env для высокой нагрузки
MAX_CONCURRENT_REQUESTS=500
ANALYTICS_BATCH_SIZE=1000
ANALYTICS_FLUSH_INTERVAL=10
CACHE_TTL_GPUSTACK=900
CACHE_TTL_PREDICTIONS=60
ANALYTICS_DB_POOL_SIZE=30
REDIS_POOL_SIZE=25
```

#### Настройки для экономии ресурсов

```bash
# .env для экономии ресурсов
MAX_CONCURRENT_REQUESTS=50
ANALYTICS_BATCH_SIZE=50
ANALYTICS_FLUSH_INTERVAL=120
CACHE_TTL_GPUSTACK=300
CACHE_TTL_PREDICTIONS=30
ANALYTICS_DB_POOL_SIZE=5
REDIS_POOL_SIZE=5
```

### Профилирование производительности

#### Измерение времени ответа

```python
# Скрипт для измерения производительности
import time
import asyncio
import aiohttp

async def measure_response_time(url, data, headers):
    start_time = time.time()
    
    async with aiohttp.ClientSession() as session:
        async with session.post(url, json=data, headers=headers) as response:
            result = await response.json()
            
    end_time = time.time()
    response_time = end_time - start_time
    
    return {
        'response_time': response_time,
        'status': response.status,
        'selected_deployment': result.get('x-llm-router', {}).get('selected_deployment'),
        'routing_time': result.get('x-llm-router', {}).get('decision_time_ms', 0) / 1000
    }

# Запуск измерений
async def run_performance_test():
    url = 'http://localhost:4000/v1/chat/completions'
    headers = {'Authorization': 'Bearer sk-test-key'}
    data = {
        'model': 'chat_instruct',
        'messages': [{'role': 'user', 'content': 'Test message'}],
        'max_tokens': 100
    }
    
    results = []
    for i in range(100):
        result = await measure_response_time(url, data, headers)
        results.append(result)
        print(f"Request {i+1}: {result['response_time']:.3f}s")
    
    # Анализ результатов
    avg_time = sum(r['response_time'] for r in results) / len(results)
    avg_routing_time = sum(r['routing_time'] for r in results) / len(results)
    
    print(f"\nСредние результаты:")
    print(f"Общее время ответа: {avg_time:.3f}s")
    print(f"Время маршрутизации: {avg_routing_time:.3f}s")

if __name__ == "__main__":
    asyncio.run(run_performance_test())
```

## Восстановление после сбоев

### Процедуры восстановления

#### Восстановление после полного сбоя

```bash
#!/bin/bash
# recovery.sh - скрипт восстановления системы

echo "=== Начало процедуры восстановления ==="

# 1. Остановка всех сервисов
echo "1. Остановка сервисов..."
./run.sh docker down

# 2. Проверка и очистка ресурсов
echo "2. Очистка ресурсов..."
docker system prune -f
docker volume prune -f

# 3. Проверка конфигурации
echo "3. Проверка конфигурации..."
./run.sh config validate || {
    echo "Ошибка конфигурации! Проверьте .env и litellm.config.yaml"
    exit 1
}

# 4. Восстановление из резервной копии (если нужно)
if [ "$1" = "--restore-backup" ]; then
    echo "4. Восстановление из резервной копии..."
    LATEST_BACKUP=$(ls -t /backups/llm_router/*/config_backup.tar.gz | head -1)
    if [ -n "$LATEST_BACKUP" ]; then
        ./run.sh config restore "$LATEST_BACKUP"
    fi
fi

# 5. Запуск сервисов
echo "5. Запуск сервисов..."
./run.sh docker up -d --build

# 6. Ожидание готовности
echo "6. Ожидание готовности сервисов..."
sleep 60

# 7. Применение миграций
echo "7. Применение миграций БД..."
./run.sh analytics migrate up

# 8. Проверка работоспособности
echo "8. Проверка работоспособности..."
./run.sh test endpoint || {
    echo "Система не готова! Проверьте логи."
    ./run.sh docker logs litellm
    exit 1
}

echo "✓ Восстановление завершено успешно"
```

#### Восстановление базы данных

```bash
#!/bin/bash
# db-recovery.sh - восстановление базы данных

echo "=== Восстановление базы данных ==="

# 1. Создание резервной копии текущего состояния
echo "1. Создание резервной копии..."
./run.sh analytics backup create emergency_backup

# 2. Остановка приложения (но не БД)
echo "2. Остановка приложения..."
docker stop llm_router_litellm_1

# 3. Восстановление из резервной копии
if [ -n "$1" ]; then
    echo "3. Восстановление из $1..."
    ./run.sh analytics backup restore "$1"
else
    echo "3. Применение миграций..."
    ./run.sh analytics migrate up
fi

# 4. Проверка целостности БД
echo "4. Проверка целостности..."
./run.sh analytics database check-integrity

# 5. Перестроение индексов
echo "5. Перестроение индексов..."
./run.sh analytics database rebuild-indexes

# 6. Запуск приложения
echo "6. Запуск приложения..."
docker start llm_router_litellm_1

# 7. Проверка подключения
echo "7. Проверка подключения к БД..."
sleep 30
./run.sh config test-database

echo "✓ Восстановление БД завершено"
```

### Мониторинг восстановления

```bash
# Мониторинг процесса восстановления
watch -n 5 './run.sh docker status'

# Мониторинг логов в реальном времени
./run.sh docker logs -f litellm | tee recovery.log

# Проверка готовности сервисов
while ! curl -f http://localhost:4000/health; do
    echo "Ожидание готовности..."
    sleep 10
done
echo "Система готова!"
```

## Контакты и поддержка

### Сбор информации для поддержки

При обращении в поддержку соберите следующую информацию:

```bash
#!/bin/bash
# support-info.sh - сбор информации для поддержки

SUPPORT_DIR="support_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$SUPPORT_DIR"

echo "Сбор информации для поддержки в $SUPPORT_DIR/"

# Системная информация
echo "=== Системная информация ===" > "$SUPPORT_DIR/system_info.txt"
uname -a >> "$SUPPORT_DIR/system_info.txt"
docker --version >> "$SUPPORT_DIR/system_info.txt"
docker-compose --version >> "$SUPPORT_DIR/system_info.txt"

# Конфигурация
echo "=== Конфигурация ===" > "$SUPPORT_DIR/config.txt"
./run.sh config validate >> "$SUPPORT_DIR/config.txt" 2>&1

# Статус сервисов
echo "=== Статус сервисов ===" > "$SUPPORT_DIR/services.txt"
./run.sh docker status >> "$SUPPORT_DIR/services.txt"

# Логи (последние 1000 строк)
echo "Сбор логов..."
./run.sh docker logs litellm --tail 1000 > "$SUPPORT_DIR/litellm.log"
./run.sh docker logs db --tail 1000 > "$SUPPORT_DIR/database.log"

# Диагностика
echo "=== Диагностика ===" > "$SUPPORT_DIR/diagnostics.txt"
./run.sh debug status >> "$SUPPORT_DIR/diagnostics.txt" 2>&1

# Тесты
echo "=== Результаты тестов ===" > "$SUPPORT_DIR/tests.txt"
./run.sh test endpoint >> "$SUPPORT_DIR/tests.txt" 2>&1

# Создание архива
tar -czf "${SUPPORT_DIR}.tar.gz" "$SUPPORT_DIR"
rm -rf "$SUPPORT_DIR"

echo "Информация собрана в ${SUPPORT_DIR}.tar.gz"
echo "Отправьте этот файл в службу поддержки"
```

### Уровни поддержки

1. **Самостоятельное решение**
   - Проверьте данное руководство
   - Используйте команды диагностики
   - Проверьте логи системы

2. **Документация и FAQ**
   - [Руководство по эксплуатации](OPERATIONS_GUIDE.md)
   - [Справочник API](API_REFERENCE.md)
   - [Руководство по аналитике](ANALYTICS_GUIDE.md)

3. **Техническая поддержка**
   - Соберите информацию с помощью `support-info.sh`
   - Опишите проблему и шаги для воспроизведения
   - Приложите файлы логов и конфигурации

---

*Следующий раздел: [Руководство по миграции](MIGRATION_GUIDE.md)*