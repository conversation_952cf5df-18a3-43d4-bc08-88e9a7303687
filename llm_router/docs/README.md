# LLM Router - Документация

Добро пожаловать в документацию LLM Router - интеллектуальной системы маршрутизации для LiteLLM с машинным обучением и гибридной балансировкой нагрузки.

## 🚀 Быстрый старт

Для новых разработчиков рекомендуем начать с:
- **[Руководство по началу работы](GETTING_STARTED.md)** - пошаговое введение в проект
- **[Архитектура системы](ARCHITECTURE.md)** - понимание структуры проекта

## 📚 Основная документация

### Для разработчиков
- **[Руководство по началу работы](GETTING_STARTED.md)** - введение в проект, быстрая настройка и основные концепции
- **[Архитектура системы](ARCHITECTURE.md)** - полная архитектура системы, компоненты и потоки данных
- **[Руководство по тестированию](TESTING_GUIDE.md)** - стратегии тестирования, E2E тесты и валидация

### Для администраторов
- **[Руководство по эксплуатации](OPERATIONS_GUIDE.md)** - развертывание, конфигурация, команды и обслуживание
- **[Руководство по устранению неполадок](TROUBLESHOOTING.md)** - диагностика и решение проблем

### Специализированные руководства
- **[Руководство по аналитике](ANALYTICS_GUIDE.md)** - система аналитики, миграции БД и отчеты
- **[Справочник API](API_REFERENCE.md)** - документация API и технические справочники
- **[Руководство по миграции](MIGRATION_GUIDE.md)** - процедуры обновления и миграции системы

## 🎯 Навигация по типу задач

### Первое знакомство с проектом
1. [Обзор проекта](GETTING_STARTED.md#обзор-проекта) - что такое LLM Router
2. [Быстрая настройка](GETTING_STARTED.md#быстрая-настройка) - запуск за 5 минут
3. [Основные концепции](GETTING_STARTED.md#основные-концепции) - ключевые понятия

### Разработка и тестирование
1. [Архитектура кодовой базы](ARCHITECTURE.md#архитектура-кодовой-базы) - структура проекта
2. [Стратегии тестирования](TESTING_GUIDE.md#стратегии-тестирования) - подходы к тестированию
3. [E2E тестирование](TESTING_GUIDE.md#e2e-тестирование) - комплексное тестирование

### Развертывание и эксплуатация
1. [Установка и настройка](OPERATIONS_GUIDE.md#установка-и-настройка) - развертывание системы
2. [Управление конфигурацией](OPERATIONS_GUIDE.md#управление-конфигурацией) - настройка параметров
3. [Мониторинг и обслуживание](OPERATIONS_GUIDE.md#мониторинг-и-обслуживание) - поддержка системы

### Работа с аналитикой
1. [Настройка аналитики](ANALYTICS_GUIDE.md#настройка-аналитики) - подключение системы аналитики
2. [Миграции базы данных](ANALYTICS_GUIDE.md#миграции-базы-данных) - управление схемой БД
3. [Отчеты и запросы](ANALYTICS_GUIDE.md#отчеты-и-запросы) - работа с данными

## 🔧 Быстрые ссылки

### Команды
- [Справочник команд](OPERATIONS_GUIDE.md#справочник-команд) - полный список команд run.sh
- [Команды Docker](OPERATIONS_GUIDE.md#команды-docker) - управление контейнерами
- [Команды тестирования](TESTING_GUIDE.md#команды-тестирования) - запуск тестов

### API и интеграции
- [LiteLLM API](API_REFERENCE.md#litellm-api) - интеграция с LiteLLM
- [GPUStack интеграция](API_REFERENCE.md#gpustack-интеграция) - работа с GPUStack
- [ML Predictor API](API_REFERENCE.md#ml-predictor-api) - сервис предсказаний

### Устранение неполадок
- [Частые проблемы](TROUBLESHOOTING.md#частые-проблемы) - типичные ошибки и решения
- [Диагностика системы](TROUBLESHOOTING.md#диагностика-системы) - инструменты отладки
- [Проблемы производительности](TROUBLESHOOTING.md#проблемы-производительности) - оптимизация

## 📖 Дополнительная информация

### Архитектурные решения
- [Гибридная балансировка нагрузки](ARCHITECTURE.md#гибридная-балансировка-нагрузки)
- [Интеграция машинного обучения](ARCHITECTURE.md#интеграция-машинного-обучения)
- [Система кэширования](ARCHITECTURE.md#система-кэширования)

### Лучшие практики
- [Конфигурация для продакшена](OPERATIONS_GUIDE.md#конфигурация-для-продакшена)
- [Мониторинг производительности](OPERATIONS_GUIDE.md#мониторинг-производительности)
- [Безопасность и аутентификация](OPERATIONS_GUIDE.md#безопасность-и-аутентификация)

## 🤝 Участие в разработке

Для участия в разработке проекта:
1. Изучите [архитектуру системы](ARCHITECTURE.md)
2. Ознакомьтесь с [руководством по тестированию](TESTING_GUIDE.md)
3. Следуйте стандартам, описанным в [руководстве по эксплуатации](OPERATIONS_GUIDE.md)

## 📝 История изменений

Документация регулярно обновляется. Основные изменения:
- Консолидация 24 файлов документации в 9 структурированных руководств
- Перевод всей документации на русский язык
- Улучшение навигации и поиска информации
- Стандартизация форматирования и стиля

---

*Последнее обновление: $(date)*
*Версия документации: 2.0*