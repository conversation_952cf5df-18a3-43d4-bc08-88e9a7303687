# Руководство по тестированию LLM Router

Комплексное руководство по тестированию системы LLM Router, включающее быструю валидацию, всестороннее тестирование и процедуры E2E тестирования.

## Обзор системы тестирования

### Архитектура тестирования

```mermaid
graph TB
    subgraph "Пирамида тестирования LLM Router"
        E2E[E2E Tests<br/>Полные сценарии использования]
        Integration[Integration Tests<br/>Тестирование интеграций]
        Unit[Unit Tests<br/>Тестирование компонентов]
        Static[Static Analysis<br/>Линтинг и типизация]
    end
    
    Static --> Unit
    Unit --> Integration
    Integration --> E2E
    
    style E2E fill:#fce4ec
    style Integration fill:#fff3e0
    style Unit fill:#e8f5e8
    style Static fill:#e1f5fe
```

### Типы тестирования

1. **Быстрая валидация** - экспресс-проверка работоспособности (5 минут)
2. **Модульные тесты** - тестирование отдельных компонентов
3. **Интеграционные тесты** - тестирование взаимодействия компонентов
4. **E2E тесты** - тестирование полных пользовательских сценариев
5. **Нагрузочные тесты** - тестирование производительности под нагрузкой
6. **Тесты отказоустойчивости** - проверка работы при сбоях компонентов

## Быстрая валидация (5 минут)

### ⚡ Экспресс-проверка с новыми Quick-Check командами

```bash
# Быстрая проверка всех компонентов (2-3 минуты)
./run.sh test quick-check all

# Проверка только аналитики и отчетов (1-2 минуты)
./run.sh test quick-check analytics

# Проверка всех отчетов аналитики (2 минуты)
./run.sh test quick-check reports

# Детальная проверка с подтестами (5-10 минут)
./run.sh test quick-check --verbose all

# С показом ошибок для диагностики
./run.sh test quick-check --show-errors analytics
```
**Ожидается:** ✅ РЕЗУЛЬТАТ: PASSED для всех категорий

### 🔧 Пошаговая экспресс-проверка (классический метод)

#### 1. Статус системы
```bash
./run.sh docker status
```
**Ожидается:** Все сервисы UP и HEALTHY

#### 2. E2E тест аналитики
```bash
./run.sh analytics test e2e
```
**Ожидается:** ✅ E2E TEST PASSED - данные записываются в БД

#### 3. Тест роутинга
```bash
./run.sh test endpoint
```
**Ожидается:** 200 OK responses от всех моделей

#### 4. Предиктор
```bash
docker exec -ti filin-litellm-router python3 -m llm_router.tests.predictor.test_predictor_service
```
**Ожидается:** Predictor service is healthy

#### 5. Данные в БД
```bash
./run.sh analytics data counts
```
**Ожидается:** Records > 0 в основных таблицах

### 🎯 Критерии успеха быстрой валидации

#### ✅ Обязательное (MUST PASS)
1. Docker сервисы запущены
2. E2E аналитика проходит
3. Роутер отвечает на запросы
4. Предиктор генерирует предсказания
5. Данные записываются в БД

#### 🔥 Желательное (SHOULD PASS)  
1. Least-busy распределяет нагрузку
2. GPUStack данные собираются
3. VRAM показывает реальные значения
4. Стресс-тесты проходят

### 🚨 Красные флаги

#### ❌ Критические ошибки
- Docker сервисы не запускаются
- E2E тест аналитики фейлится
- Предиктор недоступен
- 500 ошибки при запросах к роутеру

#### ⚠️ Предупреждения
- VRAM = 0.0GB в аналитике (проблема GPUStack)
- Least-busy счетчики не обновляются
- Высокое время ответа (>5 сек)
- Ошибки в логах Docker

### 🛠️ Быстрая диагностика

#### Аналитика не работает:
```bash
./run.sh analytics health check
./run.sh docker logs litellm | grep -i analytics
```

#### Предиктор недоступен:
```bash
./run.sh debug predictor
docker exec -ti filin-litellm-router python3 -m llm_router.tests.predictor.test_predictor_service
```

#### Роутер не отвечает:
```bash
./run.sh debug status
./run.sh docker logs litellm | tail -50
```

#### GPUStack не работает:
```bash
docker exec -ti filin-litellm-router python3 -m llm_router.tests.gpustack.test_gpustack_health
```

## Всестороннее тестирование (30-60 минут)

### 🧩 Тесты по компонентам

#### 📊 Analytics & Database
| Команда | Описание | Время |
|---------|----------|-------|
| `./run.sh analytics test e2e` | E2E тест аналитики | 2 мин |
| `./run.sh analytics test sql` | SQL операции | 1 мин |
| `./run.sh analytics data consistency` | Консистентность данных | 30 сек |
| `./run.sh analytics health check` | Проверка здоровья БД | 15 сек |

#### 🧮 Predictor (ML Service)
| Команда | Описание | Время |
|---------|----------|-------|
| `docker exec -ti filin-litellm-router python3 -m llm_router.tests.predictor.test_predictor_service` | Базовый тест | 30 сек |
| `docker exec -ti filin-litellm-router python3 -m llm_router.tests.predictor.test_predictor_external` | Внешний API | 1 мин |
| `./run.sh debug predictor` | Отладка предиктора | 30 сек |

#### ⚖️ Load Balancing (Least-Busy)
| Команда | Описание | Время |
|---------|----------|-------|
| `docker exec -ti filin-litellm-router python3 -m llm_router.tests.unit.test_least_busy_integration` | Unit тесты | 1 мин |
| `docker exec -ti filin-litellm-router python3 -m llm_router.tests.load_balancing.test_load_balancing_real` | Реальная балансировка | 2 мин |
| `docker exec -ti filin-litellm-router python3 -m llm_router.tests.load_balancing.test_high_load_balancing` | Стресс-тест | 5 мин |

#### 🏗️ GPUStack Integration
| Команда | Описание | Время |
|---------|----------|-------|
| `docker exec -ti filin-litellm-router python3 -m llm_router.tests.gpustack.test_gpustack_health` | Проверка соединения | 30 сек |
| `docker exec -ti filin-litellm-router python3 -m llm_router.tests.gpustack.test_gpustack_api_integration` | API интеграция | 1 мин |
| `docker exec -ti filin-litellm-router python3 -m llm_router.tests.gpustack.test_gpustack_cache` | Кэширование | 1 мин |

#### 🚦 Routing
| Команда | Описание | Время |
|---------|----------|-------|
| `./run.sh test endpoint` | Базовый роутинг | 1 мин |
| `docker exec -ti filin-litellm-router python3 -m llm_router.tests.simple_test` | E2E роутинг | 3 мин |
| `docker exec -ti filin-litellm-router python3 -m llm_router.tests.unit.test_llm_router` | Unit тесты роутера | 2 мин |

#### 📊 Reports (Отчеты аналитики)
| Команда | Описание | Время |
|---------|----------|-------|
| `./run.sh analytics reports summary --format=table --limit=10` | Системная сводка | 30 сек |
| `./run.sh analytics reports decisions --format=table --limit=10` | Анализ решений роутера | 1 мин |
| `./run.sh analytics reports accuracy --format=table --limit=10` | Точность предсказаний | 1 мин |
| `./run.sh analytics reports performance --format=table --limit=10` | Анализ производительности | 1 мин |
| `./run.sh analytics reports quality --format=table --limit=10` | Качество роутинга | 1 мин |
| `./run.sh analytics reports tables models --format=table` | Статистика моделей | 30 сек |
| `./run.sh analytics reports tables deployments --format=table` | Статистика развертываний | 30 сек |
| `./run.sh analytics reports tables models --full --format=table` | Детальная статистика моделей | 1 мин |

### Модульные тесты

```bash
# Запуск всех модульных тестов
./run.sh test unit

# Тестирование конкретного модуля
./run.sh test unit --module router
./run.sh test unit --module analytics
./run.sh test unit --module gpustack

# Тестирование с покрытием кода
./run.sh test unit --coverage

# Тестирование в режиме отладки
./run.sh test unit --debug --verbose
```

### Интеграционные тесты

```bash
# Полные интеграционные тесты
./run.sh test integration

# Тестирование конкретной интеграции
./run.sh test integration --service predictor
./run.sh test integration --service gpustack
./run.sh test integration --service database

# Тестирование маршрутизации
./run.sh test routing

# Тестирование аналитики
./run.sh test analytics
```

## E2E тестирование аналитики

### Обзор E2E системы

End-to-End (E2E) тестирование аналитики представляет собой комплексную систему проверки функциональности всего пайплайна аналитики LLM-Router от HTTP-запроса до записи данных в базу данных. Система предназначена для обеспечения надежности, производительности и устойчивости к отказам аналитической инфраструктуры.

### Архитектура E2E тестирования

#### Компоненты системы

1. **Основные модули**
   - **Tester** (`analytics/utils/e2e/tester.py`) - главный движок тестирования
   - **CLI Handler** (`analytics/utils/e2e/cli.py`) - обработка аргументов командной строки
   - **Commands** (`analytics/utils/e2e/commands/`) - реализация команд через Command Pattern
   - **Validation Engine** (`analytics/utils/e2e/validation/`) - 4-уровневая система валидации
   - **HTTP Client** (`analytics/utils/e2e/http/`) - специализированный клиент для LiteLLM

2. **Конфигурация и модели данных**
   - **TestConfig** - типизированная конфигурация тестов
   - **TestThresholds** - настраиваемые пороговые значения
   - **TestResult** - структура результатов тестирования
   - **Константы** - настройки по умолчанию и параметры

3. **Паттерны проектирования**
   - **Command Pattern** - для расширяемой системы команд
   - **Factory Pattern** - для создания команд через реестр
   - **Strategy Pattern** - для различных сценариев валидации
   - **Dependency Injection** - для гибкой конфигурации

### Доступные команды E2E тестирования

#### 1. Полный E2E тест

```bash
./run.sh analytics test e2e
```

**Описание**: Запускает комплексный тест всего пайплайна аналитики

**Процесс выполнения**:
1. Отправка HTTP-запроса через LiteLLM роутер
2. Ожидание появления данных в базе аналитики (с таймаутом)
3. 4-уровневая валидация данных (CRITICAL, BUSINESS, QUALITY, TIMING)
4. Проверка производительности (analytics lag detection)
5. Формирование детального отчета

**Примеры использования**:
```bash
# Базовый запуск с настройками по умолчанию
./run.sh analytics test e2e

# С кастомными параметрами (если поддерживается)
./run.sh analytics test e2e --timeout 60 --max-lag 10
```

#### 2. Тесты сценариев отказов

##### 2.1 Тест отказа предиктора
```bash
./run.sh analytics test failure --scenario=predictor-down
```

**Назначение**: Проверяет устойчивость роутера при недоступности ML-предиктора

**Сценарий**:
- Имитирует недоступность сервиса предиктора
- Проверяет переключение на fallback-логику
- Убеждается, что роутер продолжает обслуживать запросы
- Проверяет корректность аналитических данных в режиме fallback

##### 2.2 Тест ошибок LLM
```bash
./run.sh analytics test failure --scenario=llm-error
```

**Назначение**: Проверяет обработку ошибок от языковых моделей

**Сценарий**:
- Отправляет запросы, вызывающие ошибки LLM API
- Проверяет корректную обработку ошибок роутером
- Убеждается в правильном логировании ошибок
- Проверяет запись ошибок в аналитику

##### 2.3 Тест таймаутов
```bash
./run.sh analytics test failure --scenario=timeout
```

**Назначение**: Проверяет обработку превышения времени ожидания

**Сценарий**:
- Имитирует сценарии медленных ответов моделей
- Проверяет корректную обработку таймаутов
- Убеждается в правильном логировании таймаутов
- Проверяет влияние на аналитические данные

##### 2.4 Критический тест отказа БД
```bash
./run.sh analytics test failure --scenario=db-failure
```

**Назначение**: Проверяет критически важную устойчивость к отказу БД аналитики

**Сценарий**:
- Останавливает базу данных аналитики (Docker-контейнер)
- Проверяет, что основной роутер остается функциональным
- Отправляет тестовые запросы для проверки работоспособности
- Перезапускает БД и проверяет восстановление
- **Принцип**: аналитика НЕ должна влиять на работу роутера

### 4-уровневая система валидации

#### CRITICAL уровень
**Цель**: Проверка критически важных аспектов

**Проверки**:
- Наличие записи в базе данных аналитики
- Корректность request_id
- Наличие обязательных полей (model, response_time, etc.)
- Базовая структурная целостность данных

**Критерии успеха**: Все проверки должны пройти

#### BUSINESS уровень  
**Цель**: Проверка бизнес-логики и функциональности

**Проверки**:
- Корректность выбранной модели
- Правильность timestamps (request_time, response_time)
- Валидность статусов ответов
- Соответствие данных запроса и ответа

**Критерии успеха**: Все бизнес-правила соблюдены

#### QUALITY уровень
**Цель**: Проверка качества данных и ML предсказаний

**Проверки**:
- Точность ML предсказаний (если доступны)
- Качество scoring данных
- Полнота метаданных запроса
- Корректность enrichment данных (GPUStack, least-busy)

**Критерии успеха**: Данные соответствуют стандартам качества

#### TIMING уровень
**Цель**: Проверка производительности и временных характеристик

**Проверки**:
- Analytics lag (время от запроса до появления в БД)
- Response time в разумных пределах
- Производительность запросов к БД
- Отсутствие значительных задержек в пайплайне

**Критерии успеха**: Временные показатели в пределах SLA

### Конфигурация E2E тестов

#### Переменные окружения

```bash
# Основные параметры подключения
API_URL=http://localhost:4000           # URL LiteLLM роутера
DATABASE_URL=postgresql://user:pass@localhost:5432/analytics  # БД аналитики
LITELLM_MASTER_KEY=sk-1234              # Ключ для аутентификации

# Настройки таймаутов
E2E_TIMEOUT=60                          # Общий таймаут теста
E2E_REQUEST_TIMEOUT=30                  # Таймаут HTTP запроса
E2E_MAX_ANALYTICS_LAG=15                # Максимальная задержка аналитики

# Настройки валидации
E2E_MAX_WAIT_FOR_DATA=45                # Время ожидания данных в БД
E2E_POLLING_INTERVAL=2                  # Интервал опроса БД
```

#### Настройки по умолчанию

```python
# Тестовые параметры
DEFAULT_TEST_MODEL = "gpt-3.5-turbo"
DEFAULT_TEST_PROMPT = "Тестовый запрос для E2E проверки аналитики"

# Пороговые значения
class TestThresholds:
    MAX_RESPONSE_TIME = 120.0           # Максимальное время ответа
    MAX_ANALYTICS_LAG = 15.0            # Максимальная задержка аналитики
    MIN_PREDICTION_ACCURACY = 0.8       # Минимальная точность предсказаний
    MAX_DB_QUERY_TIME = 5.0             # Максимальное время запроса к БД
```

### Интерпретация результатов E2E тестов

#### Успешный тест
```
INFO: E2E testing completed successfully
Exit code: 0

# В логах:
E2E: HTTP request successful - waiting for analytics data  
E2E: Analytics data found - starting validation
VALIDATION: CRITICAL level - PASSED (4/4 checks)
VALIDATION: BUSINESS level - PASSED (3/3 checks)  
VALIDATION: QUALITY level - PASSED (2/2 checks)
VALIDATION: TIMING level - PASSED (analytics lag: 2.3s)
E2E: All validation levels passed - test successful
```

#### Частично успешный тест
```
WARNING: Some validation levels failed
Exit code: 1

# В логах:
VALIDATION: CRITICAL level - PASSED (4/4 checks)
VALIDATION: BUSINESS level - PASSED (3/3 checks)
VALIDATION: QUALITY level - FAILED (1/2 checks failed)
  - ML prediction accuracy below threshold: 0.65 < 0.8
VALIDATION: TIMING level - PASSED (analytics lag: 3.1s)
E2E: 3/4 validation levels passed - partial success
```

#### Неуспешный тест
```
ERROR: E2E testing failed - check logs for details
Exit code: 2

# В логах:
E2E: HTTP request successful - waiting for analytics data
E2E: Analytics data not found after 45s
ERROR: Analytics pipeline failure - data not recorded
```

### Docker архитектура для E2E тестов

#### Синтаксис команд

```bash
python3 e2e_tester.py [COMMAND] \
  --api_url <ROUTER_URL> \
  --database_url <DATABASE_URL> \
  [--api_key <AUTH_KEY>] \
  [OPTIONS]
```

**Обязательные параметры:**
- `--api_url` - URL LiteLLM router для HTTP запросов
- `--database_url` - PostgreSQL connection string для доступа к аналитике

**Опциональные параметры:**
- `--api_key` - API ключ для аутентификации
- `--timeout SECONDS` - Таймаут тестов (по умолчанию: 120)
- `--max-lag SECONDS` - Максимальная задержка аналитики (по умолчанию: 5)

#### Примеры использования

##### Docker окружение с аутентификацией
```bash
# Полная конфигурация для Docker
python3 e2e_tester.py run \
  --api_url http://host.docker.internal:4000 \
  --database_url ****************************************/litellm \
  --api_key sk-1234567890abcdef
```

##### Локальная разработка
```bash
# URLs для разработки на хост машине
python3 e2e_tester.py run \
  --api_url http://localhost:4000 \
  --database_url postgresql://llmproxy:dbpassword@localhost:5432/litellm \
  --api_key sk-dev-key
```

##### Продакшн окружение
```bash
# URLs для продакшна
python3 e2e_tester.py run \
  --api_url https://api.company.com \
  --database_url ***********************************/analytics \
  --api_key sk-prod-key
```

### Типичные проблемы и решения

#### 1. Analytics data not found
**Причина**: Данные не появляются в БД аналитики в течение ожидаемого времени

**Диагностика**:
```bash
# Проверить статус миграций
./run.sh analytics migrate status

# Проверить подключение к БД
./run.sh docker logs db

# Проверить логи роутера
./run.sh docker logs litellm | grep -i analytics
```

**Решение**:
```bash
# Применить миграции если нужно
./run.sh analytics migrate deploy

# Перезапустить сервисы
./run.sh docker restart
```

#### 2. HTTP request failed
**Причина**: Роутер недоступен или не отвечает

**Диагностика**:
```bash
# Проверить статус сервисов
./run.sh docker status

# Проверить health endpoint
curl http://localhost:4000/health

# Проверить логи роутера
./run.sh docker logs litellm
```

#### 3. Validation failures
**Причина**: Данные не соответствуют ожидаемым критериям

**Диагностика**:
```bash
# Посмотреть детали валидации в логах
./run.sh docker logs litellm | grep -i validation

# Проверить данные в БД напрямую
./run.sh analytics database connect
SELECT * FROM analytics_requests ORDER BY created_at DESC LIMIT 5;
```

## Рабочие процессы тестирования

### 🔄 Типичные сценарии использования

#### 🔧 После рефакторинга
1. **Быстрая проверка:** Экспресс-валидация (5 мин)
2. **Если есть проблемы:** Диагностика из раздела быстрой валидации
3. **Полная валидация:** Всестороннее тестирование (30-60 мин)

#### 🚀 Перед деплоем в продакшн
1. **Всестороннее тестирование** - полные тесты
2. **Еженедельная проверка** из быстрой валидации
3. **Мониторинг:** `./run.sh docker logs -f`

#### 🐛 При возникновении проблем
1. **Диагностика** из быстрой валидации
2. **Детальная отладка** из всестороннего тестирования
3. **Specific component tests** из таблиц компонентов

#### 📈 Регулярный мониторинг
- **Ежедневно:** 5 команд из быстрой валидации
- **Еженедельно:** Комплексные тесты из всестороннего тестирования
- **Ежемесячно:** Полная валидация системы

### 🚨 Экстренная диагностика

#### Система не отвечает
```bash
./run.sh docker status
./run.sh docker logs litellm | tail -50
```

#### Аналитика не работает
```bash
./run.sh analytics health check
./run.sh analytics migrate status
```

#### Роутер выбирает неправильно
```bash
./run.sh debug router
./run.sh analytics data query --query "SELECT deployment_id, selection_method FROM \"LiteLLM_RouterAnalytics\" ORDER BY created_at DESC LIMIT 10"
```

#### Предиктор недоступен
```bash
docker exec -ti filin-litellm-router python3 -m llm_router.tests.predictor.test_predictor_service
./run.sh debug predictor
```

### 📅 Регулярные проверки

#### ⚡ Ежедневная проверка (новый быстрый способ)
```bash
# 1 команда, 2-3 минуты
./run.sh test quick-check all
```
**Если результат ✅ PASSED - система работает корректно!**

#### 📊 Плюс проверка отчетов (дополнительно 2 минуты)
```bash
./run.sh test quick-check reports
```

#### 🔧 Классический способ (5 команд, 5 минут)

```bash
# 1. Статус (30 сек)
./run.sh docker status

# 2. E2E тест (2 мин)  
./run.sh analytics test e2e

# 3. Роутер (1 мин)
./run.sh test endpoint

# 4. Данные (30 сек)
./run.sh analytics data counts

# 5. Предиктор (30 сек)
docker exec -ti filin-litellm-router python3 -m llm_router.tests.predictor.test_predictor_service
```

#### 📈 Еженедельная проверка

##### ⚡ Новый способ (детальная проверка всех компонентов)
```bash
# Детальная проверка всех компонентов (10-15 минут)
./run.sh test quick-check --verbose all

# Проверка с показом ошибок (для диагностики)
./run.sh test quick-check --show-errors all
```

##### 🔧 Классический способ (комплексные тесты)
```bash
# Комплексные тесты (один раз в неделю)
./run.sh testing router all
./run.sh analytics test failure --scenario=predictor-down
docker exec -ti filin-litellm-router python3 -m llm_router.tests.load_balancing.test_high_load_balancing

# Дополнительно: полная проверка отчетов с деталями
./run.sh test quick-check --verbose reports
```

### Нагрузочное тестирование

```bash
# Базовый нагрузочный тест
./run.sh test load

# Нагрузочный тест с параметрами
./run.sh test load --requests 1000 --concurrent 50 --duration 300

# Тест конкурентных запросов
./run.sh test concurrent --threads 20 --requests-per-thread 100

# Стресс-тестирование
./run.sh test stress --ramp-up 60 --max-users 200

# Параметризуемые тесты
docker exec -ti filin-litellm-router python3 -m llm_router.tests.test_true_concurrent --max-tokens 50 --stagger 0.01 --requests 30

# High Load Balancing (через run.sh)
./run.sh testing router high-load 25 6  # 25 запросов, 6 concurrent
./run.sh testing router high-load 50 10 --delay 0.1
```

## Интеграция в CI/CD

### GitHub Actions пример для E2E тестов

```yaml
name: E2E Analytics Testing

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  e2e-tests:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Start services
      run: |
        cd llm-router
        ./run.sh docker up
        
    - name: Wait for services
      run: sleep 30
      
    - name: Run E2E tests
      run: |
        cd llm-router
        ./run.sh analytics test e2e
        
    - name: Run failure tests
      run: |
        cd llm-router  
        ./run.sh analytics test failure --scenario=timeout
        ./run.sh analytics test failure --scenario=db-failure
        
    - name: Cleanup
      if: always()
      run: |
        cd llm-router
        ./run.sh docker down
```

### Мониторинг производственной среды

```bash
# Ежедневная проверка (cron job)
0 2 * * * cd /opt/llm-router && ./run.sh analytics test e2e >> /var/log/e2e-tests.log 2>&1

# Еженедельная проверка отказоустойчивости
0 3 * * 0 cd /opt/llm-router && ./run.sh analytics test failure --scenario=db-failure >> /var/log/failure-tests.log 2>&1
```

### Docker Compose пример
```yaml
environment:
  - LITELLM_API_BASE=http://litellm:4000
  - DATABASE_URL=************************************/litellm
  - LITELLM_MASTER_KEY=sk-docker-test-key
command: ["./run.sh", "analytics", "test", "e2e"]
```

## Справочная информация

### Архитектура тестов
```
tests/
├── unit/              # Unit тесты отдельных компонентов
├── integration/       # Интеграционные тесты
├── load_balancing/    # Тесты балансировки нагрузки  
├── routing/           # Тесты маршрутизации
├── predictor/         # Тесты ML предиктора
├── gpustack/          # Тесты GPUStack интеграции
├── simple_test.py     # E2E тест роутинга
├── advanced_test.py   # Продвинутые E2E тесты
└── run_integration_tests.py  # Полные интеграционные тесты
```

### Основные команды run.sh
```bash
# Быстрая проверка (новое!)
./run.sh test quick-check [system|analytics|predictor|least-busy|gpustack|routing|reports|all]
./run.sh test quick-check --verbose [category]     # Детальная проверка
./run.sh test quick-check --show-errors [category] # С показом ошибок

# Аналитика
./run.sh analytics test [e2e|sql|failure]
./run.sh analytics health [check|monitor]
./run.sh analytics data [counts|consistency|quality]
./run.sh analytics reports [summary|decisions|accuracy|performance|quality]
./run.sh analytics reports tables [models|deployments|overview|responses] [--full]

# Роутер  
./run.sh testing router [unit|integration|advanced|all]
./run.sh test [endpoint|models]

# Docker
./run.sh docker [status|logs|up|down]

# Отладка
./run.sh debug [status|router|predictor]
```

### Переменные окружения для тестов
```bash
export PREDICTOR_SERVICE_URL="http://localhost:8001"
export ANALYTICS_ENABLED="true"
export GPUSTACK_INTEGRATION_ENABLED="true"
export DATABASE_URL="postgresql://..."
```

## Команды для разработчика

```bash
# Полный сброс и проверка
./run.sh docker down && ./run.sh docker up
sleep 60
./run.sh test quick-check all

# Быстрая диагностика по категориям
./run.sh test quick-check --show-errors --verbose analytics
./run.sh test quick-check --show-command system
./run.sh test quick-check reports

# Мониторинг в реальном времени
./run.sh docker logs -f

# Отладка конкретного компонента
./run.sh debug router
./run.sh debug predictor
docker exec -ti filin-litellm-router python3 -m llm_router.debug.debug_least_busy

# Параметризуемые тесты
docker exec -ti filin-litellm-router python3 -m llm_router.tests.test_true_concurrent --max-tokens 50 --stagger 0.01 --requests 30
./run.sh testing router high-load 25 6

# Проверка отчетов в разных форматах
./run.sh analytics reports summary --format=json --output=summary.json
./run.sh analytics reports tables models --full --format=csv --output=models.csv
```

## Расширение системы тестирования

### Добавление новой команды

1. **Создать класс команды**:
```python
# analytics/utils/e2e/commands/custom.py
from .base import TestCommand
from ..core.models import TestResult

class CustomTestCommand(TestCommand):
    @property
    def name(self) -> str:
        return "custom_test"
    
    async def execute(self) -> TestResult:
        # Реализация теста
        pass
```

2. **Зарегистрировать в реестре**:
```python
# analytics/utils/e2e/commands/registry.py
from .custom import CustomTestCommand

def _register_default_commands(self) -> None:
    # существующие команды...
    self.commands["custom_test"] = CustomTestCommand
```

3. **Добавить обработку в CLI**:
```bash
# В run.sh
analytics)
    case "$2" in
        test)
            case "$3" in
                custom)
                    execute_docker_module "analytics.utils.e2e" "custom_test"
                    ;;
```

### Добавление новых валидаций

```python
# analytics/utils/e2e/validation/custom_validators.py
class CustomValidator:
    def validate_custom_logic(self, data: Dict[str, Any]) -> ValidationResult:
        # Кастомная логика валидации
        pass
```

## Модульное тестирование

### Структура тестов

```
tests/
├── unit/                          # Модульные тесты
│   ├── router/
│   │   ├── test_custom_router.py
│   │   ├── test_deployment_selector.py
│   │   ├── test_predictor_mixin.py
│   │   └── test_gpustack_mixin.py
│   ├── analytics/
│   │   ├── test_analytics_logger.py
│   │   ├── test_data_builder.py
│   │   └── test_db_client.py
│   ├── config/
│   │   ├── test_config_manager.py
│   │   └── test_service_config.py
│   └── common/
│       ├── test_http_client.py
│       └── test_auth_checker.py
├── integration/                   # Интеграционные тесты
│   ├── test_predictor_integration.py
│   ├── test_gpustack_integration.py
│   ├── test_database_integration.py
│   └── test_routing_integration.py
├── e2e/                          # E2E тесты
│   ├── test_full_request_flow.py
│   ├── test_model_endpoints.py
│   └── test_error_scenarios.py
└── load/                         # Нагрузочные тесты
    ├── test_concurrent_requests.py
    ├── test_high_load.py
    └── test_stress_scenarios.py
```

### Примеры модульных тестов

#### Тестирование роутера

```python
# tests/unit/router/test_custom_router.py
import pytest
from unittest.mock import Mock, AsyncMock
from llm_router.router.custom_router import LLMTimePredictorRoutingStrategy

class TestLLMTimePredictorRoutingStrategy:
    
    @pytest.fixture
    def router_config(self):
        return {
            'predictor_url': 'http://test-predictor:8008',
            'gpustack_url': 'http://test-gpustack:80',
            'hybrid_weight': 0.3,
            'cache_ttl': 300
        }
    
    @pytest.fixture
    def router(self, router_config):
        return LLMTimePredictorRoutingStrategy(router_config)
    
    @pytest.mark.asyncio
    async def test_select_deployment_with_ml_predictions(self, router):
        """Тест выбора deployment с ML предсказаниями"""
        # Подготовка данных
        deployments = [
            {'deployment_name': 'gpu-h100-1', 'max_requests': 100},
            {'deployment_name': 'gpu-a6000-1', 'max_requests': 80}
        ]
        
        request_data = {
            'messages': [{'role': 'user', 'content': 'Test message'}],
            'model': 'chat_instruct'
        }
        
        # Мокирование внешних сервисов
        router.predictor_client.get_predictions = AsyncMock(return_value={
            'gpu-h100-1': {'predicted_time': 2.3, 'confidence': 0.92},
            'gpu-a6000-1': {'predicted_time': 3.1, 'confidence': 0.88}
        })
        
        router.get_current_load = Mock(return_value={
            'gpu-h100-1': 5,
            'gpu-a6000-1': 3
        })
        
        # Выполнение теста
        selected = await router.select_deployment(deployments, request_data)
        
        # Проверки
        assert selected is not None
        assert selected['deployment_name'] in ['gpu-h100-1', 'gpu-a6000-1']
        
        # Проверка вызовов внешних сервисов
        router.predictor_client.get_predictions.assert_called_once()
    
    def test_calculate_hybrid_score(self, router):
        """Тест вычисления гибридного балла"""
        ml_prediction = 2.5
        current_load = 10
        max_load = 100
        weight = 0.3
        
        score = router.calculate_hybrid_score(
            ml_prediction, current_load, max_load, weight
        )
        
        expected_ml_normalized = min(ml_prediction / 10.0, 1.0)  # 0.25
        expected_load_penalty = current_load / max_load  # 0.1
        expected_score = (1 - weight) * expected_ml_normalized + weight * expected_load_penalty
        
        assert abs(score - expected_score) < 0.001
    
    @pytest.mark.asyncio
    async def test_fallback_to_least_busy_when_predictor_fails(self, router):
        """Тест fallback на least-busy при недоступности predictor"""
        deployments = [
            {'deployment_name': 'gpu-h100-1', 'max_requests': 100},
            {'deployment_name': 'gpu-a6000-1', 'max_requests': 80}
        ]
        
        request_data = {'messages': [{'role': 'user', 'content': 'Test'}]}
        
        # Мокирование ошибки predictor
        router.predictor_client.get_predictions = AsyncMock(
            side_effect=Exception("Predictor unavailable")
        )
        
        router.get_current_load = Mock(return_value={
            'gpu-h100-1': 10,
            'gpu-a6000-1': 5  # Меньше нагрузки
        })
        
        selected = await router.select_deployment(deployments, request_data)
        
        # Должен выбрать deployment с меньшей нагрузкой
        assert selected['deployment_name'] == 'gpu-a6000-1'
```

#### Тестирование аналитики

```python
# tests/unit/analytics/test_analytics_logger.py
import pytest
from unittest.mock import Mock, AsyncMock
from llm_router.analytics.router_analytics_logger import RouterAnalyticsLogger

class TestRouterAnalyticsLogger:
    
    @pytest.fixture
    def logger_config(self):
        return {
            'database_url': 'postgresql://test:test@localhost:5432/test',
            'batch_size': 10,
            'flush_interval': 5
        }
    
    @pytest.fixture
    def analytics_logger(self, logger_config):
        return RouterAnalyticsLogger(logger_config)
    
    @pytest.mark.asyncio
    async def test_log_routing_decision(self, analytics_logger):
        """Тест логирования решения маршрутизации"""
        decision_data = {
            'request_id': 'req_123',
            'selected_deployment': 'gpu-h100-1',
            'ml_prediction': 2.3,
            'hybrid_score': 1.87,
            'alternatives': [
                {'deployment': 'gpu-a6000-1', 'score': 2.15}
            ]
        }
        
        # Мокирование БД клиента
        analytics_logger.db_client.insert_routing_decision = AsyncMock()
        
        await analytics_logger.log_routing_decision(decision_data)
        
        # Проверка вызова БД
        analytics_logger.db_client.insert_routing_decision.assert_called_once_with(
            decision_data
        )
    
    def test_data_validation(self, analytics_logger):
        """Тест валидации данных перед логированием"""
        invalid_data = {
            'request_id': None,  # Обязательное поле
            'selected_deployment': 'gpu-h100-1'
        }
        
        with pytest.raises(ValueError, match="request_id is required"):
            analytics_logger.validate_decision_data(invalid_data)
    
    @pytest.mark.asyncio
    async def test_batch_processing(self, analytics_logger):
        """Тест пакетной обработки данных"""
        analytics_logger.db_client.batch_insert = AsyncMock()
        
        # Добавление данных в очередь
        for i in range(15):  # Больше чем batch_size (10)
            await analytics_logger.queue_decision({
                'request_id': f'req_{i}',
                'selected_deployment': 'gpu-h100-1'
            })
        
        # Должна произойти пакетная запись
        analytics_logger.db_client.batch_insert.assert_called()
```

### Запуск модульных тестов

```bash
# Запуск всех модульных тестов
pytest tests/unit/ -v

# Запуск с покрытием кода
pytest tests/unit/ --cov=llm_router --cov-report=html

# Запуск конкретного теста
pytest tests/unit/router/test_custom_router.py::TestLLMTimePredictorRoutingStrategy::test_select_deployment_with_ml_predictions -v

# Запуск тестов с маркерами
pytest tests/unit/ -m "not slow" -v
```

## Интеграционное тестирование

### Тестирование интеграций

#### Тест ML Predictor интеграции

```python
# tests/integration/test_predictor_integration.py
import pytest
import aiohttp
from llm_router.predictor.predictor_client import PredictorClient

class TestPredictorIntegration:
    
    @pytest.fixture
    def predictor_client(self):
        return PredictorClient(
            url='http://localhost:8008',
            timeout=10
        )
    
    @pytest.mark.asyncio
    async def test_get_predictions_success(self, predictor_client):
        """Тест успешного получения предсказаний"""
        request_data = {
            'text': 'Test message for prediction',
            'task_type': 'chat',
            'model_info': {
                'name': 'llama-3.1-70b-instruct',
                'size': '70B'
            }
        }
        
        deployments = ['gpu-h100-1', 'gpu-a6000-1']
        
        predictions = await predictor_client.get_predictions(
            request_data, deployments
        )
        
        # Проверки
        assert isinstance(predictions, dict)
        assert len(predictions) == len(deployments)
        
        for deployment in deployments:
            assert deployment in predictions
            assert 'predicted_time' in predictions[deployment]
            assert 'confidence' in predictions[deployment]
            assert predictions[deployment]['predicted_time'] > 0
            assert 0 <= predictions[deployment]['confidence'] <= 1
    
    @pytest.mark.asyncio
    async def test_predictor_timeout_handling(self, predictor_client):
        """Тест обработки таймаута predictor"""
        # Настройка короткого таймаута
        predictor_client.timeout = 0.1
        
        with pytest.raises(aiohttp.ClientTimeout):
            await predictor_client.get_predictions(
                {'text': 'test'}, ['gpu-h100-1']
            )
    
    @pytest.mark.asyncio
    async def test_predictor_error_handling(self, predictor_client):
        """Тест обработки ошибок predictor"""
        # Неверный URL для вызова ошибки
        predictor_client.url = 'http://nonexistent-service:8008'
        
        with pytest.raises(aiohttp.ClientConnectorError):
            await predictor_client.get_predictions(
                {'text': 'test'}, ['gpu-h100-1']
            )
```

#### Тест GPUStack интеграции

```python
# tests/integration/test_gpustack_integration.py
import pytest
from llm_router.router_gpustack.gpustack_integration import GPUStackIntegration

class TestGPUStackIntegration:
    
    @pytest.fixture
    def gpustack_client(self):
        return GPUStackIntegration(
            url='http://localhost:80',
            api_key='test-key',
            token='test-token'
        )
    
    @pytest.mark.asyncio
    async def test_get_workers_info(self, gpustack_client):
        """Тест получения информации о workers"""
        workers = await gpustack_client.get_workers_info()
        
        assert isinstance(workers, list)
        
        for worker in workers:
            assert 'worker_id' in worker
            assert 'gpu_specs' in worker
            assert 'status' in worker
            
            gpu_specs = worker['gpu_specs']
            assert 'model' in gpu_specs
            assert 'memory_total' in gpu_specs
    
    @pytest.mark.asyncio
    async def test_get_model_deployments(self, gpustack_client):
        """Тест получения информации о deployments моделей"""
        deployments = await gpustack_client.get_model_deployments()
        
        assert isinstance(deployments, dict)
        
        for deployment_name, info in deployments.items():
            assert 'worker_id' in info
            assert 'model_name' in info
            assert 'model_size' in info
    
    @pytest.mark.asyncio
    async def test_cache_functionality(self, gpustack_client):
        """Тест функциональности кэширования"""
        # Первый вызов - должен обратиться к API
        workers1 = await gpustack_client.get_workers_info()
        
        # Второй вызов - должен использовать кэш
        workers2 = await gpustack_client.get_workers_info()
        
        assert workers1 == workers2
        
        # Проверка статистики кэша
        cache_stats = gpustack_client.get_cache_stats()
        assert cache_stats['hit_rate'] > 0
```

### Тестирование маршрутизации

```python
# tests/integration/test_routing_integration.py
import pytest
from llm_router.router.custom_router import LLMTimePredictorRoutingStrategy

class TestRoutingIntegration:
    
    @pytest.fixture
    def full_router_config(self):
        return {
            'predictor_url': 'http://localhost:8008',
            'gpustack_url': 'http://localhost:80',
            'gpustack_key': 'test-key',
            'hybrid_weight': 0.3,
            'cache_ttl': 300,
            'fallback_strategy': 'least-busy'
        }
    
    @pytest.fixture
    def router(self, full_router_config):
        return LLMTimePredictorRoutingStrategy(full_router_config)
    
    @pytest.mark.asyncio
    async def test_full_routing_flow(self, router):
        """Тест полного потока маршрутизации"""
        deployments = [
            {
                'deployment_name': 'gpu-h100-1',
                'litellm_params': {'model': 'llama-3.1-70b-instruct'},
                'max_requests': 100
            },
            {
                'deployment_name': 'gpu-a6000-1', 
                'litellm_params': {'model': 'llama-3.1-70b-instruct'},
                'max_requests': 80
            }
        ]
        
        request_data = {
            'messages': [
                {'role': 'user', 'content': 'Explain quantum computing'}
            ],
            'model': 'chat_instruct',
            'max_tokens': 1000
        }
        
        # Выполнение маршрутизации
        selected = await router.select_deployment(deployments, request_data)
        
        # Проверки
        assert selected is not None
        assert selected['deployment_name'] in [d['deployment_name'] for d in deployments]
        
        # Проверка логирования аналитики
        assert hasattr(router, 'analytics_logger')
        # Здесь можно проверить, что данные были залогированы
    
    @pytest.mark.asyncio
    async def test_routing_with_different_request_types(self, router):
        """Тест маршрутизации для разных типов запросов"""
        deployments = [
            {'deployment_name': 'gpu-code-1', 'max_requests': 50},
            {'deployment_name': 'gpu-chat-1', 'max_requests': 100}
        ]
        
        # Тест chat запроса
        chat_request = {
            'messages': [{'role': 'user', 'content': 'Hello, how are you?'}],
            'model': 'chat_instruct'
        }
        
        chat_selected = await router.select_deployment(deployments, chat_request)
        
        # Тест code запроса
        code_request = {
            'messages': [{'role': 'user', 'content': 'def fibonacci(n):'}],
            'model': 'code_instruct'
        }
        
        code_selected = await router.select_deployment(deployments, code_request)
        
        # Проверки
        assert chat_selected is not None
        assert code_selected is not None
        
        # Роутер может выбрать разные deployments для разных типов задач
        # (в зависимости от ML предсказаний)
```

## E2E тестирование

### Архитектура E2E тестов

```mermaid
graph LR
    subgraph "E2E Test Environment"
        Client[Test Client]
        LiteLLM[LiteLLM Proxy]
        Router[LLM Router]
        MockPredictor[Mock ML Predictor]
        MockGPUStack[Mock GPUStack]
        TestDB[(Test Database)]
    end
    
    Client --> LiteLLM
    LiteLLM --> Router
    Router --> MockPredictor
    Router --> MockGPUStack
    Router --> TestDB
    
    style Client fill:#e8f5e8
    style LiteLLM fill:#e1f5fe
    style Router fill:#fff3e0
```

### Docker архитектура для E2E тестов

```yaml
# docker-compose.test.yml
version: '3.8'

services:
  litellm-test:
    build:
      context: .
      dockerfile: Dockerfile
      target: runtime
    ports:
      - "4001:4000"
    environment:
      - DATABASE_URL=***********************************/litellm_test
      - PREDICT_URL=http://mock-predictor:8008
      - GPUSTACK_URL=http://mock-gpustack:80
      - LITELLM_LOG=DEBUG
    volumes:
      - ./tests/e2e/litellm.test.config.yaml:/app/litellm.config.yaml:ro
    depends_on:
      - test-db
      - mock-predictor
      - mock-gpustack

  test-db:
    image: postgres:16
    environment:
      - POSTGRES_DB=litellm_test
      - POSTGRES_USER=test
      - POSTGRES_PASSWORD=test
    ports:
      - "5433:5432"

  mock-predictor:
    build:
      context: ./tests/e2e/mocks
      dockerfile: Dockerfile.predictor
    ports:
      - "8009:8008"

  mock-gpustack:
    build:
      context: ./tests/e2e/mocks
      dockerfile: Dockerfile.gpustack
    ports:
      - "8080:80"
```

### E2E тесты

```python
# tests/e2e/test_full_request_flow.py
import pytest
import aiohttp
import asyncio
from typing import Dict, Any

class TestFullRequestFlow:
    
    @pytest.fixture
    def litellm_base_url(self):
        return 'http://localhost:4001'
    
    @pytest.fixture
    def headers(self):
        return {
            'Authorization': 'Bearer sk-test-key',
            'Content-Type': 'application/json'
        }
    
    @pytest.mark.asyncio
    async def test_chat_completion_flow(self, litellm_base_url, headers):
        """Тест полного потока chat completion"""
        request_data = {
            'model': 'chat_instruct',
            'messages': [
                {'role': 'user', 'content': 'What is the capital of France?'}
            ],
            'max_tokens': 100,
            'temperature': 0.7
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f'{litellm_base_url}/v1/chat/completions',
                json=request_data,
                headers=headers
            ) as response:
                
                assert response.status == 200
                
                result = await response.json()
                
                # Проверка структуры ответа OpenAI
                assert 'id' in result
                assert 'object' in result
                assert result['object'] == 'chat.completion'
                assert 'choices' in result
                assert len(result['choices']) > 0
                
                choice = result['choices'][0]
                assert 'message' in choice
                assert 'content' in choice['message']
                assert len(choice['message']['content']) > 0
    
    @pytest.mark.asyncio
    async def test_streaming_completion(self, litellm_base_url, headers):
        """Тест streaming completion"""
        request_data = {
            'model': 'chat_instruct',
            'messages': [
                {'role': 'user', 'content': 'Count from 1 to 5'}
            ],
            'stream': True,
            'max_tokens': 50
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f'{litellm_base_url}/v1/chat/completions',
                json=request_data,
                headers=headers
            ) as response:
                
                assert response.status == 200
                assert response.headers.get('content-type') == 'text/plain; charset=utf-8'
                
                chunks = []
                async for line in response.content:
                    if line:
                        line_str = line.decode('utf-8').strip()
                        if line_str.startswith('data: '):
                            chunks.append(line_str[6:])
                
                # Проверка получения streaming chunks
                assert len(chunks) > 0
                assert chunks[-1] == '[DONE]'
    
    @pytest.mark.asyncio
    async def test_model_routing_decisions(self, litellm_base_url, headers):
        """Тест решений маршрутизации для разных моделей"""
        models_to_test = ['chat_instruct', 'code_instruct']
        
        for model in models_to_test:
            request_data = {
                'model': model,
                'messages': [
                    {'role': 'user', 'content': f'Test request for {model}'}
                ],
                'max_tokens': 50
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f'{litellm_base_url}/v1/chat/completions',
                    json=request_data,
                    headers=headers
                ) as response:
                    
                    assert response.status == 200
                    result = await response.json()
                    
                    # Проверка, что запрос был обработан
                    assert 'choices' in result
                    assert len(result['choices']) > 0
    
    @pytest.mark.asyncio
    async def test_concurrent_requests(self, litellm_base_url, headers):
        """Тест конкурентных запросов"""
        async def make_request(session, request_id):
            request_data = {
                'model': 'chat_instruct',
                'messages': [
                    {'role': 'user', 'content': f'Concurrent request {request_id}'}
                ],
                'max_tokens': 50
            }
            
            async with session.post(
                f'{litellm_base_url}/v1/chat/completions',
                json=request_data,
                headers=headers
            ) as response:
                return response.status, await response.json()
        
        # Выполнение 10 конкурентных запросов
        async with aiohttp.ClientSession() as session:
            tasks = [
                make_request(session, i) for i in range(10)
            ]
            
            results = await asyncio.gather(*tasks)
            
            # Проверка, что все запросы успешны
            for status, result in results:
                assert status == 200
                assert 'choices' in result
    
    @pytest.mark.asyncio
    async def test_error_handling(self, litellm_base_url, headers):
        """Тест обработки ошибок"""
        # Тест с неверной моделью
        request_data = {
            'model': 'nonexistent_model',
            'messages': [
                {'role': 'user', 'content': 'Test message'}
            ]
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f'{litellm_base_url}/v1/chat/completions',
                json=request_data,
                headers=headers
            ) as response:
                
                assert response.status == 400
                error_result = await response.json()
                assert 'error' in error_result
    
    @pytest.mark.asyncio
    async def test_health_endpoints(self, litellm_base_url):
        """Тест health endpoints"""
        async with aiohttp.ClientSession() as session:
            # Тест основного health endpoint
            async with session.get(f'{litellm_base_url}/health') as response:
                assert response.status == 200
                result = await response.json()
                assert result.get('status') == 'healthy'
            
            # Тест readiness endpoint
            async with session.get(f'{litellm_base_url}/health/readiness') as response:
                assert response.status == 200
            
            # Тест liveness endpoint
            async with session.get(f'{litellm_base_url}/health/liveness') as response:
                assert response.status == 200
```

### Запуск E2E тестов

```bash
# Запуск E2E тестов с тестовым окружением
./run.sh test e2e

# Запуск E2E тестов с детальным логированием
./run.sh test e2e --verbose --log-requests

# Запуск конкретного E2E теста
./run.sh test e2e --test test_full_request_flow.py

# Запуск E2E тестов с покрытием
./run.sh test e2e --coverage
```

## Нагрузочное тестирование

### Конфигурация нагрузочных тестов

```python
# tests/load/test_high_load.py
import pytest
import asyncio
import aiohttp
import time
from typing import List, Dict
import statistics

class TestHighLoad:
    
    @pytest.fixture
    def load_test_config(self):
        return {
            'base_url': 'http://localhost:4001',
            'concurrent_users': 50,
            'requests_per_user': 20,
            'ramp_up_time': 30,  # секунд
            'test_duration': 300,  # секунд
        }
    
    @pytest.mark.asyncio
    async def test_sustained_load(self, load_test_config):
        """Тест устойчивой нагрузки"""
        results = []
        
        async def user_session(user_id: int, session: aiohttp.ClientSession):
            """Симуляция пользовательской сессии"""
            user_results = []
            
            for request_id in range(load_test_config['requests_per_user']):
                start_time = time.time()
                
                try:
                    request_data = {
                        'model': 'chat_instruct',
                        'messages': [
                            {'role': 'user', 'content': f'Load test request {user_id}-{request_id}'}
                        ],
                        'max_tokens': 100
                    }
                    
                    async with session.post(
                        f"{load_test_config['base_url']}/v1/chat/completions",
                        json=request_data,
                        headers={'Authorization': 'Bearer sk-test-key'}
                    ) as response:
                        
                        response_time = time.time() - start_time
                        status = response.status
                        
                        user_results.append({
                            'user_id': user_id,
                            'request_id': request_id,
                            'response_time': response_time,
                            'status': status,
                            'success': status == 200
                        })
                        
                        if status != 200:
                            error_text = await response.text()
                            print(f"Error for user {user_id}, request {request_id}: {error_text}")
                
                except Exception as e:
                    response_time = time.time() - start_time
                    user_results.append({
                        'user_id': user_id,
                        'request_id': request_id,
                        'response_time': response_time,
                        'status': 0,
                        'success': False,
                        'error': str(e)
                    })
                
                # Пауза между запросами
                await asyncio.sleep(0.1)
            
            return user_results
        
        # Создание пользовательских сессий
        async with aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30)
        ) as session:
            
            # Постепенное увеличение нагрузки (ramp-up)
            tasks = []
            ramp_up_delay = load_test_config['ramp_up_time'] / load_test_config['concurrent_users']
            
            for user_id in range(load_test_config['concurrent_users']):
                await asyncio.sleep(ramp_up_delay)
                task = asyncio.create_task(user_session(user_id, session))
                tasks.append(task)
            
            # Ожидание завершения всех задач
            all_results = await asyncio.gather(*tasks)
            
            # Объединение результатов
            for user_results in all_results:
                results.extend(user_results)
        
        # Анализ результатов
        self.analyze_load_test_results(results, load_test_config)
    
    def analyze_load_test_results(self, results: List[Dict], config: Dict):
        """Анализ результатов нагрузочного тестирования"""
        total_requests = len(results)
        successful_requests = sum(1 for r in results if r['success'])
        failed_requests = total_requests - successful_requests
        
        success_rate = successful_requests / total_requests if total_requests > 0 else 0
        
        response_times = [r['response_time'] for r in results if r['success']]
        
        if response_times:
            avg_response_time = statistics.mean(response_times)
            median_response_time = statistics.median(response_times)
            p95_response_time = statistics.quantiles(response_times, n=20)[18]  # 95th percentile
            p99_response_time = statistics.quantiles(response_times, n=100)[98]  # 99th percentile
            max_response_time = max(response_times)
            min_response_time = min(response_times)
        else:
            avg_response_time = median_response_time = p95_response_time = p99_response_time = 0
            max_response_time = min_response_time = 0
        
        # Вывод результатов
        print(f"\n=== Результаты нагрузочного тестирования ===")
        print(f"Конфигурация:")
        print(f"  Конкурентных пользователей: {config['concurrent_users']}")
        print(f"  Запросов на пользователя: {config['requests_per_user']}")
        print(f"  Время ramp-up: {config['ramp_up_time']}s")
        
        print(f"\nОбщие результаты:")
        print(f"  Всего запросов: {total_requests}")
        print(f"  Успешных запросов: {successful_requests}")
        print(f"  Неудачных запросов: {failed_requests}")
        print(f"  Процент успеха: {success_rate:.2%}")
        
        print(f"\nВремя ответа:")
        print(f"  Среднее: {avg_response_time:.3f}s")
        print(f"  Медиана: {median_response_time:.3f}s")
        print(f"  95-й процентиль: {p95_response_time:.3f}s")
        print(f"  99-й процентиль: {p99_response_time:.3f}s")
        print(f"  Максимальное: {max_response_time:.3f}s")
        print(f"  Минимальное: {min_response_time:.3f}s")
        
        # Проверки производительности
        assert success_rate >= 0.95, f"Процент успеха слишком низкий: {success_rate:.2%}"
        assert avg_response_time <= 10.0, f"Среднее время ответа слишком высокое: {avg_response_time:.3f}s"
        assert p95_response_time <= 20.0, f"95-й процентиль времени ответа слишком высокий: {p95_response_time:.3f}s"
```

### Запуск нагрузочных тестов

```bash
# Базовый нагрузочный тест
./run.sh test load

# Нагрузочный тест с параметрами
./run.sh test load --concurrent 100 --requests 50 --duration 600

# Стресс-тестирование
./run.sh test stress --max-users 500 --ramp-up 120

# Тест производительности с мониторингом
./run.sh test performance --monitor-resources
```

## Автоматизация тестирования

### CI/CD интеграция

```yaml
# .github/workflows/test.yml
name: Test Suite

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.13'
      
      - name: Install dependencies
        run: |
          pip install -r requirements.txt
          pip install pytest pytest-asyncio pytest-cov
      
      - name: Run unit tests
        run: |
          pytest tests/unit/ --cov=llm_router --cov-report=xml
      
      - name: Upload coverage
        uses: codecov/codecov-action@v3

  integration-tests:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:16
        env:
          POSTGRES_PASSWORD: test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Start test services
        run: |
          docker-compose -f docker-compose.test.yml up -d
          sleep 30  # Ожидание запуска сервисов
      
      - name: Run integration tests
        run: |
          ./run.sh test integration
      
      - name: Cleanup
        run: |
          docker-compose -f docker-compose.test.yml down

  e2e-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Start full test environment
        run: |
          ./run.sh docker up --build -d
          sleep 60  # Ожидание полного запуска
      
      - name: Run E2E tests
        run: |
          ./run.sh test e2e
      
      - name: Collect logs on failure
        if: failure()
        run: |
          ./run.sh docker logs > test-logs.txt
      
      - name: Upload logs
        if: failure()
        uses: actions/upload-artifact@v3
        with:
          name: test-logs
          path: test-logs.txt
```

### Тестовые чек-листы

#### Быстрая проверка (< 2 минуты)

```bash
#!/bin/bash
# quick-check.sh

echo "=== Быстрая проверка системы ==="

# 1. Проверка конфигурации
echo "1. Проверка конфигурации..."
./run.sh config validate || exit 1

# 2. Проверка health endpoints
echo "2. Проверка health endpoints..."
./run.sh test endpoint || exit 1

# 3. Проверка подключений
echo "3. Проверка внешних подключений..."
./run.sh config test-database || exit 1

echo "✓ Быстрая проверка завершена успешно"
```

#### Полная проверка (< 10 минут)

```bash
#!/bin/bash
# full-check.sh

echo "=== Полная проверка системы ==="

# 1. Быстрая проверка
./quick-check.sh || exit 1

# 2. Модульные тесты
echo "4. Запуск модульных тестов..."
./run.sh test unit || exit 1

# 3. Интеграционные тесты
echo "5. Запуск интеграционных тестов..."
./run.sh test integration || exit 1

# 4. Тест маршрутизации
echo "6. Тест маршрутизации..."
./run.sh test routing || exit 1

# 5. Тест моделей
echo "7. Тест моделей..."
./run.sh test models || exit 1

echo "✓ Полная проверка завершена успешно"
```

---

*Следующий раздел: [Справочник API](API_REFERENCE.md)*
## 
Заключение

Система тестирования LLM Router обеспечивает:

- **Быстрая валидация**: Экспресс-проверка работоспособности за 5 минут
- **Всестороннее тестирование**: Комплексная проверка всех компонентов за 30-60 минут  
- **E2E тестирование аналитики**: Специализированные тесты с 4-уровневой валидацией
- **Отказоустойчивость**: Тестирование критических сценариев отказов
- **Производительность**: Мониторинг временных характеристик и нагрузочное тестирование
- **Расширяемость**: Модульная архитектура для добавления новых тестов
- **Автоматизация**: Готовность к интеграции в CI/CD пайплайны

### 💡 Pro Tips

#### Ускорение тестов
- Используйте Docker exec для параллельного запуска тестов
- Кэшируйте результаты health checks
- Запускайте только изменённые компоненты

#### Мониторинг в реальном времени
```bash
# Терминал 1: Логи
./run.sh docker logs -f

# Терминал 2: Мониторинг роутинга  
./run.sh monitor routing

# Терминал 3: Выполнение тестов
./run.sh analytics test e2e
```

#### Автоматизация
```bash
# Создайте alias для быстрых проверок
alias router-check='cd /path/to/llm-router && ./run.sh docker status && ./run.sh analytics test e2e && ./run.sh test endpoint'

# Запуск в background
nohup ./run.sh analytics test e2e > test-results.log 2>&1 &
```

### 📞 Когда обращаться за помощью

#### 🟢 Всё хорошо
- Все 5 команд из быстрой проверки проходят
- E2E тест аналитики успешен
- Роутер отвечает на запросы

#### 🟡 Нужно внимание  
- Некоторые компоненты не работают
- Высокое время ответа
- Предупреждения в логах

#### 🔴 Критическая ситуация
- Docker сервисы не запускаются
- E2E тест аналитики фейлится
- 500 ошибки при запросах
- Предиктор недоступен

**В критической ситуации:** запустите полную диагностику из раздела "Всестороннее тестирование", используйте экстренную диагностику и проверьте типичные проблемы и их решения.

Регулярное использование системы тестирования гарантирует стабильную работу LLM Router и раннее обнаружение проблем в production среде.