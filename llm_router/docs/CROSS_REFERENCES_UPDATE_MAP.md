# Карта обновления перекрестных ссылок

## Обзор

Данный документ содержит полную карту всех перекрестных ссылок, которые необходимо обновить в процессе консолидации документации. Включает внутренние ссылки между файлами документации, внешние ссылки из кода проекта, и якорные ссылки внутри файлов.

---

## Внутренние ссылки документации

### Ссылки из README.md

#### Текущие ссылки → Новые ссылки:
```markdown
# СТАРЫЕ ССЫЛКИ (для замены):
[DOCUMENTATION_INDEX.md](./DOCUMENTATION_INDEX.md)
→ УДАЛИТЬ (содержимое интегрировано в README.md)

[PROJECT_ARCHITECTURE.md](./PROJECT_ARCHITECTURE.md)
→ [ARCHITECTURE.md](./ARCHITECTURE.md)

[COMMANDS_QUICK_REFERENCE.md](./COMMANDS_QUICK_REFERENCE.md)
→ [OPERATIONS_GUIDE.md](./OPERATIONS_GUIDE.md#quick-reference)

[ANALYTICS_INTEGRATION.md](./ANALYTICS_INTEGRATION.md)
→ [ANALYTICS_GUIDE.md](./ANALYTICS_GUIDE.md#overview)

[E2E_TESTING_GUIDE.md](./E2E_TESTING_GUIDE.md)
→ [TESTING_GUIDE.md](./TESTING_GUIDE.md#e2e-testing)

[QUICK_TEST_CHECKLIST.md](./QUICK_TEST_CHECKLIST.md)
→ [TESTING_GUIDE.md](./TESTING_GUIDE.md#quick-validation)
```

### Ссылки из DOCUMENTATION_INDEX.md (интегрируется в README.md)

#### Архитектурные ссылки:
```markdown
# СТАРЫЕ ССЫЛКИ:
[PROJECT_ARCHITECTURE.md](./PROJECT_ARCHITECTURE.md)
[LLM_ROUTER_CODEBASE_ARCHITECTURE.md](./LLM_ROUTER_CODEBASE_ARCHITECTURE.md)
[LLM_ROUTER_DATA_FLOW_ARCHITECTURE.md](./LLM_ROUTER_DATA_FLOW_ARCHITECTURE.md)

# НОВЫЕ ССЫЛКИ:
[ARCHITECTURE.md](./ARCHITECTURE.md)
[ARCHITECTURE.md#system-overview](./ARCHITECTURE.md#system-overview)
[ARCHITECTURE.md#code-structure](./ARCHITECTURE.md#code-structure)
[ARCHITECTURE.md#data-flows](./ARCHITECTURE.md#data-flows)
```

#### Аналитические ссылки:
```markdown
# СТАРЫЕ ССЫЛКИ:
[ANALYTICS_INTEGRATION.md](./ANALYTICS_INTEGRATION.md)
[ANALYTICS_MIGRATIONS.md](./ANALYTICS_MIGRATIONS.md)
[ANALYTICS_MIGRATIONS_COMPLETE_GUIDE.md](./ANALYTICS_MIGRATIONS_COMPLETE_GUIDE.md)
[ANALYTICS_MIGRATIONS_CHEATSHEET.md](./ANALYTICS_MIGRATIONS_CHEATSHEET.md)
[ANALYTICS_REPORTS_GUIDE.md](./ANALYTICS_REPORTS_GUIDE.md)
[ANALYTICS_DATA_COMMANDS.md](./ANALYTICS_DATA_COMMANDS.md)
[ANALYTICS_PRISMA_COMMANDS.md](./ANALYTICS_PRISMA_COMMANDS.md)

# НОВЫЕ ССЫЛКИ:
[ANALYTICS_GUIDE.md](./ANALYTICS_GUIDE.md)
[ANALYTICS_GUIDE.md#overview](./ANALYTICS_GUIDE.md#overview)
[ANALYTICS_GUIDE.md#migrations](./ANALYTICS_GUIDE.md#migrations)
[ANALYTICS_GUIDE.md#setup](./ANALYTICS_GUIDE.md#setup)
[ANALYTICS_GUIDE.md#quick-reference](./ANALYTICS_GUIDE.md#quick-reference)
[ANALYTICS_GUIDE.md#reports](./ANALYTICS_GUIDE.md#reports)
[ANALYTICS_GUIDE.md#data-commands](./ANALYTICS_GUIDE.md#data-commands)
[ANALYTICS_GUIDE.md#prisma-commands](./ANALYTICS_GUIDE.md#prisma-commands)
```

#### Командные ссылки:
```markdown
# СТАРЫЕ ССЫЛКИ:
[COMMANDS_DOCUMENTATION.md](./COMMANDS_DOCUMENTATION.md)
[COMMANDS_QUICK_REFERENCE.md](./COMMANDS_QUICK_REFERENCE.md)
[MIGRATION_DIRECT_COMMANDS.md](./MIGRATION_DIRECT_COMMANDS.md)

# НОВЫЕ ССЫЛКИ:
[OPERATIONS_GUIDE.md](./OPERATIONS_GUIDE.md)
[OPERATIONS_GUIDE.md#quick-reference](./OPERATIONS_GUIDE.md#quick-reference)
[MIGRATION_GUIDE.md](./MIGRATION_GUIDE.md)
```

#### Тестовые ссылки:
```markdown
# СТАРЫЕ ССЫЛКИ:
[E2E_TESTING_GUIDE.md](./E2E_TESTING_GUIDE.md)
[E2E_TESTING_DOCKER_ARCHITECTURE.md](./E2E_TESTING_DOCKER_ARCHITECTURE.md)
[TESTING_NAVIGATOR.md](./TESTING_NAVIGATOR.md)
[QUICK_TEST_CHECKLIST.md](./QUICK_TEST_CHECKLIST.md)

# НОВЫЕ ССЫЛКИ:
[TESTING_GUIDE.md](./TESTING_GUIDE.md)
[TESTING_GUIDE.md#e2e-testing](./TESTING_GUIDE.md#e2e-testing)
[TESTING_GUIDE.md#docker-testing](./TESTING_GUIDE.md#docker-testing)
[TESTING_GUIDE.md#navigation](./TESTING_GUIDE.md#navigation)
[TESTING_GUIDE.md#quick-validation](./TESTING_GUIDE.md#quick-validation)
```

### Ссылки между архитектурными файлами

#### Из PROJECT_ARCHITECTURE.md:
```markdown
# СТАРЫЕ ССЫЛКИ:
[LLM_ROUTER_CODEBASE_ARCHITECTURE.md](./LLM_ROUTER_CODEBASE_ARCHITECTURE.md)
[LLM_ROUTER_DATA_FLOW_ARCHITECTURE.md](./LLM_ROUTER_DATA_FLOW_ARCHITECTURE.md)
[DATA_LOGGING_ARCHITECTURE.md](./DATA_LOGGING_ARCHITECTURE.md)

# НОВЫЕ ССЫЛКИ (внутри ARCHITECTURE.md):
[#code-structure](#code-structure)
[#data-flows](#data-flows)
[DATA_LOGGING_ARCHITECTURE.md](./DATA_LOGGING_ARCHITECTURE.md) # остается без изменений
```

#### Из LLM_ROUTER_CODEBASE_ARCHITECTURE.md:
```markdown
# СТАРЫЕ ССЫЛКИ:
[PROJECT_ARCHITECTURE.md](./PROJECT_ARCHITECTURE.md)
[LLM_ROUTER_DATA_FLOW_ARCHITECTURE.md](./LLM_ROUTER_DATA_FLOW_ARCHITECTURE.md)
[ANALYTICS_INTEGRATION.md](./ANALYTICS_INTEGRATION.md)

# НОВЫЕ ССЫЛКИ (внутри ARCHITECTURE.md):
[#system-overview](#system-overview)
[#data-flows](#data-flows)
[ANALYTICS_GUIDE.md](./ANALYTICS_GUIDE.md#overview)
```

#### Из LLM_ROUTER_DATA_FLOW_ARCHITECTURE.md:
```markdown
# СТАРЫЕ ССЫЛКИ:
[PROJECT_ARCHITECTURE.md](./PROJECT_ARCHITECTURE.md)
[LLM_ROUTER_CODEBASE_ARCHITECTURE.md](./LLM_ROUTER_CODEBASE_ARCHITECTURE.md)

# НОВЫЕ ССЫЛКИ (внутри ARCHITECTURE.md):
[#system-overview](#system-overview)
[#code-structure](#code-structure)
```

### Ссылки между аналитическими файлами

#### Из ANALYTICS_INTEGRATION.md:
```markdown
# СТАРЫЕ ССЫЛКИ:
[ANALYTICS_MIGRATIONS.md](./ANALYTICS_MIGRATIONS.md)
[ANALYTICS_REPORTS_GUIDE.md](./ANALYTICS_REPORTS_GUIDE.md)
[DATA_LOGGING_ARCHITECTURE.md](./DATA_LOGGING_ARCHITECTURE.md)

# НОВЫЕ ССЫЛКИ (внутри ANALYTICS_GUIDE.md):
[#migrations](#migrations)
[#reports](#reports)
[DATA_LOGGING_ARCHITECTURE.md](./DATA_LOGGING_ARCHITECTURE.md) # остается без изменений
```

#### Из ANALYTICS_MIGRATIONS.md:
```markdown
# СТАРЫЕ ССЫЛКИ:
[ANALYTICS_INTEGRATION.md](./ANALYTICS_INTEGRATION.md)
[ANALYTICS_MIGRATIONS_COMPLETE_GUIDE.md](./ANALYTICS_MIGRATIONS_COMPLETE_GUIDE.md)
[ANALYTICS_MIGRATIONS_CHEATSHEET.md](./ANALYTICS_MIGRATIONS_CHEATSHEET.md)
[MIGRATION_DIRECT_COMMANDS.md](./MIGRATION_DIRECT_COMMANDS.md)

# НОВЫЕ ССЫЛКИ (внутри ANALYTICS_GUIDE.md):
[#overview](#overview)
[#setup](#setup)
[#quick-reference](#quick-reference)
[MIGRATION_GUIDE.md](./MIGRATION_GUIDE.md)
```

#### Из ANALYTICS_REPORTS_GUIDE.md:
```markdown
# СТАРЫЕ ССЫЛКИ:
[ANALYTICS_INTEGRATION.md](./ANALYTICS_INTEGRATION.md)
[ANALYTICS_DATA_COMMANDS.md](./ANALYTICS_DATA_COMMANDS.md)
[DATA_LOGGING_ARCHITECTURE.md](./DATA_LOGGING_ARCHITECTURE.md)

# НОВЫЕ ССЫЛКИ (внутри ANALYTICS_GUIDE.md):
[#overview](#overview)
[#data-commands](#data-commands)
[DATA_LOGGING_ARCHITECTURE.md](./DATA_LOGGING_ARCHITECTURE.md) # остается без изменений
```

### Ссылки между тестовыми файлами

#### Из E2E_TESTING_GUIDE.md:
```markdown
# СТАРЫЕ ССЫЛКИ:
[E2E_TESTING_DOCKER_ARCHITECTURE.md](./E2E_TESTING_DOCKER_ARCHITECTURE.md)
[TESTING_NAVIGATOR.md](./TESTING_NAVIGATOR.md)
[ANALYTICS_INTEGRATION.md](./ANALYTICS_INTEGRATION.md)

# НОВЫЕ ССЫЛКИ (внутри TESTING_GUIDE.md):
[#docker-testing](#docker-testing)
[#navigation](#navigation)
[ANALYTICS_GUIDE.md](./ANALYTICS_GUIDE.md#overview)
```

#### Из TESTING_NAVIGATOR.md:
```markdown
# СТАРЫЕ ССЫЛКИ:
[QUICK_TEST_CHECKLIST.md](./QUICK_TEST_CHECKLIST.md)
[E2E_TESTING_GUIDE.md](./E2E_TESTING_GUIDE.md)
[COMMANDS_QUICK_REFERENCE.md](./COMMANDS_QUICK_REFERENCE.md)

# НОВЫЕ ССЫЛКИ (внутри TESTING_GUIDE.md):
[#quick-validation](#quick-validation)
[#e2e-testing](#e2e-testing)
[OPERATIONS_GUIDE.md](./OPERATIONS_GUIDE.md#quick-reference)
```

### Ссылки между командными файлами

#### Из COMMANDS_DOCUMENTATION.md:
```markdown
# СТАРЫЕ ССЫЛКИ:
[COMMANDS_QUICK_REFERENCE.md](./COMMANDS_QUICK_REFERENCE.md)
[ANALYTICS_DATA_COMMANDS.md](./ANALYTICS_DATA_COMMANDS.md)
[E2E_TESTING_GUIDE.md](./E2E_TESTING_GUIDE.md)

# НОВЫЕ ССЫЛКИ (внутри OPERATIONS_GUIDE.md):
[#quick-reference](#quick-reference)
[ANALYTICS_GUIDE.md](./ANALYTICS_GUIDE.md#data-commands)
[TESTING_GUIDE.md](./TESTING_GUIDE.md#e2e-testing)
```

---

## Внешние ссылки из кода проекта

### Ссылки из Python файлов

#### llm_router/__init__.py:
```python
# СТАРЫЕ ССЫЛКИ в docstrings:
"""
Подробная документация: docs/PROJECT_ARCHITECTURE.md
Команды: docs/COMMANDS_DOCUMENTATION.md
"""

# НОВЫЕ ССЫЛКИ:
"""
Подробная документация: docs/ARCHITECTURE.md
Команды: docs/OPERATIONS_GUIDE.md
"""
```

#### llm_router/start_litellm_with_router.py:
```python
# СТАРЫЕ ССЫЛКИ в комментариях:
# См. docs/ANALYTICS_INTEGRATION.md для настройки аналитики
# См. docs/COMMANDS_QUICK_REFERENCE.md для команд

# НОВЫЕ ССЫЛКИ:
# См. docs/ANALYTICS_GUIDE.md для настройки аналитики
# См. docs/OPERATIONS_GUIDE.md#quick-reference для команд
```

#### llm_router/analytics/router_analytics_logger.py:
```python
# СТАРЫЕ ССЫЛКИ:
# Документация по миграциям: docs/ANALYTICS_MIGRATIONS.md
# Схема данных: docs/DATA_LOGGING_ARCHITECTURE.md

# НОВЫЕ ССЫЛКИ:
# Документация по миграциям: docs/ANALYTICS_GUIDE.md#migrations
# Схема данных: docs/DATA_LOGGING_ARCHITECTURE.md # остается без изменений
```

#### llm_router/router/custom_router.py:
```python
# СТАРЫЕ ССЫЛКИ:
# Архитектура роутера: docs/LLM_ROUTER_CODEBASE_ARCHITECTURE.md
# Потоки данных: docs/LLM_ROUTER_DATA_FLOW_ARCHITECTURE.md

# НОВЫЕ ССЫЛКИ:
# Архитектура роутера: docs/ARCHITECTURE.md#code-structure
# Потоки данных: docs/ARCHITECTURE.md#data-flows
```

### Ссылки из скриптов

#### run.sh:
```bash
# СТАРЫЕ ССЫЛКИ в комментариях:
# Полная документация команд: docs/COMMANDS_DOCUMENTATION.md
# Быстрый справочник: docs/COMMANDS_QUICK_REFERENCE.md
# Тестирование: docs/E2E_TESTING_GUIDE.md

# НОВЫЕ ССЫЛКИ:
# Полная документация команд: docs/OPERATIONS_GUIDE.md
# Быстрый справочник: docs/OPERATIONS_GUIDE.md#quick-reference
# Тестирование: docs/TESTING_GUIDE.md
```

#### scripts/analytics/main.sh:
```bash
# СТАРЫЕ ССЫЛКИ:
# Миграции: docs/ANALYTICS_MIGRATIONS.md
# Команды данных: docs/ANALYTICS_DATA_COMMANDS.md

# НОВЫЕ ССЫЛКИ:
# Миграции: docs/ANALYTICS_GUIDE.md#migrations
# Команды данных: docs/ANALYTICS_GUIDE.md#data-commands
```

#### entrypoint.sh:
```bash
# СТАРЫЕ ССЫЛКИ:
# Настройка: docs/ANALYTICS_INTEGRATION.md
# Команды: docs/COMMANDS_QUICK_REFERENCE.md

# НОВЫЕ ССЫЛКИ:
# Настройка: docs/ANALYTICS_GUIDE.md#setup
# Команды: docs/OPERATIONS_GUIDE.md#quick-reference
```

### Ссылки из конфигурационных файлов

#### docker-compose.yml:
```yaml
# СТАРЫЕ ССЫЛКИ в комментариях:
# Документация по настройке: docs/ANALYTICS_INTEGRATION.md
# Архитектура: docs/PROJECT_ARCHITECTURE.md

# НОВЫЕ ССЫЛКИ:
# Документация по настройке: docs/ANALYTICS_GUIDE.md#setup
# Архитектура: docs/ARCHITECTURE.md
```

#### .env.example:
```bash
# СТАРЫЕ ССЫЛКИ в комментариях:
# Настройка аналитики: docs/ANALYTICS_INTEGRATION.md
# Команды конфигурации: docs/COMMANDS_DOCUMENTATION.md

# НОВЫЕ ССЫЛКИ:
# Настройка аналитики: docs/ANALYTICS_GUIDE.md#setup
# Команды конфигурации: docs/OPERATIONS_GUIDE.md#configuration
```

#### litellm.config.yaml:
```yaml
# СТАРЫЕ ССЫЛКИ в комментариях:
# API справочник: docs/LITELLM_CONFIG_API_REFERENCE.md
# Архитектура роутера: docs/LLM_ROUTER_CODEBASE_ARCHITECTURE.md

# НОВЫЕ ССЫЛКИ:
# API справочник: docs/API_REFERENCE.md
# Архитектура роутера: docs/ARCHITECTURE.md#code-structure
```

### Ссылки из README файлов

#### Главный README.md проекта:
```markdown
# СТАРЫЕ ССЫЛКИ:
[Документация](./llm_router/docs/DOCUMENTATION_INDEX.md)
[Архитектура](./llm_router/docs/PROJECT_ARCHITECTURE.md)
[Команды](./llm_router/docs/COMMANDS_QUICK_REFERENCE.md)

# НОВЫЕ ССЫЛКИ:
[Документация](./llm_router/docs/README.md)
[Архитектура](./llm_router/docs/ARCHITECTURE.md)
[Команды](./llm_router/docs/OPERATIONS_GUIDE.md)
```

#### tests/README.md:
```markdown
# СТАРЫЕ ССЫЛКИ:
[E2E тестирование](../llm_router/docs/E2E_TESTING_GUIDE.md)
[Быстрые тесты](../llm_router/docs/QUICK_TEST_CHECKLIST.md)

# НОВЫЕ ССЫЛКИ:
[E2E тестирование](../llm_router/docs/TESTING_GUIDE.md#e2e-testing)
[Быстрые тесты](../llm_router/docs/TESTING_GUIDE.md#quick-validation)
```

---

## Якорные ссылки внутри файлов

### ARCHITECTURE.md (новый файл)

#### Основные якоря:
```markdown
# Якоря для внешних ссылок:
#system-overview          # Обзор системы
#components               # Компоненты системы
#code-structure           # Структура кода
#data-flows               # Потоки данных
#integration-patterns     # Паттерны интеграции
#ml-predictor             # ML предиктор
#gpustack-integration     # GPUStack интеграция
#analytics-system         # Система аналитики
```

#### Внутренние ссылки:
```markdown
# Ссылки внутри ARCHITECTURE.md:
[Компоненты системы](#components)
[Потоки данных](#data-flows)
[ML предиктор](#ml-predictor)
[GPUStack интеграция](#gpustack-integration)
```

### ANALYTICS_GUIDE.md (новый файл)

#### Основные якоря:
```markdown
# Якоря для внешних ссылок:
#overview                 # Обзор аналитики
#setup                    # Настройка и конфигурация
#migrations               # Миграции базы данных
#data-commands            # Команды работы с данными
#prisma-commands          # Prisma команды
#reports                  # Отчеты и анализ
#troubleshooting          # Устранение неполадок
#quick-reference          # Быстрый справочник
```

#### Внутренние ссылки:
```markdown
# Ссылки внутри ANALYTICS_GUIDE.md:
[Настройка](#setup)
[Миграции](#migrations)
[Отчеты](#reports)
[Устранение неполадок](#troubleshooting)
```

### OPERATIONS_GUIDE.md (новый файл)

#### Основные якоря:
```markdown
# Якоря для внешних ссылок:
#installation             # Установка и настройка
#docker-commands          # Управление Docker
#configuration            # Конфигурация
#monitoring               # Мониторинг и отладка
#quick-reference          # Быстрый справочник
#usage-scenarios          # Сценарии использования
```

#### Внутренние ссылки:
```markdown
# Ссылки внутри OPERATIONS_GUIDE.md:
[Установка](#installation)
[Docker команды](#docker-commands)
[Конфигурация](#configuration)
[Быстрый справочник](#quick-reference)
```

### TESTING_GUIDE.md (новый файл)

#### Основные якоря:
```markdown
# Якоря для внешних ссылок:
#testing-strategy         # Стратегия тестирования
#quick-validation         # Быстрая валидация
#comprehensive-testing    # Комплексное тестирование
#e2e-testing              # E2E тестирование
#docker-testing           # Docker тестирование
#automated-testing        # Автоматизированное тестирование
#navigation               # Навигация по тестам
```

#### Внутренние ссылки:
```markdown
# Ссылки внутри TESTING_GUIDE.md:
[Быстрая валидация](#quick-validation)
[E2E тестирование](#e2e-testing)
[Docker тестирование](#docker-testing)
[Автоматизированное тестирование](#automated-testing)
```

### API_REFERENCE.md (новый файл)

#### Основные якоря:
```markdown
# Якоря для внешних ссылок:
#api-overview             # Обзор API
#litellm-config-api       # LiteLLM Configuration API
#ml-predictor-api         # ML Predictor API
#analytics-api            # Analytics API
#data-formats             # Форматы данных
#usage-examples           # Примеры использования
```

### TROUBLESHOOTING.md (новый файл)

#### Основные якоря:
```markdown
# Якоря для внешних ссылок:
#common-issues            # Общие проблемы
#docker-issues            # Проблемы Docker
#database-issues          # Проблемы базы данных
#integration-issues       # Проблемы интеграций
#testing-issues           # Проблемы тестирования
#diagnostic-commands      # Диагностические команды
```

### MIGRATION_GUIDE.md (новый файл)

#### Основные якоря:
```markdown
# Якоря для внешних ссылок:
#migration-overview       # Обзор миграций
#system-migrations        # Миграции системы
#analytics-migrations     # Миграции аналитики
#rollback-procedures      # Процедуры отката
#backup-procedures        # Резервное копирование
```

---

## Автоматизация обновления ссылок

### Скрипт для поиска и замены ссылок

```bash
#!/bin/bash
# update_links.sh - Скрипт для автоматического обновления ссылок

# Массив замен: "старая_ссылка|новая_ссылка"
declare -a LINK_REPLACEMENTS=(
    "DOCUMENTATION_INDEX.md|README.md"
    "PROJECT_ARCHITECTURE.md|ARCHITECTURE.md"
    "LLM_ROUTER_CODEBASE_ARCHITECTURE.md|ARCHITECTURE.md#code-structure"
    "LLM_ROUTER_DATA_FLOW_ARCHITECTURE.md|ARCHITECTURE.md#data-flows"
    "COMMANDS_DOCUMENTATION.md|OPERATIONS_GUIDE.md"
    "COMMANDS_QUICK_REFERENCE.md|OPERATIONS_GUIDE.md#quick-reference"
    "ANALYTICS_INTEGRATION.md|ANALYTICS_GUIDE.md#overview"
    "ANALYTICS_MIGRATIONS.md|ANALYTICS_GUIDE.md#migrations"
    "ANALYTICS_MIGRATIONS_COMPLETE_GUIDE.md|ANALYTICS_GUIDE.md#setup"
    "ANALYTICS_MIGRATIONS_CHEATSHEET.md|ANALYTICS_GUIDE.md#quick-reference"
    "ANALYTICS_REPORTS_GUIDE.md|ANALYTICS_GUIDE.md#reports"
    "ANALYTICS_DATA_COMMANDS.md|ANALYTICS_GUIDE.md#data-commands"
    "ANALYTICS_PRISMA_COMMANDS.md|ANALYTICS_GUIDE.md#prisma-commands"
    "E2E_TESTING_GUIDE.md|TESTING_GUIDE.md#e2e-testing"
    "E2E_TESTING_DOCKER_ARCHITECTURE.md|TESTING_GUIDE.md#docker-testing"
    "TESTING_NAVIGATOR.md|TESTING_GUIDE.md#navigation"
    "QUICK_TEST_CHECKLIST.md|TESTING_GUIDE.md#quick-validation"
    "LITELLM_CONFIG_API_REFERENCE.md|API_REFERENCE.md"
    "LLM_TIME_PREDICTOR_DATA_FORMAT.MD|API_REFERENCE.md#data-formats"
    "MIGRATION_DIRECT_COMMANDS.md|MIGRATION_GUIDE.md"
)

# Функция для обновления ссылок в файле
update_links_in_file() {
    local file="$1"
    echo "Обновление ссылок в файле: $file"
    
    for replacement in "${LINK_REPLACEMENTS[@]}"; do
        old_link=$(echo "$replacement" | cut -d'|' -f1)
        new_link=$(echo "$replacement" | cut -d'|' -f2)
        
        # Замена ссылок в формате [text](./old_link)
        sed -i "s|\](\./$old_link)|\](\./$new_link)|g" "$file"
        
        # Замена ссылок в формате [text](old_link)
        sed -i "s|\]($old_link)|\]($new_link)|g" "$file"
    done
}

# Обновление ссылок во всех файлах проекта
find . -name "*.md" -type f | while read -r file; do
    update_links_in_file "$file"
done

find . -name "*.py" -type f | while read -r file; do
    update_links_in_file "$file"
done

find . -name "*.sh" -type f | while read -r file; do
    update_links_in_file "$file"
done

echo "Обновление ссылок завершено"
```

### Скрипт для проверки ссылок

```bash
#!/bin/bash
# check_links.sh - Скрипт для проверки корректности ссылок

echo "Проверка внутренних ссылок документации..."

# Проверка существования файлов, на которые ссылаются
find llm_router/docs -name "*.md" -type f | while read -r file; do
    echo "Проверка файла: $file"
    
    # Извлечение всех ссылок на .md файлы
    grep -o '\[.*\](\.\/.*\.md[#a-zA-Z0-9_-]*)' "$file" | while read -r link; do
        # Извлечение пути к файлу
        file_path=$(echo "$link" | sed 's/.*](\.\///' | sed 's/#.*//')
        full_path="llm_router/docs/$file_path"
        
        if [ ! -f "$full_path" ]; then
            echo "❌ ОШИБКА: Файл не найден: $full_path (ссылка из $file)"
        else
            echo "✅ OK: $full_path"
        fi
    done
done

echo "Проверка ссылок завершена"
```

---

## Чек-лист обновления ссылок

### Этап 1: Подготовка
- [ ] Создать резервную копию всех файлов
- [ ] Подготовить скрипты автоматизации
- [ ] Составить полный список файлов для обновления

### Этап 2: Обновление внутренних ссылок
- [ ] Обновить ссылки в README.md
- [ ] Обновить ссылки в новых консолидированных файлах
- [ ] Проверить все якорные ссылки

### Этап 3: Обновление внешних ссылок
- [ ] Обновить ссылки в Python файлах
- [ ] Обновить ссылки в скриптах
- [ ] Обновить ссылки в конфигурационных файлах
- [ ] Обновить ссылки в README файлах

### Этап 4: Проверка
- [ ] Запустить скрипт проверки ссылок
- [ ] Проверить все ссылки вручную
- [ ] Протестировать навигацию по документации

### Этап 5: Финализация
- [ ] Добавить переадресации в устаревшие файлы
- [ ] Обновить внешние ссылки и закладки
- [ ] Документировать изменения

---

*Карта обновления создана: 2025-07-29*
*Основа: Анализ всех перекрестных ссылок в 24 файлах документации*
*Цель: Обеспечить корректность всех ссылок после консолидации*