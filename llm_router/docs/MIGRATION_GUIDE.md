# Руководство по миграции

Данное руководство описывает процедуры обновления и миграции системы LLM Router, включая обновление версий, миграции базы данных и изменения конфигурации.

## Обзор процесса миграции

### Типы миграций

1. **Миграции базы данных** - изменения схемы PostgreSQL
2. **Миграции конфигурации** - обновления формата конфигурационных файлов
3. **Миграции кода** - обновления версий системы
4. **Миграции данных** - преобразование существующих данных

### Стратегия миграции

```mermaid
graph TB
    subgraph "Подготовка"
        A[Резервное копирование]
        B[Проверка совместимости]
        C[Планирование downtime]
    end
    
    subgraph "Миграция"
        D[Остановка сервисов]
        E[Обновление кода]
        F[Миграция БД]
        G[Миграция конфигурации]
        H[Миграция данных]
    end
    
    subgraph "Проверка"
        I[Запуск сервисов]
        J[Тестирование]
        K[Мониторинг]
    end
    
    subgraph "Откат (при необходимости)"
        L[Остановка сервисов]
        M[Восстановление БД]
        N[Восстановление кода]
        O[Запуск старой версии]
    end
    
    A --> B --> C --> D
    D --> E --> F --> G --> H
    H --> I --> J --> K
    
    J --> L
    L --> M --> N --> O
    
    style A fill:#e8f5e8
    style F fill:#fff3e0
    style J fill:#e1f5fe
    style L fill:#fce4ec
```

## Миграции базы данных

### Управление миграциями

#### Структура миграций

```
analytics/migrations/
├── 20240101_001_initial_schema.sql
├── 20240115_002_add_prediction_accuracy.sql
├── 20240201_003_add_cache_metrics.sql
├── 20240215_004_optimize_indexes.sql
└── 20240301_005_add_partitioning.sql
```

#### Формат файла миграции

```sql
-- analytics/migrations/20240215_004_optimize_indexes.sql
-- Description: Оптимизация индексов для улучшения производительности
-- Author: LLM Router Team
-- Date: 2024-02-15

-- Up migration
BEGIN;

-- Добавление составного индекса для частых запросов
CREATE INDEX CONCURRENTLY idx_routing_decisions_deployment_created 
ON routing_decisions(selected_deployment, created_at DESC);

-- Добавление частичного индекса для успешных запросов
CREATE INDEX CONCURRENTLY idx_routing_decisions_success 
ON routing_decisions(created_at DESC) 
WHERE success = true;

-- Оптимизация индекса для аналитических запросов
DROP INDEX IF EXISTS idx_routing_decisions_created_at;
CREATE INDEX CONCURRENTLY idx_routing_decisions_analytics 
ON routing_decisions(created_at DESC, model_name, selected_deployment);

COMMIT;

-- Down migration (для отката)
-- BEGIN;
-- DROP INDEX IF EXISTS idx_routing_decisions_deployment_created;
-- DROP INDEX IF EXISTS idx_routing_decisions_success;
-- DROP INDEX IF EXISTS idx_routing_decisions_analytics;
-- CREATE INDEX idx_routing_decisions_created_at ON routing_decisions(created_at);
-- COMMIT;
```

### Команды миграции

#### Применение миграций

```bash
# Просмотр статуса миграций
./run.sh analytics migrate status

# Применение всех новых миграций
./run.sh analytics migrate up

# Применение до конкретной миграции
./run.sh analytics migrate up --target 20240215_004_optimize_indexes

# Применение одной миграции
./run.sh analytics migrate up --steps 1

# Проверка миграций без применения (dry-run)
./run.sh analytics migrate up --dry-run
```

#### Откат миграций

```bash
# Откат последней миграции
./run.sh analytics migrate down

# Откат до конкретной миграции
./run.sh analytics migrate down --target 20240201_003_add_cache_metrics

# Откат нескольких миграций
./run.sh analytics migrate down --steps 2

# Проверка отката без применения
./run.sh analytics migrate down --dry-run
```

#### Создание новых миграций

```bash
# Создание новой миграции
./run.sh analytics migrate create add_user_metrics_table

# Создание миграции с описанием
./run.sh analytics migrate create "optimize_query_performance" --description "Добавление индексов для оптимизации запросов"
```

### Безопасные миграции

#### Принципы безопасных миграций

1. **Обратная совместимость** - новая схема должна работать со старым кодом
2. **Постепенные изменения** - избегать больших изменений в одной миграции
3. **Неблокирующие операции** - использовать CONCURRENTLY для индексов
4. **Тестирование** - проверять миграции на копии продакшн данных

#### Пример безопасной миграции добавления колонки

```sql
-- Безопасное добавление колонки
-- analytics/migrations/20240301_006_add_request_priority.sql

BEGIN;

-- Добавление колонки с значением по умолчанию
ALTER TABLE routing_decisions 
ADD COLUMN request_priority INTEGER DEFAULT 1;

-- Добавление комментария
COMMENT ON COLUMN routing_decisions.request_priority 
IS 'Приоритет запроса: 1=низкий, 2=средний, 3=высокий';

-- Добавление индекса (неблокирующе)
COMMIT;

-- Создание индекса отдельно (неблокирующая операция)
CREATE INDEX CONCURRENTLY idx_routing_decisions_priority 
ON routing_decisions(request_priority, created_at DESC);
```

#### Пример безопасного удаления колонки

```sql
-- Безопасное удаление колонки (двухэтапный процесс)
-- Этап 1: analytics/migrations/20240315_007_deprecate_old_column.sql

BEGIN;

-- Добавление комментария о deprecation
COMMENT ON COLUMN routing_decisions.old_column 
IS 'DEPRECATED: Будет удалена в версии 2.1.0';

-- Создание представления без deprecated колонки
CREATE OR REPLACE VIEW routing_decisions_current AS
SELECT 
    id, created_at, request_id, selected_deployment,
    ml_prediction_time, actual_response_time, hybrid_score
    -- Исключаем old_column
FROM routing_decisions;

COMMIT;

-- Этап 2 (в следующей версии): физическое удаление колонки
-- ALTER TABLE routing_decisions DROP COLUMN old_column;
```

### Миграции с большими данными

#### Стратегии для больших таблиц

```sql
-- Миграция больших таблиц с батчингом
-- analytics/migrations/20240401_008_update_large_table.sql

DO $$
DECLARE
    batch_size INTEGER := 10000;
    processed INTEGER := 0;
    total_rows INTEGER;
BEGIN
    -- Получение общего количества строк
    SELECT COUNT(*) INTO total_rows 
    FROM routing_decisions 
    WHERE some_condition = true;
    
    RAISE NOTICE 'Обновление % строк батчами по %', total_rows, batch_size;
    
    -- Обновление батчами
    LOOP
        UPDATE routing_decisions 
        SET new_column = calculate_new_value(old_column)
        WHERE id IN (
            SELECT id 
            FROM routing_decisions 
            WHERE some_condition = true 
              AND new_column IS NULL
            LIMIT batch_size
        );
        
        GET DIAGNOSTICS processed = ROW_COUNT;
        
        IF processed = 0 THEN
            EXIT;
        END IF;
        
        RAISE NOTICE 'Обработано % строк', processed;
        
        -- Пауза для снижения нагрузки
        PERFORM pg_sleep(0.1);
    END LOOP;
    
    RAISE NOTICE 'Миграция завершена';
END $$;
```

## Миграции конфигурации

### Версионирование конфигурации

#### Структура версий конфигурации

```yaml
# litellm.config.yaml
version: "2.1.0"  # Версия формата конфигурации

model_list:
  - model_name: "chat_instruct"
    litellm_params:
      model: "openai/gpt-4"
      api_base: "https://api.openai.com/v1"
      api_key: "os.environ/OPENAI_API_KEY"
    model_info:
      mode: "chat"
      supports_function_calling: true
      max_tokens: 4096
      # Новые поля в версии 2.1.0
      priority: 1
      cost_per_token: 0.00003

router_settings:
  routing_strategy: "llm-time-predictor"
  routing_strategy_args:
    # Обновленные настройки в версии 2.1.0
    predictor_url: "os.environ/PREDICT_URL"
    gpustack_url: "os.environ/GPUSTACK_URL"
    hybrid_weight: 0.3
    cache_ttl: 300
    fallback_strategy: "least-busy"
    # Новые настройки
    enable_request_priority: true
    priority_weight: 0.1
```

### Автоматическая миграция конфигурации

```python
# config/config_migrator.py
class ConfigMigrator:
    """Автоматическая миграция конфигурационных файлов"""
    
    MIGRATIONS = {
        "2.0.0": "migrate_to_2_0_0",
        "2.1.0": "migrate_to_2_1_0",
    }
    
    def migrate_config(self, config_path: str, target_version: str):
        """Миграция конфигурации до целевой версии"""
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        current_version = config.get('version', '1.0.0')
        
        # Применение миграций по порядку
        for version in sorted(self.MIGRATIONS.keys()):
            if self._version_greater(version, current_version):
                if self._version_less_or_equal(version, target_version):
                    migration_method = getattr(self, self.MIGRATIONS[version])
                    config = migration_method(config)
                    config['version'] = version
                    print(f"Применена миграция конфигурации до версии {version}")
        
        # Создание резервной копии
        backup_path = f"{config_path}.backup.{int(time.time())}"
        shutil.copy2(config_path, backup_path)
        print(f"Создана резервная копия: {backup_path}")
        
        # Сохранение обновленной конфигурации
        with open(config_path, 'w') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
        
        return config
    
    def migrate_to_2_1_0(self, config: dict) -> dict:
        """Миграция конфигурации до версии 2.1.0"""
        # Добавление новых полей в model_list
        for model in config.get('model_list', []):
            if 'model_info' not in model:
                model['model_info'] = {}
            
            # Добавление приоритета по умолчанию
            if 'priority' not in model['model_info']:
                model['model_info']['priority'] = 1
            
            # Добавление стоимости по умолчанию
            if 'cost_per_token' not in model['model_info']:
                model['model_info']['cost_per_token'] = 0.00003
        
        # Обновление router_settings
        router_settings = config.setdefault('router_settings', {})
        routing_args = router_settings.setdefault('routing_strategy_args', {})
        
        # Добавление новых настроек приоритизации
        if 'enable_request_priority' not in routing_args:
            routing_args['enable_request_priority'] = True
        
        if 'priority_weight' not in routing_args:
            routing_args['priority_weight'] = 0.1
        
        return config
```

### Команды миграции конфигурации

```bash
# Проверка версии конфигурации
./run.sh config version

# Миграция конфигурации до последней версии
./run.sh config migrate

# Миграция до конкретной версии
./run.sh config migrate --target 2.1.0

# Проверка миграции без применения
./run.sh config migrate --dry-run

# Валидация конфигурации после миграции
./run.sh config validate --strict
```

## Обновление версий системы

### Процедура обновления

#### Подготовка к обновлению

```bash
#!/bin/bash
# pre-upgrade.sh - подготовка к обновлению

echo "=== Подготовка к обновлению LLM Router ==="

# 1. Проверка текущей версии
echo "Текущая версия:"
./run.sh --version

# 2. Создание полной резервной копии
echo "Создание резервной копии..."
./run.sh config backup
./run.sh analytics backup create pre_upgrade_$(date +%Y%m%d_%H%M%S)

# 3. Проверка здоровья системы
echo "Проверка здоровья системы..."
./run.sh monitor health || {
    echo "ВНИМАНИЕ: Система не в здоровом состоянии!"
    echo "Рекомендуется устранить проблемы перед обновлением."
    exit 1
}

# 4. Проверка свободного места
echo "Проверка свободного места..."
AVAILABLE_SPACE=$(df / | awk 'NR==2 {print $4}')
REQUIRED_SPACE=1048576  # 1GB в KB

if [ "$AVAILABLE_SPACE" -lt "$REQUIRED_SPACE" ]; then
    echo "ОШИБКА: Недостаточно свободного места для обновления"
    echo "Доступно: ${AVAILABLE_SPACE}KB, требуется: ${REQUIRED_SPACE}KB"
    exit 1
fi

# 5. Проверка активных соединений
echo "Проверка активных соединений..."
ACTIVE_CONNECTIONS=$(./run.sh monitor resources | grep "active_requests" | awk '{sum+=$2} END {print sum}')

if [ "$ACTIVE_CONNECTIONS" -gt 100 ]; then
    echo "ВНИМАНИЕ: Высокая нагрузка ($ACTIVE_CONNECTIONS активных соединений)"
    echo "Рекомендуется дождаться снижения нагрузки"
fi

echo "✓ Подготовка к обновлению завершена"
echo "Можно приступать к обновлению"
```

#### Процедура обновления

```bash
#!/bin/bash
# upgrade.sh - основная процедура обновления

set -e  # Остановка при любой ошибке

NEW_VERSION="$1"
if [ -z "$NEW_VERSION" ]; then
    echo "Использование: $0 <новая_версия>"
    exit 1
fi

echo "=== Обновление LLM Router до версии $NEW_VERSION ==="

# 1. Остановка сервисов
echo "1. Остановка сервисов..."
./run.sh docker down

# 2. Обновление кода
echo "2. Обновление кода..."
git fetch origin
git checkout "v$NEW_VERSION"

# 3. Обновление зависимостей
echo "3. Обновление зависимостей..."
./run.sh update dependencies

# 4. Миграция конфигурации
echo "4. Миграция конфигурации..."
./run.sh config migrate --target "$NEW_VERSION"

# 5. Сборка новых образов
echo "5. Сборка образов..."
./run.sh docker build --no-cache

# 6. Запуск БД для миграций
echo "6. Запуск базы данных..."
./run.sh docker up -d db
sleep 30

# 7. Применение миграций БД
echo "7. Применение миграций базы данных..."
./run.sh analytics migrate up

# 8. Запуск всех сервисов
echo "8. Запуск сервисов..."
./run.sh docker up -d

# 9. Ожидание готовности
echo "9. Ожидание готовности системы..."
sleep 60

# 10. Проверка работоспособности
echo "10. Проверка работоспособности..."
./run.sh test endpoint || {
    echo "ОШИБКА: Система не прошла проверку работоспособности!"
    echo "Запуск процедуры отката..."
    ./rollback.sh
    exit 1
}

echo "✓ Обновление до версии $NEW_VERSION завершено успешно"
```

#### Процедура отката

```bash
#!/bin/bash
# rollback.sh - откат к предыдущей версии

echo "=== Откат LLM Router к предыдущей версии ==="

# 1. Остановка сервисов
echo "1. Остановка сервисов..."
./run.sh docker down

# 2. Восстановление кода
echo "2. Восстановление кода..."
PREVIOUS_VERSION=$(git describe --tags --abbrev=0 HEAD~1)
git checkout "$PREVIOUS_VERSION"

# 3. Восстановление конфигурации
echo "3. Восстановление конфигурации..."
LATEST_CONFIG_BACKUP=$(ls -t config_backups/*.tar.gz | head -1)
if [ -n "$LATEST_CONFIG_BACKUP" ]; then
    ./run.sh config restore "$LATEST_CONFIG_BACKUP"
fi

# 4. Откат миграций БД
echo "4. Откат миграций базы данных..."
./run.sh docker up -d db
sleep 30

# Определение целевой миграции для отката
TARGET_MIGRATION=$(./run.sh analytics migrate status | grep "$PREVIOUS_VERSION" | tail -1 | awk '{print $1}')
if [ -n "$TARGET_MIGRATION" ]; then
    ./run.sh analytics migrate down --target "$TARGET_MIGRATION"
fi

# 5. Пересборка образов
echo "5. Пересборка образов..."
./run.sh docker build --no-cache

# 6. Запуск сервисов
echo "6. Запуск сервисов..."
./run.sh docker up -d

# 7. Проверка работоспособности
echo "7. Проверка работоспособности..."
sleep 60
./run.sh test endpoint

echo "✓ Откат к версии $PREVIOUS_VERSION завершен"
```

### Автоматизированное обновление

#### CI/CD пайплайн для обновлений

```yaml
# .github/workflows/upgrade.yml
name: Automated Upgrade

on:
  release:
    types: [published]

jobs:
  upgrade-staging:
    runs-on: ubuntu-latest
    environment: staging
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          ref: ${{ github.event.release.tag_name }}
      
      - name: Deploy to staging
        run: |
          # Подключение к staging серверу
          ssh staging-server << 'EOF'
            cd /opt/llm_router
            ./pre-upgrade.sh
            ./upgrade.sh ${{ github.event.release.tag_name }}
          EOF
      
      - name: Run integration tests
        run: |
          ssh staging-server << 'EOF'
            cd /opt/llm_router
            ./run.sh test integration
          EOF
      
      - name: Notify on failure
        if: failure()
        run: |
          ssh staging-server << 'EOF'
            cd /opt/llm_router
            ./rollback.sh
          EOF

  upgrade-production:
    needs: upgrade-staging
    runs-on: ubuntu-latest
    environment: production
    
    steps:
      - name: Manual approval
        uses: trstringer/manual-approval@v1
        with:
          secret: ${{ github.TOKEN }}
          approvers: admin-team
          minimum-approvals: 2
      
      - name: Deploy to production
        run: |
          # Подключение к production серверу
          ssh production-server << 'EOF'
            cd /opt/llm_router
            ./pre-upgrade.sh
            ./upgrade.sh ${{ github.event.release.tag_name }}
          EOF
```

## Миграция данных

### Миграция аналитических данных

#### Экспорт данных

```bash
# Экспорт данных перед миграцией
./run.sh analytics data export \
  --format parquet \
  --from "2024-01-01T00:00:00Z" \
  --to "2024-12-31T23:59:59Z" \
  --output migration_data_$(date +%Y%m%d).parquet

# Экспорт конкретных таблиц
./run.sh analytics data export \
  --table routing_decisions \
  --format csv \
  --output routing_decisions_backup.csv

./run.sh analytics data export \
  --table deployment_metrics \
  --format json \
  --output deployment_metrics_backup.json
```

#### Трансформация данных

```python
# data_transformer.py - трансформация данных при миграции
import pandas as pd
import json
from datetime import datetime

class DataTransformer:
    """Трансформация данных между версиями"""
    
    def transform_routing_decisions_v2_to_v3(self, input_file: str, output_file: str):
        """Трансформация routing_decisions из версии 2.0 в 3.0"""
        
        # Загрузка данных
        if input_file.endswith('.parquet'):
            df = pd.read_parquet(input_file)
        elif input_file.endswith('.csv'):
            df = pd.read_csv(input_file)
        else:
            raise ValueError("Неподдерживаемый формат файла")
        
        # Трансформации для версии 3.0
        
        # 1. Добавление новых колонок
        df['request_priority'] = 1  # Приоритет по умолчанию
        df['cost_estimate'] = df['actual_response_time'] * 0.00003  # Оценка стоимости
        
        # 2. Преобразование существующих данных
        df['hybrid_score_v3'] = self._recalculate_hybrid_score(df)
        
        # 3. Обновление метаданных
        if 'metadata' in df.columns:
            df['metadata'] = df['metadata'].apply(self._update_metadata_format)
        
        # 4. Валидация данных
        df = self._validate_transformed_data(df)
        
        # Сохранение результата
        if output_file.endswith('.parquet'):
            df.to_parquet(output_file, index=False)
        elif output_file.endswith('.csv'):
            df.to_csv(output_file, index=False)
        
        print(f"Трансформировано {len(df)} записей")
        return df
    
    def _recalculate_hybrid_score(self, df: pd.DataFrame) -> pd.Series:
        """Пересчет гибридного балла для новой версии"""
        # Новая формула учитывает приоритет запроса
        ml_weight = 0.6  # Уменьшен с 0.7
        load_weight = 0.3
        priority_weight = 0.1  # Новый компонент
        
        normalized_ml = df['ml_prediction_time'] / 10.0
        normalized_load = df['load_penalty']
        normalized_priority = (4 - df['request_priority']) / 3.0  # Инвертированный приоритет
        
        return (ml_weight * normalized_ml + 
                load_weight * normalized_load + 
                priority_weight * normalized_priority)
    
    def _update_metadata_format(self, metadata_str: str) -> str:
        """Обновление формата метаданных"""
        try:
            metadata = json.loads(metadata_str) if isinstance(metadata_str, str) else metadata_str
            
            # Добавление новых полей метаданных
            metadata['migration_version'] = '3.0'
            metadata['migration_timestamp'] = datetime.utcnow().isoformat()
            
            # Обновление существующих полей
            if 'performance' in metadata:
                metadata['performance']['version'] = '3.0'
            
            return json.dumps(metadata)
        except (json.JSONDecodeError, TypeError):
            return metadata_str
    
    def _validate_transformed_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Валидация трансформированных данных"""
        # Проверка обязательных полонок
        required_columns = [
            'id', 'created_at', 'request_id', 'selected_deployment',
            'request_priority', 'cost_estimate', 'hybrid_score_v3'
        ]
        
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            raise ValueError(f"Отсутствуют обязательные колонки: {missing_columns}")
        
        # Проверка диапазонов значений
        df = df[df['request_priority'].between(1, 3)]
        df = df[df['cost_estimate'] >= 0]
        df = df[df['hybrid_score_v3'] >= 0]
        
        return df

# Использование трансформера
if __name__ == "__main__":
    transformer = DataTransformer()
    transformer.transform_routing_decisions_v2_to_v3(
        'routing_decisions_v2.parquet',
        'routing_decisions_v3.parquet'
    )
```

#### Импорт трансформированных данных

```bash
# Импорт трансформированных данных
./run.sh analytics data import \
  --file routing_decisions_v3.parquet \
  --table routing_decisions \
  --mode replace

# Импорт с валидацией
./run.sh analytics data import \
  --file deployment_metrics_v3.json \
  --table deployment_metrics \
  --mode append \
  --validate

# Проверка импортированных данных
./run.sh analytics data validate --table routing_decisions
```

### Миграция конфигурационных данных

#### Миграция настроек моделей

```python
# model_config_migrator.py
class ModelConfigMigrator:
    """Миграция конфигурации моделей"""
    
    def migrate_model_configs_v2_to_v3(self, config_file: str):
        """Миграция конфигурации моделей из версии 2.0 в 3.0"""
        
        with open(config_file, 'r') as f:
            config = yaml.safe_load(f)
        
        # Миграция model_list
        for model in config.get('model_list', []):
            # Добавление новых полей
            model_info = model.setdefault('model_info', {})
            
            # Миграция приоритета на основе типа модели
            if 'priority' not in model_info:
                model_name = model.get('model_name', '')
                if 'code' in model_name.lower():
                    model_info['priority'] = 2  # Высокий приоритет для кода
                elif 'chat' in model_name.lower():
                    model_info['priority'] = 1  # Обычный приоритет для чата
                else:
                    model_info['priority'] = 1
            
            # Добавление стоимости на основе модели
            if 'cost_per_token' not in model_info:
                litellm_model = model.get('litellm_params', {}).get('model', '')
                model_info['cost_per_token'] = self._estimate_cost_per_token(litellm_model)
            
            # Добавление лимитов производительности
            if 'performance_limits' not in model_info:
                model_info['performance_limits'] = {
                    'max_tokens_per_minute': 10000,
                    'max_requests_per_minute': 100,
                    'max_concurrent_requests': 10
                }
        
        # Обновление router_settings
        router_settings = config.setdefault('router_settings', {})
        routing_args = router_settings.setdefault('routing_strategy_args', {})
        
        # Новые настройки для версии 3.0
        routing_args.update({
            'enable_cost_optimization': True,
            'cost_weight': 0.1,
            'enable_performance_limits': True,
            'priority_boost_factor': 1.5
        })
        
        # Сохранение обновленной конфигурации
        backup_file = f"{config_file}.backup.{int(time.time())}"
        shutil.copy2(config_file, backup_file)
        
        with open(config_file, 'w') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
        
        print(f"Конфигурация моделей мигрирована, резервная копия: {backup_file}")
        return config
    
    def _estimate_cost_per_token(self, model: str) -> float:
        """Оценка стоимости за токен на основе модели"""
        cost_mapping = {
            'gpt-4': 0.00003,
            'gpt-3.5-turbo': 0.000002,
            'claude-3': 0.000015,
            'llama': 0.000001,
        }
        
        for model_key, cost in cost_mapping.items():
            if model_key in model.lower():
                return cost
        
        return 0.00001  # Значение по умолчанию
```

## Тестирование миграций

### Тестовая среда для миграций

```bash
#!/bin/bash
# setup-migration-test.sh - настройка тестовой среды

echo "=== Настройка тестовой среды для миграций ==="

# 1. Создание тестовой БД
docker run -d --name migration-test-db \
  -e POSTGRES_DB=litellm_migration_test \
  -e POSTGRES_USER=test \
  -e POSTGRES_PASSWORD=test \
  -p 5434:5432 \
  postgres:16

# 2. Копирование продакшн данных (образец)
echo "Копирование образца продакшн данных..."
pg_dump -h production-db -U llmproxy litellm \
  --data-only \
  --table=routing_decisions \
  --where="created_at >= NOW() - INTERVAL '7 days'" \
  | psql -h localhost -p 5434 -U test litellm_migration_test

# 3. Настройка тестовой конфигурации
cp .env .env.migration_test
sed -i 's/5432/5434/g' .env.migration_test
sed -i 's/litellm/litellm_migration_test/g' .env.migration_test

echo "✓ Тестовая среда готова"
echo "Используйте DATABASE_URL=postgresql://test:test@localhost:5434/litellm_migration_test"
```

### Автоматизированное тестирование миграций

```python
# test_migrations.py
import pytest
import psycopg2
import yaml
from datetime import datetime, timedelta

class TestMigrations:
    """Тесты миграций базы данных"""
    
    @pytest.fixture
    def test_db_connection(self):
        """Подключение к тестовой БД"""
        conn = psycopg2.connect(
            host='localhost',
            port=5434,
            database='litellm_migration_test',
            user='test',
            password='test'
        )
        yield conn
        conn.close()
    
    def test_migration_up_and_down(self, test_db_connection):
        """Тест применения и отката миграции"""
        cursor = test_db_connection.cursor()
        
        # Получение текущей версии схемы
        cursor.execute("SELECT version FROM schema_migrations ORDER BY version DESC LIMIT 1")
        current_version = cursor.fetchone()[0]
        
        # Применение следующей миграции
        next_migration = self._get_next_migration(current_version)
        if next_migration:
            self._apply_migration(cursor, next_migration)
            
            # Проверка успешного применения
            cursor.execute("SELECT version FROM schema_migrations WHERE version = %s", (next_migration,))
            assert cursor.fetchone() is not None
            
            # Откат миграции
            self._rollback_migration(cursor, next_migration)
            
            # Проверка успешного отката
            cursor.execute("SELECT version FROM schema_migrations WHERE version = %s", (next_migration,))
            assert cursor.fetchone() is None
    
    def test_data_integrity_after_migration(self, test_db_connection):
        """Тест целостности данных после миграции"""
        cursor = test_db_connection.cursor()
        
        # Подсчет записей до миграции
        cursor.execute("SELECT COUNT(*) FROM routing_decisions")
        count_before = cursor.fetchone()[0]
        
        # Применение миграции
        latest_migration = self._get_latest_migration()
        self._apply_migration(cursor, latest_migration)
        
        # Подсчет записей после миграции
        cursor.execute("SELECT COUNT(*) FROM routing_decisions")
        count_after = cursor.fetchone()[0]
        
        # Проверка, что данные не потеряны
        assert count_after >= count_before
        
        # Проверка валидности данных
        cursor.execute("""
            SELECT COUNT(*) FROM routing_decisions 
            WHERE request_id IS NULL OR selected_deployment IS NULL
        """)
        invalid_records = cursor.fetchone()[0]
        assert invalid_records == 0
    
    def test_performance_after_migration(self, test_db_connection):
        """Тест производительности после миграции"""
        cursor = test_db_connection.cursor()
        
        # Тест производительности основных запросов
        test_queries = [
            "SELECT * FROM routing_decisions WHERE created_at >= NOW() - INTERVAL '1 day' LIMIT 1000",
            "SELECT selected_deployment, COUNT(*) FROM routing_decisions GROUP BY selected_deployment",
            "SELECT AVG(actual_response_time) FROM routing_decisions WHERE success = true"
        ]
        
        for query in test_queries:
            start_time = datetime.now()
            cursor.execute(query)
            cursor.fetchall()
            execution_time = (datetime.now() - start_time).total_seconds()
            
            # Проверка, что запрос выполняется быстро
            assert execution_time < 5.0, f"Медленный запрос: {query} ({execution_time}s)"

# Запуск тестов миграций
if __name__ == "__main__":
    pytest.main([__file__, "-v"])
```

## Мониторинг миграций

### Логирование процесса миграции

```python
# migration_logger.py
import logging
import json
from datetime import datetime

class MigrationLogger:
    """Логирование процесса миграций"""
    
    def __init__(self, log_file: str = "migration.log"):
        self.logger = logging.getLogger("migration")
        self.logger.setLevel(logging.INFO)
        
        # Настройка обработчика файла
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(logging.INFO)
        
        # Настройка форматирования
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        
        self.logger.addHandler(file_handler)
    
    def log_migration_start(self, migration_name: str, migration_type: str):
        """Логирование начала миграции"""
        self.logger.info(json.dumps({
            'event': 'migration_start',
            'migration_name': migration_name,
            'migration_type': migration_type,
            'timestamp': datetime.utcnow().isoformat()
        }))
    
    def log_migration_success(self, migration_name: str, duration: float):
        """Логирование успешного завершения миграции"""
        self.logger.info(json.dumps({
            'event': 'migration_success',
            'migration_name': migration_name,
            'duration_seconds': duration,
            'timestamp': datetime.utcnow().isoformat()
        }))
    
    def log_migration_error(self, migration_name: str, error: str):
        """Логирование ошибки миграции"""
        self.logger.error(json.dumps({
            'event': 'migration_error',
            'migration_name': migration_name,
            'error': error,
            'timestamp': datetime.utcnow().isoformat()
        }))
    
    def log_rollback_start(self, migration_name: str):
        """Логирование начала отката"""
        self.logger.warning(json.dumps({
            'event': 'rollback_start',
            'migration_name': migration_name,
            'timestamp': datetime.utcnow().isoformat()
        }))
```

### Алерты для миграций

```bash
# migration-alerts.sh - настройка алертов для миграций

# Webhook для уведомлений
WEBHOOK_URL="https://your-webhook-service.com/alerts"

# Функция отправки алерта
send_alert() {
    local level="$1"
    local message="$2"
    local details="$3"
    
    curl -X POST "$WEBHOOK_URL" \
        -H "Content-Type: application/json" \
        -d "{
            \"level\": \"$level\",
            \"service\": \"llm-router\",
            \"event\": \"migration\",
            \"message\": \"$message\",
            \"details\": \"$details\",
            \"timestamp\": \"$(date -u +%Y-%m-%dT%H:%M:%SZ)\"
        }"
}

# Мониторинг логов миграций
tail -f migration.log | while read line; do
    if echo "$line" | grep -q "migration_error"; then
        send_alert "error" "Migration failed" "$line"
    elif echo "$line" | grep -q "rollback_start"; then
        send_alert "warning" "Migration rollback started" "$line"
    elif echo "$line" | grep -q "migration_success"; then
        send_alert "info" "Migration completed successfully" "$line"
    fi
done
```

---

*Это завершает документацию LLM Router. Все 9 консолидированных файлов созданы согласно требованиям.*