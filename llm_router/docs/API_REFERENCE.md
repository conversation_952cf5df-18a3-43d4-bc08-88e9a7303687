# Справочник API

Данный справочник содержит полную документацию по API интеграциям LLM Router, включая LiteLLM API, GPUStack интеграцию и ML Predictor API.

## LiteLLM API

### Обзор LiteLLM интеграции

LLM Router интегрируется с LiteLLM через CustomRoutingStrategyBase, предоставляя интеллектуальную маршрутизацию для всех поддерживаемых LiteLLM endpoints.

### Основные endpoints

#### Chat Completions

**POST** `/v1/chat/completions`

Основной endpoint для chat completions с интеллектуальной маршрутизацией.

**Заголовки запроса:**
```http
Authorization: Bearer sk-your-litellm-key
Content-Type: application/json
```

**Тело запроса:**
```json
{
  "model": "chat_instruct",
  "messages": [
    {
      "role": "user",
      "content": "Explain quantum computing in simple terms"
    }
  ],
  "max_tokens": 1000,
  "temperature": 0.7,
  "stream": false
}
```

**Ответ:**
```json
{
  "id": "chatcmpl-123456",
  "object": "chat.completion",
  "created": **********,
  "model": "chat_instruct",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "Quantum computing is a revolutionary approach to computation..."
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 12,
    "completion_tokens": 150,
    "total_tokens": 162
  },
  "x-llm-router": {
    "selected_deployment": "gpu-h100-1",
    "routing_strategy": "llm-time-predictor",
    "decision_time_ms": 15,
    "ml_prediction": 2.3,
    "hybrid_score": 1.87
  }
}
```

#### Streaming Chat Completions

**POST** `/v1/chat/completions` (с `"stream": true`)

**Тело запроса:**
```json
{
  "model": "chat_instruct",
  "messages": [
    {
      "role": "user", 
      "content": "Write a short story about AI"
    }
  ],
  "stream": true,
  "max_tokens": 500
}
```

**Ответ (Server-Sent Events):**
```
data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":**********,"model":"chat_instruct","choices":[{"index":0,"delta":{"role":"assistant","content":""},"finish_reason":null}]}

data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":**********,"model":"chat_instruct","choices":[{"index":0,"delta":{"content":"Once"},"finish_reason":null}]}

data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":**********,"model":"chat_instruct","choices":[{"index":0,"delta":{"content":" upon"},"finish_reason":null}]}

...

data: [DONE]
```

#### Embeddings

**POST** `/v1/embeddings`

**Тело запроса:**
```json
{
  "model": "text-embedding-ada-002",
  "input": "The quick brown fox jumps over the lazy dog"
}
```

**Ответ:**
```json
{
  "object": "list",
  "data": [
    {
      "object": "embedding",
      "embedding": [0.**********, -0.009327292, ...],
      "index": 0
    }
  ],
  "model": "text-embedding-ada-002",
  "usage": {
    "prompt_tokens": 8,
    "total_tokens": 8
  }
}
```

### Health и мониторинг endpoints

#### Health Check

**GET** `/health`

**Ответ:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00Z",
  "version": "1.0.0",
  "components": {
    "database": "healthy",
    "ml_predictor": "healthy", 
    "gpustack": "healthy",
    "cache": "healthy"
  }
}
```

#### Readiness Check

**GET** `/health/readiness`

**Ответ:**
```json
{
  "status": "ready",
  "checks": {
    "database_connection": true,
    "ml_predictor_connection": true,
    "gpustack_connection": true,
    "migrations_applied": true
  }
}
```

#### Liveness Check

**GET** `/health/liveness`

**Ответ:**
```json
{
  "status": "alive",
  "uptime_seconds": 3600,
  "memory_usage_mb": 512,
  "cpu_usage_percent": 25.5
}
```

### Управление моделями

#### Список моделей

**GET** `/v1/models`

**Ответ:**
```json
{
  "object": "list",
  "data": [
    {
      "id": "chat_instruct",
      "object": "model",
      "created": **********,
      "owned_by": "llm-router",
      "permission": [],
      "root": "chat_instruct",
      "parent": null,
      "max_tokens": 4096,
      "deployments": [
        {
          "deployment_name": "gpu-h100-1",
          "status": "active",
          "current_load": 5,
          "max_requests": 100
        },
        {
          "deployment_name": "gpu-a6000-1", 
          "status": "active",
          "current_load": 3,
          "max_requests": 80
        }
      ]
    }
  ]
}
```

#### Информация о модели

**GET** `/v1/models/{model_id}`

**Ответ:**
```json
{
  "id": "chat_instruct",
  "object": "model",
  "created": **********,
  "owned_by": "llm-router",
  "max_tokens": 4096,
  "routing_info": {
    "strategy": "llm-time-predictor",
    "hybrid_weight": 0.3,
    "fallback_strategy": "least-busy"
  },
  "deployments": [
    {
      "deployment_name": "gpu-h100-1",
      "litellm_params": {
        "model": "openai/gpt-4",
        "api_base": "https://api.openai.com/v1"
      },
      "hardware_info": {
        "gpu_model": "NVIDIA H100",
        "gpu_memory": "80GB",
        "worker_id": "gpu-worker-1"
      },
      "performance_metrics": {
        "avg_response_time": 2.3,
        "p95_response_time": 4.1,
        "success_rate": 0.995
      }
    }
  ]
}
```

### Конфигурация и управление

#### Получение конфигурации роутера

**GET** `/router/config`

**Заголовки:**
```http
Authorization: Bearer sk-your-admin-key
```

**Ответ:**
```json
{
  "routing_strategy": "llm-time-predictor",
  "routing_strategy_args": {
    "predictor_url": "http://predictor-service:8008",
    "gpustack_url": "http://gpustack:80",
    "hybrid_weight": 0.3,
    "cache_ttl": 300,
    "fallback_strategy": "least-busy"
  },
  "analytics": {
    "enabled": true,
    "batch_size": 100,
    "flush_interval": 30
  }
}
```

#### Обновление конфигурации роутера

**PUT** `/router/config`

**Тело запроса:**
```json
{
  "routing_strategy_args": {
    "hybrid_weight": 0.25,
    "cache_ttl": 600
  }
}
```

**Ответ:**
```json
{
  "status": "updated",
  "message": "Router configuration updated successfully",
  "updated_fields": ["hybrid_weight", "cache_ttl"]
}
```

#### Статистика маршрутизации

**GET** `/router/stats`

**Ответ:**
```json
{
  "total_requests": 15847,
  "time_period": "24h",
  "deployment_stats": {
    "gpu-h100-1": {
      "requests": 5234,
      "percentage": 33.0,
      "avg_response_time": 2.3,
      "success_rate": 0.998
    },
    "gpu-a6000-1": {
      "requests": 4891,
      "percentage": 30.9,
      "avg_response_time": 3.1,
      "success_rate": 0.995
    }
  },
  "ml_prediction_accuracy": {
    "avg_error_percent": 15.2,
    "median_error_percent": 12.1,
    "predictions_within_20_percent": 78.5
  },
  "cache_stats": {
    "gpustack_hit_rate": 96.2,
    "predictions_hit_rate": 85.7,
    "total_cache_hits": 12456
  }
}
```

## GPUStack интеграция

### Обзор GPUStack API

LLM Router интегрируется с GPUStack для получения информации о физическом оборудовании и deployments моделей.

### Аутентификация

GPUStack поддерживает несколько методов аутентификации:

```python
# Метод 1: API Key
headers = {
    'Authorization': f'Bearer {GPUSTACK_KEY}'
}

# Метод 2: Token
headers = {
    'X-API-Token': GPUSTACK_TOKEN
}

# Метод 3: Basic Auth (с паролем)
auth = aiohttp.BasicAuth('admin', GPUSTACK_PASSWORD)
```

### Workers API

#### Получение списка workers

**GET** `/api/v1/workers`

**Заголовки:**
```http
Authorization: Bearer your-gpustack-key
```

**Ответ:**
```json
{
  "items": [
    {
      "id": "worker-1",
      "name": "gpu-h100-worker",
      "status": "ready",
      "labels": {
        "gpu.type": "H100",
        "gpu.memory": "80Gi",
        "node.type": "gpu"
      },
      "resources": {
        "gpu": {
          "total": 1,
          "available": 1,
          "allocated": 0
        },
        "memory": {
          "total": "80Gi",
          "available": "75Gi",
          "allocated": "5Gi"
        }
      },
      "conditions": [
        {
          "type": "Ready",
          "status": "True",
          "lastTransitionTime": "2024-01-15T10:00:00Z"
        }
      ]
    }
  ]
}
```

#### Получение информации о конкретном worker

**GET** `/api/v1/workers/{worker_id}`

**Ответ:**
```json
{
  "id": "worker-1",
  "name": "gpu-h100-worker",
  "status": "ready",
  "spec": {
    "labels": {
      "gpu.type": "H100",
      "gpu.memory": "80Gi",
      "gpu.compute_capability": "9.0"
    }
  },
  "status": {
    "conditions": [
      {
        "type": "Ready",
        "status": "True"
      }
    ],
    "allocatable": {
      "gpu": 1,
      "memory": "80Gi",
      "cpu": "32"
    },
    "capacity": {
      "gpu": 1,
      "memory": "80Gi", 
      "cpu": "32"
    }
  },
  "metrics": {
    "gpu_utilization": 45.2,
    "gpu_memory_used": "15Gi",
    "gpu_temperature": 65,
    "power_usage": 250
  }
}
```

### Models API

#### Получение списка моделей

**GET** `/api/v1/models`

**Ответ:**
```json
{
  "items": [
    {
      "id": "model-1",
      "name": "llama-3.1-70b-instruct",
      "status": "ready",
      "spec": {
        "model_name": "llama-3.1-70b-instruct",
        "model_size": "70B",
        "quantization": "fp16",
        "context_length": 8192,
        "worker_selector": {
          "matchLabels": {
            "gpu.type": "H100"
          }
        }
      },
      "status": {
        "phase": "Ready",
        "replicas": 2,
        "ready_replicas": 2,
        "endpoints": [
          {
            "name": "gpu-h100-1",
            "url": "http://gpu-worker-1:8000/v1",
            "worker_id": "worker-1",
            "status": "ready"
          },
          {
            "name": "gpu-h100-2", 
            "url": "http://gpu-worker-2:8000/v1",
            "worker_id": "worker-2",
            "status": "ready"
          }
        ]
      }
    }
  ]
}
```

#### Получение метрик модели

**GET** `/api/v1/models/{model_id}/metrics`

**Ответ:**
```json
{
  "model_id": "model-1",
  "model_name": "llama-3.1-70b-instruct",
  "metrics": {
    "total_requests": 1247,
    "active_requests": 5,
    "avg_response_time": 2.3,
    "tokens_per_second": 45.2,
    "success_rate": 0.998
  },
  "endpoints": [
    {
      "name": "gpu-h100-1",
      "worker_id": "worker-1",
      "metrics": {
        "requests": 623,
        "active_requests": 3,
        "avg_response_time": 2.1,
        "gpu_utilization": 65.2
      }
    },
    {
      "name": "gpu-h100-2",
      "worker_id": "worker-2", 
      "metrics": {
        "requests": 624,
        "active_requests": 2,
        "avg_response_time": 2.5,
        "gpu_utilization": 58.7
      }
    }
  ]
}
```

### Интеграция с кэшированием

LLM Router кэширует данные GPUStack для оптимизации производительности:

```python
# Конфигурация кэша
CACHE_CONFIG = {
    'workers_info': {
        'ttl': 300,  # 5 минут
        'key_pattern': 'gpustack:workers:{worker_id}'
    },
    'models_info': {
        'ttl': 180,  # 3 минуты
        'key_pattern': 'gpustack:models:{model_id}'
    },
    'metrics': {
        'ttl': 60,   # 1 минута
        'key_pattern': 'gpustack:metrics:{model_id}'
    }
}
```

### Обработка ошибок GPUStack

```python
# Типичные ошибки и их обработка
ERROR_HANDLERS = {
    401: "Неверные учетные данные GPUStack",
    403: "Недостаточно прав доступа",
    404: "Ресурс не найден",
    500: "Внутренняя ошибка GPUStack",
    503: "GPUStack временно недоступен"
}

# Fallback стратегии
FALLBACK_STRATEGIES = {
    'connection_error': 'use_cached_data',
    'timeout_error': 'use_cached_data',
    'auth_error': 'disable_gpustack_integration',
    'server_error': 'retry_with_backoff'
}
```

## ML Predictor API

### Обзор ML Predictor сервиса

ML Predictor - это внешний HTTP сервис, который предсказывает время ответа для различных deployments на основе характеристик запроса.

### Endpoints

#### Получение предсказаний

**POST** `/predict`

**Тело запроса:**
```json
{
  "request_features": {
    "text": "Explain the theory of relativity in detail",
    "text_length": 45,
    "task_type": "explanation",
    "complexity_score": 0.8,
    "estimated_tokens": 500
  },
  "deployments": [
    {
      "deployment_name": "gpu-h100-1",
      "model_info": {
        "model_name": "llama-3.1-70b-instruct",
        "model_size": "70B",
        "quantization": "fp16",
        "context_length": 8192
      },
      "hardware_info": {
        "gpu_model": "NVIDIA H100",
        "gpu_memory_total": "80GB",
        "gpu_memory_available": "75GB",
        "compute_capability": "9.0"
      },
      "current_load": {
        "active_requests": 5,
        "queue_length": 2,
        "avg_response_time": 2.1
      }
    },
    {
      "deployment_name": "gpu-a6000-1",
      "model_info": {
        "model_name": "llama-3.1-70b-instruct",
        "model_size": "70B",
        "quantization": "fp16",
        "context_length": 8192
      },
      "hardware_info": {
        "gpu_model": "NVIDIA RTX A6000",
        "gpu_memory_total": "48GB",
        "gpu_memory_available": "45GB",
        "compute_capability": "8.6"
      },
      "current_load": {
        "active_requests": 3,
        "queue_length": 1,
        "avg_response_time": 3.2
      }
    }
  ]
}
```

**Ответ:**
```json
{
  "predictions": {
    "gpu-h100-1": {
      "predicted_time": 2.3,
      "confidence": 0.92,
      "factors": {
        "hardware_score": 0.95,
        "load_impact": 0.15,
        "complexity_impact": 0.25,
        "model_efficiency": 0.88
      }
    },
    "gpu-a6000-1": {
      "predicted_time": 3.1,
      "confidence": 0.88,
      "factors": {
        "hardware_score": 0.78,
        "load_impact": 0.12,
        "complexity_impact": 0.25,
        "model_efficiency": 0.85
      }
    }
  },
  "metadata": {
    "model_version": "v2.1.0",
    "prediction_time_ms": 45,
    "features_used": [
      "text_length",
      "task_type", 
      "gpu_model",
      "current_load"
    ]
  }
}
```

#### Health Check

**GET** `/health`

**Ответ:**
```json
{
  "status": "healthy",
  "model_version": "v2.1.0",
  "uptime_seconds": 86400,
  "predictions_served": 15847,
  "avg_prediction_time_ms": 42,
  "model_accuracy": {
    "mae": 0.15,
    "rmse": 0.23,
    "r2_score": 0.87
  }
}
```

#### Метрики модели

**GET** `/metrics`

**Ответ:**
```json
{
  "model_performance": {
    "version": "v2.1.0",
    "training_date": "2024-01-10T00:00:00Z",
    "accuracy_metrics": {
      "mean_absolute_error": 0.15,
      "root_mean_square_error": 0.23,
      "r2_score": 0.87,
      "predictions_within_20_percent": 78.5
    },
    "feature_importance": {
      "gpu_model": 0.35,
      "text_length": 0.25,
      "current_load": 0.20,
      "task_type": 0.15,
      "model_size": 0.05
    }
  },
  "service_metrics": {
    "total_predictions": 15847,
    "avg_response_time_ms": 42,
    "error_rate": 0.002,
    "cache_hit_rate": 0.15
  }
}
```

### Интеграция с LLM Router

#### Конфигурация клиента

```python
# Конфигурация ML Predictor клиента
PREDICTOR_CONFIG = {
    'url': 'http://predictor-service:8008',
    'timeout': 10,
    'retry_attempts': 3,
    'retry_delay': 1,
    'cache_ttl': 30,
    'batch_size': 10
}
```

#### Обработка ошибок

```python
# Стратегии обработки ошибок
ERROR_HANDLING = {
    'connection_timeout': {
        'action': 'fallback_to_least_busy',
        'log_level': 'warning'
    },
    'service_unavailable': {
        'action': 'fallback_to_least_busy',
        'log_level': 'error'
    },
    'invalid_response': {
        'action': 'retry_with_backoff',
        'max_retries': 2
    },
    'prediction_error': {
        'action': 'use_default_prediction',
        'default_value': 5.0
    }
}
```

#### Кэширование предсказаний

```python
# Стратегия кэширования предсказаний
PREDICTION_CACHE = {
    'enabled': True,
    'ttl': 30,  # 30 секунд
    'key_strategy': 'hash_request_features',
    'similarity_threshold': 0.95,  # Для похожих запросов
    'max_cache_size': 10000
}
```

## Аналитика API

### Endpoints аналитики

#### Получение статистики маршрутизации

**GET** `/analytics/routing-stats`

**Параметры запроса:**
- `period` - период времени (1h, 24h, 7d, 30d)
- `deployment` - фильтр по deployment (опционально)
- `model` - фильтр по модели (опционально)

**Пример запроса:**
```
GET /analytics/routing-stats?period=24h&deployment=gpu-h100-1
```

**Ответ:**
```json
{
  "period": "24h",
  "total_requests": 5234,
  "deployment": "gpu-h100-1",
  "metrics": {
    "avg_response_time": 2.3,
    "p50_response_time": 2.1,
    "p95_response_time": 4.1,
    "p99_response_time": 6.8,
    "success_rate": 0.998,
    "error_rate": 0.002
  },
  "ml_prediction_accuracy": {
    "avg_error_percent": 12.5,
    "median_error_percent": 10.2,
    "predictions_within_20_percent": 82.1
  },
  "hourly_breakdown": [
    {
      "hour": "2024-01-15T00:00:00Z",
      "requests": 218,
      "avg_response_time": 2.1,
      "success_rate": 0.999
    }
  ]
}
```

#### Экспорт аналитических данных

**GET** `/analytics/export`

**Параметры запроса:**
- `format` - формат экспорта (json, csv, parquet)
- `from` - начальная дата (ISO 8601)
- `to` - конечная дата (ISO 8601)
- `table` - таблица для экспорта (routing_decisions, deployment_metrics)

**Пример запроса:**
```
GET /analytics/export?format=csv&from=2024-01-01T00:00:00Z&to=2024-01-31T23:59:59Z&table=routing_decisions
```

**Ответ (CSV):**
```csv
id,created_at,request_id,selected_deployment,ml_prediction,actual_response_time,hybrid_score
uuid1,2024-01-15T10:30:00Z,req_123,gpu-h100-1,2.3,2.1,1.87
uuid2,2024-01-15T10:30:15Z,req_124,gpu-a6000-1,3.1,3.2,2.15
...
```

## Коды ошибок и обработка

### Стандартные HTTP коды ошибок

| Код | Описание | Действие |
|-----|----------|----------|
| 400 | Bad Request | Проверить формат запроса |
| 401 | Unauthorized | Проверить API ключ |
| 403 | Forbidden | Проверить права доступа |
| 404 | Not Found | Проверить URL endpoint |
| 429 | Too Many Requests | Реализовать rate limiting |
| 500 | Internal Server Error | Проверить логи сервера |
| 502 | Bad Gateway | Проверить upstream сервисы |
| 503 | Service Unavailable | Дождаться восстановления |
| 504 | Gateway Timeout | Увеличить таймауты |

### Специфичные ошибки LLM Router

```json
{
  "error": {
    "type": "routing_error",
    "code": "PREDICTOR_UNAVAILABLE",
    "message": "ML Predictor service is unavailable, falling back to least-busy routing",
    "details": {
      "predictor_url": "http://predictor-service:8008",
      "last_successful_call": "2024-01-15T10:25:00Z",
      "fallback_strategy": "least-busy",
      "retry_after": 30
    }
  }
}
```

```json
{
  "error": {
    "type": "deployment_error", 
    "code": "NO_AVAILABLE_DEPLOYMENTS",
    "message": "No deployments available for model 'chat_instruct'",
    "details": {
      "model": "chat_instruct",
      "total_deployments": 3,
      "healthy_deployments": 0,
      "last_health_check": "2024-01-15T10:29:45Z"
    }
  }
}
```

### Retry стратегии

```python
# Конфигурация retry стратегий
RETRY_CONFIG = {
    'ml_predictor': {
        'max_attempts': 3,
        'backoff_factor': 2,
        'timeout': 10
    },
    'gpustack': {
        'max_attempts': 2,
        'backoff_factor': 1.5,
        'timeout': 5
    },
    'database': {
        'max_attempts': 5,
        'backoff_factor': 1.2,
        'timeout': 30
    }
}
```

## Примеры использования

### Python клиент

```python
import aiohttp
import asyncio

class LLMRouterClient:
    def __init__(self, base_url: str, api_key: str):
        self.base_url = base_url
        self.headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
    
    async def chat_completion(self, messages: list, model: str = "chat_instruct", **kwargs):
        """Отправка chat completion запроса"""
        data = {
            'model': model,
            'messages': messages,
            **kwargs
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f'{self.base_url}/v1/chat/completions',
                json=data,
                headers=self.headers
            ) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    error = await response.json()
                    raise Exception(f"API Error: {error}")
    
    async def get_routing_stats(self, period: str = "24h"):
        """Получение статистики маршрутизации"""
        async with aiohttp.ClientSession() as session:
            async with session.get(
                f'{self.base_url}/analytics/routing-stats?period={period}',
                headers=self.headers
            ) as response:
                return await response.json()

# Пример использования
async def main():
    client = LLMRouterClient(
        base_url='http://localhost:4000',
        api_key='sk-your-api-key'
    )
    
    # Chat completion
    response = await client.chat_completion(
        messages=[
            {'role': 'user', 'content': 'Explain machine learning'}
        ],
        max_tokens=500,
        temperature=0.7
    )
    
    print(f"Response: {response['choices'][0]['message']['content']}")
    print(f"Selected deployment: {response['x-llm-router']['selected_deployment']}")
    
    # Статистика
    stats = await client.get_routing_stats(period="1h")
    print(f"Total requests: {stats['total_requests']}")

if __name__ == "__main__":
    asyncio.run(main())
```

### cURL примеры

```bash
# Chat completion
curl -X POST http://localhost:4000/v1/chat/completions \
  -H "Authorization: Bearer sk-your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "chat_instruct",
    "messages": [
      {"role": "user", "content": "Hello, world!"}
    ],
    "max_tokens": 100
  }'

# Streaming completion
curl -X POST http://localhost:4000/v1/chat/completions \
  -H "Authorization: Bearer sk-your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "chat_instruct", 
    "messages": [
      {"role": "user", "content": "Write a poem"}
    ],
    "stream": true,
    "max_tokens": 200
  }'

# Health check
curl http://localhost:4000/health

# Статистика маршрутизации
curl -H "Authorization: Bearer sk-your-admin-key" \
  http://localhost:4000/router/stats

# Экспорт аналитики
curl -H "Authorization: Bearer sk-your-admin-key" \
  "http://localhost:4000/analytics/export?format=json&period=24h" \
  -o analytics_data.json
```

---

*Следующий раздел: [Руководство по устранению неполадок](TROUBLESHOOTING.md)*