# Руководство по эксплуатации

Данное руководство содержит полную информацию по развертыванию, конфигурации, управлению и обслуживанию системы LLM Router.

## Установка и настройка

### Системные требования

#### Минимальные требования
- **CPU**: 2 ядра
- **RAM**: 4 GB
- **Диск**: 10 GB свободного места
- **ОС**: Linux (Ubuntu 20.04+, CentOS 8+)
- **Docker**: 20.10+
- **Docker Compose**: 2.0+

#### Рекомендуемые требования
- **CPU**: 4+ ядра
- **RAM**: 8+ GB
- **Диск**: 50+ GB SSD
- **Сеть**: стабильное подключение к интернету
- **GPU**: NVIDIA GPU с CUDA 11.8+ (для GPU deployments)

### Установка Docker

```bash
# Ubuntu/Debian
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Установка Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Проверка установки
docker --version
docker-compose --version
```

### Клонирование и первоначальная настройка

```bash
# Клонирование репозитория
git clone <repository-url>
cd llm_router

# Создание конфигурационных файлов
cp .env.example .env
cp litellm.config.yaml.example litellm.config.yaml

# Установка прав доступа
chmod +x run.sh
chmod +x entrypoint.sh
```

### Конфигурация окружения

Отредактируйте файл `.env`:

```bash
# === Основные настройки LiteLLM ===
LITELLM_MASTER_KEY=sk-your-secure-master-key-here
LITELLM_SALT_KEY=sk-your-secure-salt-key-here
LITELLM_LOG=INFO

# === ML Predictor Service ===
PREDICT_URL=http://predictor-service:8008
PREDICT_TIMEOUT=10
PREDICT_RETRY_ATTEMPTS=3

# === GPUStack Integration ===
GPUSTACK_URL=https://your-gpustack-instance.com
GPUSTACK_KEY=your-gpustack-api-key
GPUSTACK_TOKEN=your-gpustack-token
GPUSTACK_PASSWORD=your-gpustack-password

# === База данных ===
DATABASE_URL=*********************************************/litellm
POSTGRES_DB=litellm
POSTGRES_USER=llmproxy
POSTGRES_PASSWORD=secure_password

# === Аналитика ===
ANALYTICS_ENABLED=true
USE_ANALYTICS_MIGRATE=true
ANALYTICS_BATCH_SIZE=100
ANALYTICS_FLUSH_INTERVAL=30

# === Кэширование ===
REDIS_URL=redis://redis:6379/0
CACHE_TTL_GPUSTACK=300
CACHE_TTL_PREDICTIONS=30

# === Мониторинг ===
PROMETHEUS_PORT=9090
GRAFANA_PORT=3000
ENABLE_METRICS=true

# === Безопасность ===
SSL_VERIFY=true
API_TIMEOUT=30
MAX_CONCURRENT_REQUESTS=100
```

## Управление конфигурацией

### Структура конфигурации LiteLLM

Файл `litellm.config.yaml` содержит основную конфигурацию:

```yaml
model_list:
  - model_name: "chat_instruct"
    litellm_params:
      model: "openai/gpt-4"
      api_base: "https://api.openai.com/v1"
      api_key: "os.environ/OPENAI_API_KEY"
    model_info:
      mode: "chat"
      supports_function_calling: true
      max_tokens: 4096

  - model_name: "code_instruct"
    litellm_params:
      model: "anthropic/claude-3-sonnet"
      api_key: "os.environ/ANTHROPIC_API_KEY"
    model_info:
      mode: "chat"
      supports_function_calling: true
      max_tokens: 8192

router_settings:
  routing_strategy: "llm-time-predictor"
  routing_strategy_args:
    predictor_url: "os.environ/PREDICT_URL"
    gpustack_url: "os.environ/GPUSTACK_URL"
    gpustack_key: "os.environ/GPUSTACK_KEY"
    hybrid_weight: 0.3
    cache_ttl: 300
    fallback_strategy: "least-busy"

general_settings:
  master_key: "os.environ/LITELLM_MASTER_KEY"
  database_url: "os.environ/DATABASE_URL"
  store_model_in_db: true
  
litellm_settings:
  drop_params: true
  set_verbose: true
  json_logs: true
  log_raw_request_response: false
```

### Команды конфигурации

```bash
# Валидация конфигурации
./run.sh config validate

# Проверка статуса конфигурации
./run.sh config check

# Обновление конфигурации
./run.sh update config

# Тестирование маршрутизации
./run.sh config test-routing

# Резервное копирование конфигурации
./run.sh config backup

# Восстановление конфигурации
./run.sh config restore <backup-file>
```

## Справочник команд

Система предоставляет 44+ команды через интерфейс `./run.sh`. Команды организованы по категориям:

### Команды Docker

```bash
# === Управление сервисами ===
./run.sh docker up              # Запуск всех сервисов
./run.sh docker up --build      # Запуск с пересборкой образов
./run.sh docker down            # Остановка всех сервисов
./run.sh docker restart         # Перезапуск всех сервисов
./run.sh docker restart litellm # Перезапуск конкретного сервиса

# === Мониторинг ===
./run.sh docker status          # Статус всех сервисов
./run.sh docker logs            # Логи всех сервисов
./run.sh docker logs litellm    # Логи конкретного сервиса
./run.sh docker logs -f litellm # Логи в реальном времени

# === Разработка ===
./run.sh docker dev             # Запуск в режиме разработки
./run.sh docker build           # Сборка образов
./run.sh docker clean           # Очистка неиспользуемых образов
./run.sh docker shell litellm   # Подключение к контейнеру
```

### Команды конфигурации

```bash
# === Валидация ===
./run.sh config validate        # Проверка корректности конфигурации
./run.sh config check           # Статус конфигурации
./run.sh config test-routing    # Тест логики маршрутизации

# === Управление ===
./run.sh config backup          # Создание резервной копии
./run.sh config restore <file>  # Восстановление из копии
./run.sh config diff            # Сравнение с предыдущей версией
./run.sh config history         # История изменений

# === Тестирование интеграций ===
./run.sh config test-predictor  # Тест ML Predictor сервиса
./run.sh config test-gpustack   # Тест GPUStack интеграции
./run.sh config test-database   # Тест подключения к БД
```

### Команды обновления

```bash
# === Обновление конфигурации ===
./run.sh update config          # Обновление основной конфигурации
./run.sh update env             # Обновление переменных окружения

# === Обновление интеграций ===
./run.sh update gpustack        # Обновление настроек GPUStack
./run.sh update gpustack token  # Обновление токена GPUStack
./run.sh update predictor       # Обновление настроек ML Predictor

# === Обновление системы ===
./run.sh update system          # Обновление системных компонентов
./run.sh update dependencies    # Обновление зависимостей
```

### Команды тестирования

```bash
# === Базовое тестирование ===
./run.sh test endpoint          # Тест health endpoints
./run.sh test models            # Тест всех моделей
./run.sh test model <name>      # Тест конкретной модели

# === Интеграционное тестирование ===
./run.sh test integration       # Полные интеграционные тесты
./run.sh test routing           # Тест логики маршрутизации
./run.sh test performance       # Тест производительности

# === Нагрузочное тестирование ===
./run.sh test load              # Базовый нагрузочный тест
./run.sh test load --requests 1000 # Тест с 1000 запросов
./run.sh test concurrent        # Тест конкурентных запросов
```

### Команды GPUStack

```bash
# === Управление ===
./run.sh gpustack status        # Статус интеграции
./run.sh gpustack workers list  # Список GPU workers
./run.sh gpustack models list   # Список доступных моделей

# === Аутентификация ===
./run.sh gpustack credentials test    # Тест учетных данных
./run.sh gpustack auth refresh        # Обновление аутентификации
./run.sh gpustack token validate      # Валидация токена

# === Мониторинг ===
./run.sh gpustack metrics       # Метрики GPU utilization
./run.sh gpustack health        # Проверка здоровья интеграции
./run.sh gpustack cache stats   # Статистика кэша
```

### Команды отладки

```bash
# === Диагностика системы ===
./run.sh debug status           # Общий статус системы
./run.sh debug validate         # Валидация всех компонентов
./run.sh debug connectivity     # Проверка сетевых соединений

# === Анализ производительности ===
./run.sh debug cache            # Анализ производительности кэша
./run.sh debug least-busy       # Мониторинг least-busy tracking
./run.sh debug routing          # Отладка решений маршрутизации

# === Логирование ===
./run.sh debug logs             # Детальные логи отладки
./run.sh debug trace            # Трассировка запросов
./run.sh debug metrics          # Метрики производительности
```

### Команды мониторинга

```bash
# === Мониторинг системы ===
./run.sh monitor health         # Общее здоровье системы
./run.sh monitor routing        # Мониторинг маршрутизации
./run.sh monitor performance    # Мониторинг производительности

# === Мониторинг ресурсов ===
./run.sh monitor resources      # Использование ресурсов
./run.sh monitor database       # Мониторинг базы данных
./run.sh monitor cache          # Мониторинг кэша

# === Алерты ===
./run.sh monitor alerts         # Активные алерты
./run.sh monitor thresholds     # Пороговые значения
```

### Команды аналитики

```bash
# === Управление аналитикой ===
./run.sh analytics health check # Проверка здоровья системы аналитики
./run.sh analytics migrate up   # Применение миграций БД
./run.sh analytics migrate down # Откат миграций БД
./run.sh analytics migrate status # Статус миграций

# === Экспорт данных ===
./run.sh analytics data export  # Экспорт аналитических данных
./run.sh analytics reports       # Генерация отчетов
./run.sh analytics cleanup       # Очистка старых данных

# === Мониторинг ===
./run.sh analytics monitor       # Мониторинг системы аналитики
./run.sh analytics stats         # Статистика использования
```

## Развертывание

### Локальное развертывание

```bash
# Полное развертывание с нуля
./run.sh docker up --build

# Проверка развертывания
./run.sh test endpoint
./run.sh docker status

# Просмотр логов
./run.sh docker logs -f litellm
```

### Продакшн развертывание

#### 1. Подготовка окружения

```bash
# Создание продакшн конфигурации
cp .env.example .env.production
cp litellm.config.yaml.example litellm.config.production.yaml

# Настройка безопасных паролей и ключей
openssl rand -hex 32  # Для LITELLM_MASTER_KEY
openssl rand -hex 32  # Для LITELLM_SALT_KEY
openssl rand -hex 32  # Для POSTGRES_PASSWORD
```

#### 2. Конфигурация для продакшена

```bash
# .env.production
LITELLM_LOG=WARNING
SSL_VERIFY=true
ENABLE_METRICS=true
MAX_CONCURRENT_REQUESTS=500
ANALYTICS_BATCH_SIZE=500
CACHE_TTL_GPUSTACK=600

# Настройка мониторинга
PROMETHEUS_ENABLED=true
GRAFANA_ENABLED=true
ALERT_WEBHOOK_URL=https://your-alerting-system.com/webhook
```

#### 3. Запуск продакшн системы

```bash
# Использование продакшн конфигурации
export ENV_FILE=.env.production
export CONFIG_FILE=litellm.config.production.yaml

# Запуск с продакшн настройками
./run.sh docker up -d --build

# Проверка развертывания
./run.sh test integration
./run.sh monitor health
```

### Docker Compose конфигурация

```yaml
version: '3.8'

services:
  litellm:
    build:
      context: .
      dockerfile: Dockerfile
      target: runtime
    ports:
      - "4000:4000"
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - LITELLM_MASTER_KEY=${LITELLM_MASTER_KEY}
      - PREDICT_URL=${PREDICT_URL}
      - GPUSTACK_URL=${GPUSTACK_URL}
    volumes:
      - ./litellm.config.yaml:/app/litellm.config.yaml:ro
      - ./logs:/app/logs
    depends_on:
      - db
      - redis
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  db:
    image: postgres:16
    environment:
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  prometheus_data:
```

## Мониторинг и обслуживание

### Мониторинг здоровья системы

```bash
# Ежедневные проверки
./run.sh monitor health
./run.sh docker status
./run.sh analytics health check

# Еженедельные проверки
./run.sh test integration
./run.sh debug validate
./run.sh analytics stats

# Ежемесячные проверки
./run.sh analytics cleanup
./run.sh config backup
./run.sh update system
```

### Логирование

#### Структура логов

```
logs/
├── litellm/
│   ├── access.log          # HTTP запросы
│   ├── error.log           # Ошибки системы
│   ├── routing.log         # Решения маршрутизации
│   └── performance.log     # Метрики производительности
├── analytics/
│   ├── database.log        # Операции с БД
│   └── migrations.log      # Миграции схемы
└── integrations/
    ├── gpustack.log        # GPUStack интеграция
    ├── predictor.log       # ML Predictor
    └── cache.log           # Операции кэширования
```

#### Настройка логирования

```python
# logging_utils.py
LOGGING_CONFIG = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'detailed': {
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        },
        'json': {
            'format': '{"timestamp": "%(asctime)s", "level": "%(levelname)s", "logger": "%(name)s", "message": "%(message)s"}'
        }
    },
    'handlers': {
        'file': {
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': 'logs/litellm/system.log',
            'maxBytes': 10485760,  # 10MB
            'backupCount': 5,
            'formatter': 'detailed'
        },
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'json'
        }
    },
    'loggers': {
        'llm_router': {
            'handlers': ['file', 'console'],
            'level': 'INFO',
            'propagate': False
        }
    }
}
```

### Резервное копирование

#### Автоматическое резервное копирование

```bash
#!/bin/bash
# backup.sh - скрипт автоматического резервного копирования

BACKUP_DIR="/backups/llm_router"
DATE=$(date +%Y%m%d_%H%M%S)

# Создание директории для бэкапов
mkdir -p "$BACKUP_DIR/$DATE"

# Резервное копирование конфигурации
./run.sh config backup "$BACKUP_DIR/$DATE/config_backup.tar.gz"

# Резервное копирование базы данных
docker exec llm_router_db_1 pg_dump -U llmproxy litellm > "$BACKUP_DIR/$DATE/database_backup.sql"

# Резервное копирование логов
tar -czf "$BACKUP_DIR/$DATE/logs_backup.tar.gz" logs/

# Очистка старых бэкапов (старше 30 дней)
find "$BACKUP_DIR" -type d -mtime +30 -exec rm -rf {} \;

echo "Backup completed: $BACKUP_DIR/$DATE"
```

#### Восстановление из резервной копии

```bash
# Восстановление конфигурации
./run.sh config restore /backups/llm_router/20240115_120000/config_backup.tar.gz

# Восстановление базы данных
docker exec -i llm_router_db_1 psql -U llmproxy litellm < /backups/llm_router/20240115_120000/database_backup.sql

# Перезапуск сервисов
./run.sh docker restart
```

### Обновление системы

#### Обновление компонентов

```bash
# Обновление Docker образов
./run.sh docker build --no-cache

# Обновление зависимостей Python
./run.sh update dependencies

# Обновление конфигурации
./run.sh update config

# Применение миграций БД
./run.sh analytics migrate up
```

#### Процедура обновления

1. **Подготовка**
   ```bash
   # Создание резервной копии
   ./run.sh config backup
   
   # Проверка текущего состояния
   ./run.sh monitor health
   ```

2. **Обновление**
   ```bash
   # Остановка сервисов
   ./run.sh docker down
   
   # Обновление кода
   git pull origin main
   
   # Пересборка образов
   ./run.sh docker build --no-cache
   ```

3. **Миграция данных**
   ```bash
   # Применение миграций
   ./run.sh analytics migrate up
   
   # Проверка миграций
   ./run.sh analytics migrate status
   ```

4. **Запуск и проверка**
   ```bash
   # Запуск сервисов
   ./run.sh docker up -d
   
   # Проверка работоспособности
   ./run.sh test integration
   ./run.sh monitor health
   ```

## Конфигурация для продакшена

### Оптимизация производительности

```yaml
# litellm.config.yaml - продакшн настройки
router_settings:
  routing_strategy: "llm-time-predictor"
  routing_strategy_args:
    hybrid_weight: 0.25          # Больший вес ML предсказаниям
    cache_ttl: 600               # Увеличенное время кэширования
    max_concurrent_requests: 500  # Увеличенный лимит запросов
    request_timeout: 30          # Таймаут запросов
    retry_attempts: 3            # Количество повторов
    circuit_breaker_threshold: 10 # Порог для circuit breaker

general_settings:
  database_url: "os.environ/DATABASE_URL"
  store_model_in_db: true
  max_budget: 1000.0
  budget_duration: "1d"
  
litellm_settings:
  drop_params: true
  set_verbose: false             # Отключение verbose в продакшене
  json_logs: true
  log_raw_request_response: false # Безопасность данных
  success_callback: ["prometheus", "analytics"]
  failure_callback: ["prometheus", "analytics"]
```

### Мониторинг производительности

```bash
# Настройка алертов
./run.sh monitor thresholds set response_time 5000  # 5 секунд
./run.sh monitor thresholds set error_rate 0.05     # 5% ошибок
./run.sh monitor thresholds set cpu_usage 0.80      # 80% CPU
./run.sh monitor thresholds set memory_usage 0.85   # 85% памяти

# Мониторинг в реальном времени
./run.sh monitor performance --interval 30  # Каждые 30 секунд
```

### Безопасность и аутентификация

```bash
# Настройка SSL/TLS
export SSL_CERT_PATH="/etc/ssl/certs/llm_router.crt"
export SSL_KEY_PATH="/etc/ssl/private/llm_router.key"

# Настройка firewall
sudo ufw allow 4000/tcp  # LiteLLM API
sudo ufw allow 9090/tcp  # Prometheus (только для мониторинга)
sudo ufw deny 5432/tcp   # PostgreSQL (только внутренний доступ)
sudo ufw deny 6379/tcp   # Redis (только внутренний доступ)

# Ротация ключей
./run.sh update credentials rotate-keys
```

---

*Следующий раздел: [Руководство по аналитике](ANALYTICS_GUIDE.md)*