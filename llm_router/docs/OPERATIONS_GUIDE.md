# Руководство по эксплуатации

Данное руководство содержит полную информацию по развертыванию, конфигурации, управлению и обслуживанию системы LLM Router.

## Установка и настройка

### Системные требования

#### Минимальные требования
- **CPU**: 2 ядра
- **RAM**: 4 GB
- **Диск**: 10 GB свободного места
- **ОС**: Linux (Ubuntu 20.04+, CentOS 8+)
- **Docker**: 20.10+
- **Docker Compose**: 2.0+

#### Рекомендуемые требования
- **CPU**: 4+ ядра
- **RAM**: 8+ GB
- **Диск**: 50+ GB SSD
- **Сеть**: стабильное подключение к интернету
- **GPU**: NVIDIA GPU с CUDA 11.8+ (для GPU deployments)

### Установка Docker

```bash
# Ubuntu/Debian
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Установка Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Проверка установки
docker --version
docker-compose --version
```

### Клонирование и первоначальная настройка

```bash
# Клонирование репозитория
git clone <repository-url>
cd llm_router

# Создание конфигурационных файлов
cp .env.example .env
cp litellm.config.yaml.example litellm.config.yaml

# Установка прав доступа
chmod +x run.sh
chmod +x entrypoint.sh
```

### Конфигурация окружения

Отредактируйте файл `.env`:

```bash
# === Основные настройки LiteLLM ===
LITELLM_MASTER_KEY=sk-your-secure-master-key-here
LITELLM_SALT_KEY=sk-your-secure-salt-key-here
LITELLM_LOG=INFO

# === ML Predictor Service ===
PREDICT_URL=http://predictor-service:8008
PREDICT_TIMEOUT=10
PREDICT_RETRY_ATTEMPTS=3

# === GPUStack Integration ===
GPUSTACK_URL=https://your-gpustack-instance.com
GPUSTACK_KEY=your-gpustack-api-key
GPUSTACK_TOKEN=your-gpustack-token
GPUSTACK_PASSWORD=your-gpustack-password

# === База данных ===
DATABASE_URL=*********************************************/litellm
POSTGRES_DB=litellm
POSTGRES_USER=llmproxy
POSTGRES_PASSWORD=secure_password

# === Аналитика ===
ANALYTICS_ENABLED=true
USE_ANALYTICS_MIGRATE=true
ANALYTICS_BATCH_SIZE=100
ANALYTICS_FLUSH_INTERVAL=30

# === Кэширование ===
REDIS_URL=redis://redis:6379/0
CACHE_TTL_GPUSTACK=300
CACHE_TTL_PREDICTIONS=30

# === Мониторинг ===
PROMETHEUS_PORT=9090
GRAFANA_PORT=3000
ENABLE_METRICS=true

# === Безопасность ===
SSL_VERIFY=true
API_TIMEOUT=30
MAX_CONCURRENT_REQUESTS=100
```

## Управление конфигурацией

### Структура конфигурации LiteLLM

Файл `litellm.config.yaml` содержит основную конфигурацию:

```yaml
model_list:
  - model_name: "chat_instruct"
    litellm_params:
      model: "openai/gpt-4"
      api_base: "https://api.openai.com/v1"
      api_key: "os.environ/OPENAI_API_KEY"
    model_info:
      mode: "chat"
      supports_function_calling: true
      max_tokens: 4096

  - model_name: "code_instruct"
    litellm_params:
      model: "anthropic/claude-3-sonnet"
      api_key: "os.environ/ANTHROPIC_API_KEY"
    model_info:
      mode: "chat"
      supports_function_calling: true
      max_tokens: 8192

router_settings:
  routing_strategy: "llm-time-predictor"
  routing_strategy_args:
    predictor_url: "os.environ/PREDICT_URL"
    gpustack_url: "os.environ/GPUSTACK_URL"
    gpustack_key: "os.environ/GPUSTACK_KEY"
    hybrid_weight: 0.3
    cache_ttl: 300
    fallback_strategy: "least-busy"

general_settings:
  master_key: "os.environ/LITELLM_MASTER_KEY"
  database_url: "os.environ/DATABASE_URL"
  store_model_in_db: true
  
litellm_settings:
  drop_params: true
  set_verbose: true
  json_logs: true
  log_raw_request_response: false
```

### Команды конфигурации

```bash
# Валидация конфигурации
./run.sh config validate

# Проверка статуса конфигурации
./run.sh config check

# Обновление конфигурации
./run.sh update config

# Тестирование маршрутизации
./run.sh config test-routing

# Резервное копирование конфигурации
./run.sh config backup

# Восстановление конфигурации
./run.sh config restore <backup-file>
```

## Справочник команд

Скрипт `run.sh` является главной точкой входа для управления всеми операциями LLM Router. Он предоставляет унифицированный интерфейс для управления Docker-сервисами, конфигурацией, тестированием и мониторингом.

### Структура команд

```bash
./run.sh [КАТЕГОРИЯ] [КОМАНДА] [ОПЦИИ]
```

### Комплексное тестирование команд

Для проверки работоспособности всех команд LLM Router создан специальный тестовый скрипт:

```bash
# Автоматическое тестирование всех команд
./scripts/test_all_commands.sh [РЕЖИМ]

# Режимы тестирования:
./scripts/test_all_commands.sh                    # Полное тестирование (по умолчанию)
./scripts/test_all_commands.sh quick              # Быстрое тестирование синтаксиса
SKIP_DOCKER_TESTS=true ./scripts/test_all_commands.sh  # Без Docker тестов
```

**Особенности тестового скрипта:**
- Безопасное тестирование: команды изменения состояния проверяются только на синтаксис
- Цветной вывод с индикаторами прогресса
- Логирование всех результатов в файл `test_commands_YYYYMMDD_HHMMSS.log`
- Итоговая статистика с процентом успешности

---

## 1. Docker - Управление контейнерами и сервисами

Категория `docker` управляет жизненным циклом Docker-контейнеров через `docker-compose`. Все команды выполняются на хост-машине.

### Базовые команды управления сервисами

```bash
# Сборка Docker-образов
./run.sh docker build
# Выполняет: docker-compose build

# Запуск сервисов
./run.sh docker up [SERVICE]
# Выполняет: docker-compose up -d [SERVICE]
# Запускает сервисы в фоновом режиме

# Остановка и удаление контейнеров
./run.sh docker down
# Выполняет: docker-compose down
# Останавливает и удаляет все контейнеры, сети и тома

# Остановка сервисов без удаления
./run.sh docker stop [SERVICE]
# Выполняет: docker-compose stop [SERVICE]

# Перезапуск сервисов
./run.sh docker restart [SERVICE]
# Выполняет: docker-compose restart [SERVICE]
```

### Команды очистки и пересборки

```bash
# Удаление контейнеров и образов проекта
./run.sh docker remove
# Удаляет все остановленные контейнеры и образы, связанные с проектом
# Определяется по метке com.docker.compose.project

# Полная пересборка
./run.sh docker rebuild
# Выполняет: down → remove → build → up
# Полная пересборка с удалением всех ресурсов

# Безопасная пересборка
./run.sh docker rebuild-safe
# Выполняет: stop → build → up
# Пересборка с сохранением данных в томах
```

### Команды синхронизации файлов

```bash
# Полная синхронизация с рестартом
./run.sh docker sync
# Копирует локальные файлы в контейнер litellm и перезапускает его
# Псевдоним для sync-restart

# Синхронизация без перезапуска
./run.sh docker sync-full
# Копирует все файлы проекта в контейнер litellm без перезапуска
```

### Команды мониторинга и логирования

```bash
# Просмотр логов
./run.sh docker logs [SERVICE]
# Показывает последние 100 строк логов

# Логи в реальном времени
./run.sh docker logs-follow [SERVICE]
# Отслеживает логи в реальном времени

# Очистка логов (требует sudo)
./run.sh docker logs-clear [SERVICE]
# Очищает файлы логов для контейнеров

# Статус сервисов
./run.sh docker status
# Показывает статус всех сервисов, их порты и доступность
```

---

## 2. Custom - Кастомные конфигурации Docker

Категория `custom` управляет различными режимами конфигурации LiteLLM при запуске через Docker.

### Режимы конфигурации

```bash
# Запуск с переменной окружения
./run.sh custom env CONFIG_PATH
# Устанавливает LITELLM_CONFIG_FILE и запускает с переменной окружения
# Файл остается на хост-системе, изменения требуют перезапуска

# Запуск с монтированием файла
./run.sh custom mount CONFIG_PATH
# Монтирует конфигурационный файл как read-only volume
# Создает временный docker-compose.override.yml
# Возможны обновления конфигурации в реальном времени

# Запуск с копированием файла
./run.sh custom cmd CONFIG_PATH
# Копирует файл в контейнер как litellm.config.custom.yaml
# Конфигурация становится частью контейнера

# Запуск со стандартной конфигурацией
./run.sh custom default
# Очищает все кастомные переменные и использует встроенную конфигурацию
# Минимальная настройка для тестирования

# Статус конфигурации
./run.sh custom status
# Показывает текущий метод конфигурации, переменные окружения,
# файлы конфигурации и Docker Compose overrides
```

---

## 3. Config - Управление конфигурацией

Категория `config` управляет конфигурацией через HTTP API LiteLLM. Команды выполняются на хост-машине, но взаимодействуют с запущенным сервисом.

### Базовые команды конфигурации

```bash
# Получение текущей конфигурации из базы данных
./run.sh config check
# Выполняет: python3 config_manager.py list
# ВНИМАНИЕ: Получает конфигурацию из БД LiteLLM, не локальную проверку файла

# Обновление конфигурации в базе данных
./run.sh config update [FILE]
# Выполняет: python3 config_manager.py update [FILE]
# Загружает конфигурацию из локального YAML-файла в БД через API

# Проверка URL-адресов сервисов
./run.sh config services
# Автоматически определяет контекст (Docker/локальный) и показывает URLs
```

### Управление моделями

```bash
# Список моделей в базе данных
./run.sh config models list
# Показывает все модели, зарегистрированные в БД LiteLLM

# Полная замена моделей
./run.sh config models update [FILE]
# ПОЛНОСТЬЮ заменяет все модели в БД на модели из файла

# Добавление моделей
./run.sh config models add [FILE]
# Добавляет модели из файла к существующим в БД

# Очистка всех моделей
./run.sh config models clear
# Удаляет все модели из БД (требует подтверждения)
```

### Управление настройками

```bash
# Список всех настроек
./run.sh config settings list
# Показывает все настройки из базы данных

# Обновление настроек роутера
./run.sh config settings update-router [FILE]
# Обновляет ТОЛЬКО секцию router_settings в БД из YAML-файла

# Просмотр настроек роутера
./run.sh config settings router
# Показывает текущие настройки маршрутизатора из БД
```

### Команды валидации

```bash
# Валидация конфигурации (псевдоним для config check)
./run.sh config validation check
# Получает конфигурацию из БД

# Проверка настроек роутера в БД
./run.sh config validation check-db
# Проверяет настройки маршрутизатора в базе данных

# Комплексная проверка системы
./run.sh config validation check-system
# Проверяет конфигурационные файлы, переменные окружения, доступность сервисов
```

---

## 4. Update - Обновления

Категория `update` для атомарных операций обновления.

### Обновление конфигурации

```bash
# Обновление локального файла конфигурации
./run.sh update config
# Выполняет: python3 update_config.py --config litellm.config.yaml
# Заменяет все значения api_key на значение из GPUSTACK_KEY
# НЕ влияет на конфигурацию в базе данных
```

### Обновление учетных данных GPUStack

```bash
# Обновление всех учетных данных GPUStack
./run.sh update gpustack [--yes]
# Получает учетные данные из контейнера gpustack-server и обновляет .env

# Обновление только токена
./run.sh update gpustack token
# Обновляет только GPUSTACK_TOKEN в .env

# Обновление только пароля
./run.sh update gpustack password
# Обновляет только GPUSTACK_PASSWORD в .env

# Создание/обновление API ключа
./run.sh update gpustack key
# Создает или обновляет GPUSTACK_KEY в .env через API
```

---

## 5. Test - Тестирование

Категория `test` для запуска различных тестов. Большинство тестов выполняются внутри Docker-контейнера `litellm`.

### Базовые тесты

```bash
# Проверка health endpoint
./run.sh test endpoint
# Проверяет доступность /health

# Тестирование всех моделей
./run.sh test models
# Тестирует все модели, определенные в scripts/testing/health.sh

# Тестирование конкретной модели
./run.sh test model MODEL_NAME
# Тестирует указанную модель
```

### Тесты моделей и предиктора

```bash
# Проверка сетевой доступности предиктора
./run.sh test models connectivity
# Проверяет сетевую доступность сервиса-предиктора

# Проверка функциональности предиктора
./run.sh test models functionality
# Проверяет полную функциональность предиктора

# Тест с managed_models конфигурацией
./run.sh test models managed-models
# Тестирует предиктор с конфигурацией из managed_models

# Отладка получения infinity от предиктора
./run.sh test models debug
# Отлаживает получение infinity от предиктора

# Тест интеграции роутера и предиктора
./run.sh test models integration
# Тестирует интеграцию роутера и предиктора

# Все тесты предиктора
./run.sh test models predictor-all
# Запускает все тесты, связанные с предиктором
```

### Тесты маршрутизатора и балансировки

```bash
# Простые тесты роутера (локально)
./run.sh test router simple
# Запускает простые тесты роутера локально

# Юнит-тесты роутера (в Docker)
./run.sh test router unit
# Запускает юнит-тесты роутера в Docker

# Тест интеграции least-busy
./run.sh test router least-busy
# Тестирует интеграцию с механизмом least-busy

# Тест высокой нагрузки
./run.sh test balancing high-load
# Запускает тест высокой нагрузки на балансировщик

# Стресс-тест
./run.sh test balancing stress
# Запускает стресс-тест балансировщика
```

### Тесты интеграции GPUStack

```bash
# Комплексный тест API GPUStack
./run.sh test gpustack api
# Полный тест API GPUStack

# Тест цикла обновления данных
./run.sh test gpustack data-update
# Тест цикла обновления данных и кэша

# Быстрая проверка работоспособности
./run.sh test gpustack health
# Быстрая проверка работоспособности интеграции

# Тест аутентификации
./run.sh test gpustack auth
# Тест аутентификации с GPUStack
```

### Тесты аутентификации

```bash
# Тест аутентификации GPUStack
./run.sh test auth gpustack
# Тест аутентификации с GPUStack

# Тест аутентификации LiteLLM
./run.sh test auth litellm
# Тест аутентификации с LiteLLM
```

### Тесты Prisma Client

Комплексное тестирование Prisma client connectivity, database operations и производительности для analytics системы. Все тесты выполняются внутри Docker-контейнера `litellm`.

```bash
# Тест подключения Prisma client
./run.sh test prisma status
# Тестирует полный цикл connect() → query_raw() → disconnect()

# Проверка analytics schema
./run.sh test prisma schema
# Проверяет существование analytics tables и подсчитывает записи

# Выполнение произвольного SQL запроса
./run.sh test prisma query "SQL"
# Выполняет SQL запрос через Prisma client для отладки
# Пример: ./run.sh test prisma query "SELECT COUNT(*) FROM \"LiteLLM_PromptLogs\""

# Тест mock mode functionality
./run.sh test prisma mock
# Тестирует работу без реального подключения к БД

# Тест context manager functionality
./run.sh test prisma context
# Тестирует автоматическое управление соединениями через async with

# Performance benchmark
./run.sh test prisma benchmark [N]
# Измеряет время connection/query/disconnect операций (default: 10 итераций)
# Пример: ./run.sh test prisma benchmark 50

# Stress testing
./run.sh test prisma stress [N]
# Тестирует concurrent connections (default: 5 connections)
# Пример: ./run.sh test prisma stress 20

# Полный набор Prisma тестов
./run.sh test prisma all
# Запускает все Prisma client тесты с summary reporting
```

**Пороги производительности для benchmark:**
- Excellent: < 100ms
- Good: < 500ms  
- Acceptable: < 1000ms
- Poor: > 1000ms

**Критерии успеха для stress test:**
- Success rate >= 80%

---

## 6. GPUStack - Операции с GPUStack

Категория `gpustack` для взаимодействия с API GPUStack. Команды выполняются внутри Docker-контейнера `litellm`.

### Управление ресурсами

```bash
# Список воркеров
./run.sh gpustack workers list
# Показывает список GPU workers

# Список запущенных моделей
./run.sh gpustack instances list
# Показывает список запущенных экземпляров моделей

# Сводная информация о кластере
./run.sh gpustack info summary
# Показывает общую информацию о кластере GPUStack

# Список моделей в GPUStack
./run.sh gpustack models list
# Показывает модели, зарегистрированные в GPUStack

# Создание моделей из файла
./run.sh gpustack models create [FILE]
# Создает модели в GPUStack из конфигурационного файла
```

### Управление учетными данными

```bash
# Управление учетными данными (см. update gpustack)
./run.sh gpustack credentials test
./run.sh gpustack credentials status
./run.sh gpustack credentials refresh
```

---

## 7. Debug - Отладка

Категория `debug` для диагностики и отладки системы.

```bash
# Статус трекера запросов роутера
./run.sh debug router status
# Показывает статус трекера запросов роутера

# Сброс счетчиков запросов
./run.sh debug router reset-all
# Сбрасывает все счетчики запросов роутера

# Локальная проверка конфигурации
./run.sh debug router local
# Запускает локальную проверку файла litellm.config.yaml
# Работает без зависимостей от Docker и запущенных сервисов
```

---

## 8. Analytics - Управление системой аналитики

Категория `analytics` управляет системой детального логирования решений роутинга и анализа производительности. Все команды работают с изолированной схемой базы данных через Prisma ORM.

### Управление миграциями

```bash
# Применение миграций в production
./run.sh analytics migrate deploy
# Выполняет: prisma migrate deploy --schema=analytics/schema.prisma
# Создает и обновляет таблицы аналитической системы

# Статус миграций
./run.sh analytics migrate status
# Выполняет: prisma migrate status --schema=analytics/schema.prisma
# Показывает какие миграции применены, какие ожидают применения

# Создание новой миграции
./run.sh analytics migrate create --name <n> --host
# Выполняет: prisma migrate dev --name <n> --schema=analytics/schema.prisma
# Требует флаг --host для подключения к БД

# Сброс таблиц Analytics (НЕ LiteLLM!)
./run.sh analytics migrate reset --force
# Сбрасывает ВСЕ таблицы Analytics, требует --force для подтверждения

# Baseline миграция для существующей БД
./run.sh analytics migrate baseline --host --yes
# Создает baseline миграцию при первичной настройке

# Пометка миграции как примененной
./run.sh analytics migrate resolve --applied <n>
# Помечает миграцию как примененную без выполнения SQL
```

### Управление типами схемы

```bash
# Генерация типов для Analytics
./run.sh analytics migrate generate
# Выполняет: prisma generate --schema=analytics/schema.prisma
# Создает типизированные модели для валидации

# Генерация типов в Docker окружении
./run.sh analytics database generate --host
# Генерирует типы схемы в контейнере litellm

# Валидация схемы Analytics
./run.sh analytics migrate validate --host
# Выполняет: prisma validate --schema=analytics/schema.prisma
# Проверяет корректность схемы и соответствие состоянию БД
```

### E2E тестирование (End-to-End Testing)

```bash
# Полный цикл E2E тестирования
./run.sh analytics test e2e
# Выполняет: python3 -m analytics.utils.e2e run
# 4-уровневая валидация: CRITICAL, BUSINESS, QUALITY, TIMING

# Тест отказа ML-предиктора
./run.sh analytics test failure --scenario=predictor-down
# Проверяет fallback-логику при недоступности предиктора

# Тест ошибок LLM API
./run.sh analytics test failure --scenario=llm-error
# Проверяет обработку ошибок от языковых моделей

# Тест таймаутов запросов
./run.sh analytics test failure --scenario=timeout
# Проверяет обработку превышения времени ожидания

# КРИТИЧЕСКИЙ тест отказа БД аналитики
./run.sh analytics test failure --scenario=db-failure
# Проверяет, что роутер остается функциональным при недоступности БД аналитики
```

### Управление данными аналитики

```bash
# Выполнение безопасных SELECT запросов
./run.sh analytics data query --query "SQL"
# Разрешены только SELECT запросы, автоматический таймаут 30 секунд
# Пример: ./run.sh analytics data query --query "SELECT COUNT(*) FROM \"LiteLLM_PromptLogs\""

# Экспорт результатов в файлы
./run.sh analytics data export --query "SQL" --output FILE --format FORMAT
# Форматы: json|csv (по умолчанию: json)
# Пример: ./run.sh analytics data export --query "SELECT * FROM \"LiteLLM_SpendLogs\"" --output logs.json

# Подсчет записей в таблицах
./run.sh analytics data counts [--period HOURS]
# Показывает количество записей во всех таблицах LiteLLM
# Пример: ./run.sh analytics data counts --period 24  # За последние 24 часа

# Проверка согласованности данных
./run.sh analytics data consistency
# Проверяет referential integrity между связанными таблицами

# Валидация качества данных
./run.sh analytics data quality
# Проверяет NULL в обязательных полях, аномальные значения, будущие временные метки

# Генерация тестовых данных
./run.sh analytics data seed [--count N] [--type TYPE]
# Типы: realistic (95% успешных), errors (20% ошибок), high-load (длительные времена)
# Примеры:
# ./run.sh analytics data seed                          # 100 realistic записей
# ./run.sh analytics data seed --count 500              # 500 записей
# ./run.sh analytics data seed --type errors --count 200 # 200 записей с ошибками

# Безопасная очистка старых данных
./run.sh analytics data cleanup [--days-old DAYS] [--confirm] [--all-tables]
# По умолчанию dry run режим, требует --confirm для реального удаления
# Примеры:
# ./run.sh analytics data cleanup                    # Dry run, Analytics таблицы, 30 дней
# ./run.sh analytics data cleanup --confirm          # Реальное удаление Analytics таблиц
# ./run.sh analytics data cleanup --all-tables       # Dry run для ВСЕХ таблиц (опасно!)
```

### Составные операции

```bash
# Полное восстановление Analytics после перезапуска LiteLLM
./run.sh analytics migrate reset --force && \
./run.sh analytics migrate baseline --host --yes && \
./run.sh analytics migrate deploy --host
# Восстанавливает работоспособность после удаления таблиц командой prisma db push --accept-data-loss
```

---

## 9. Analytics Reports - Аналитические отчеты

Категория `analytics reports` предоставляет интеллектуальные отчеты для анализа производительности роутинга, качества ML предсказаний и оптимизации системы.

### Основные отчеты

```bash
# Системный обзор и ключевые метрики
./run.sh analytics reports summary [OPTIONS]
# Главный индикатор здоровья системы с автоматическими рекомендациями
# Выполняет: python3 analytics/utils/report_generator.py summary

# Анализ процесса принятия решений роутером
./run.sh analytics reports decisions [OPTIONS]
# Показывает как и почему выбираются deployment'ы
# Опции: --request-id=ID, --limit=N, --model=MODEL, --deployment=ID

# Оценка точности ML предсказаний
./run.sh analytics reports accuracy [OPTIONS]
# Оценка точности по моделям и deployment'ам
# Опции: --threshold=0.8, --model=MODEL, --deployment=ID

# Глубокий анализ производительности системы
./run.sh analytics reports performance [OPTIONS]
# Времена отклика и bottleneck'и
# Опции: --slow-only, --failed-only, --deployment=ID

# Сравнительный анализ эффективности стратегий роутинга
./run.sh analytics reports quality [OPTIONS]
# Опции: --since=TIME, --period=HOURS, --format=FORMAT
```

### Глобальные опции для reports

```bash
# Фильтрация по времени
--period=HOURS                    # За последние N часов
--since='2024-01-15 14:30:00'     # С определенного времени
--since='2 days ago'              # Относительное время

# Фильтрация данных
--model=MODEL                     # По конкретной модели
--deployment=ID                   # По deployment ID
--limit=N                         # Ограничение количества записей

# Форматы вывода
--format=table|json|csv           # Формат вывода
--output=FILE                     # Сохранение в файл

# Отладка
--showsql                         # Показать SQL запросы
```

---

## 10. Analytics Tables - Отчеты по таблицам

Категория `analytics tables` предоставляет детальные отчеты по состоянию и содержанию таблиц базы данных.

### Отчеты по типам данных

```bash
# Анализ активных моделей
./run.sh analytics tables models [OPTIONS]
# Статистика использования моделей
# Выполняет: python3 analytics/utils/summary_generator.py models

# Состояние deployment'ов
./run.sh analytics tables deployments [OPTIONS]
# Конфигурация deployment'ов

# Статистика входящих запросов
./run.sh analytics tables prompts [OPTIONS]
# Статистика промптов и запросов
# Опции: --period-hours=HOURS, --since=TIME, --model=MODEL

# Статистика ML предиктора
./run.sh analytics tables predictors [OPTIONS]
# Статистика работы предиктора времени выполнения

# Статистика ответов системы
./run.sh analytics tables responses [OPTIONS]
# Времена выполнения и статистика ответов
# Опции: --model=MODEL, --deployment=ID

# Статистика API endpoint'ов
./run.sh analytics tables endpoints [OPTIONS]
# Статистика использования endpoint'ов
```

### Системные отчеты

```bash
# Сводная информация по всем таблицам
./run.sh analytics tables overview [OPTIONS]
# Выполняет: python3 analytics/utils/summary_generator.py overview

# Анализ актуальности данных
./run.sh analytics tables freshness
# Анализ свежести данных в таблицах

# Все отчеты по таблицам
./run.sh analytics tables all [OPTIONS]
# Генерирует все отчеты одновременно
# Опции: --output=DIR, --format=FORMAT
```

### Глобальные опции для tables

```bash
# Фильтрация по времени
--period-hours=HOURS              # За последние N часов

# Форматы и вывод
--format=table|json|csv           # Формат отчета
--output=FILE                     # Файл для сохранения
--output=DIR                      # Директория для множественных отчетов
```

---

## Быстрый справочник команд

### Ежедневные операции

```bash
# Запуск системы
./run.sh docker up

# Проверка статуса
./run.sh docker status
./run.sh test endpoint

# Обновление учетных данных
./run.sh update gpustack --yes

# Просмотр логов
./run.sh docker logs -f litellm
```

### Конфигурация и тестирование

```bash
# Валидация системы
./run.sh config validation check-system

# Тестирование моделей
./run.sh test models

# Проверка интеграций
./run.sh test gpustack auth
./run.sh test auth all
```

### Аналитика и мониторинг

```bash
# Статус аналитики
./run.sh analytics migrate status
./run.sh analytics test e2e

# Быстрые отчеты
./run.sh analytics reports summary
./run.sh analytics tables overview

# Проверка данных
./run.sh analytics data counts
./run.sh analytics data quality
```

### Отладка и диагностика

```bash
# Общая диагностика
./run.sh debug router status
./run.sh config check

# Тестирование Prisma
./run.sh test prisma all

# Проверка производительности
./run.sh test prisma benchmark
```

### Справка по командам

```bash
# Основная справка
./run.sh help

# Справка по категории
./run.sh help docker
./run.sh docker help
```

## Развертывание

### Локальное развертывание

```bash
# Полное развертывание с нуля
./run.sh docker up --build

# Проверка развертывания
./run.sh test endpoint
./run.sh docker status

# Просмотр логов
./run.sh docker logs -f litellm
```

### Продакшн развертывание

#### 1. Подготовка окружения

```bash
# Создание продакшн конфигурации
cp .env.example .env.production
cp litellm.config.yaml.example litellm.config.production.yaml

# Настройка безопасных паролей и ключей
openssl rand -hex 32  # Для LITELLM_MASTER_KEY
openssl rand -hex 32  # Для LITELLM_SALT_KEY
openssl rand -hex 32  # Для POSTGRES_PASSWORD
```

#### 2. Конфигурация для продакшена

```bash
# .env.production
LITELLM_LOG=WARNING
SSL_VERIFY=true
ENABLE_METRICS=true
MAX_CONCURRENT_REQUESTS=500
ANALYTICS_BATCH_SIZE=500
CACHE_TTL_GPUSTACK=600

# Настройка мониторинга
PROMETHEUS_ENABLED=true
GRAFANA_ENABLED=true
ALERT_WEBHOOK_URL=https://your-alerting-system.com/webhook
```

#### 3. Запуск продакшн системы

```bash
# Использование продакшн конфигурации
export ENV_FILE=.env.production
export CONFIG_FILE=litellm.config.production.yaml

# Запуск с продакшн настройками
./run.sh docker up -d --build

# Проверка развертывания
./run.sh test integration
./run.sh monitor health
```

### Docker Compose конфигурация

```yaml
version: '3.8'

services:
  litellm:
    build:
      context: .
      dockerfile: Dockerfile
      target: runtime
    ports:
      - "4000:4000"
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - LITELLM_MASTER_KEY=${LITELLM_MASTER_KEY}
      - PREDICT_URL=${PREDICT_URL}
      - GPUSTACK_URL=${GPUSTACK_URL}
    volumes:
      - ./litellm.config.yaml:/app/litellm.config.yaml:ro
      - ./logs:/app/logs
    depends_on:
      - db
      - redis
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  db:
    image: postgres:16
    environment:
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  prometheus_data:
```

## Мониторинг и обслуживание

### Мониторинг здоровья системы

```bash
# Ежедневные проверки
./run.sh monitor health
./run.sh docker status
./run.sh analytics health check

# Еженедельные проверки
./run.sh test integration
./run.sh debug validate
./run.sh analytics stats

# Ежемесячные проверки
./run.sh analytics cleanup
./run.sh config backup
./run.sh update system
```

### Логирование

#### Структура логов

```
logs/
├── litellm/
│   ├── access.log          # HTTP запросы
│   ├── error.log           # Ошибки системы
│   ├── routing.log         # Решения маршрутизации
│   └── performance.log     # Метрики производительности
├── analytics/
│   ├── database.log        # Операции с БД
│   └── migrations.log      # Миграции схемы
└── integrations/
    ├── gpustack.log        # GPUStack интеграция
    ├── predictor.log       # ML Predictor
    └── cache.log           # Операции кэширования
```

#### Настройка логирования

```python
# logging_utils.py
LOGGING_CONFIG = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'detailed': {
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        },
        'json': {
            'format': '{"timestamp": "%(asctime)s", "level": "%(levelname)s", "logger": "%(name)s", "message": "%(message)s"}'
        }
    },
    'handlers': {
        'file': {
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': 'logs/litellm/system.log',
            'maxBytes': 10485760,  # 10MB
            'backupCount': 5,
            'formatter': 'detailed'
        },
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'json'
        }
    },
    'loggers': {
        'llm_router': {
            'handlers': ['file', 'console'],
            'level': 'INFO',
            'propagate': False
        }
    }
}
```

### Резервное копирование

#### Автоматическое резервное копирование

```bash
#!/bin/bash
# backup.sh - скрипт автоматического резервного копирования

BACKUP_DIR="/backups/llm_router"
DATE=$(date +%Y%m%d_%H%M%S)

# Создание директории для бэкапов
mkdir -p "$BACKUP_DIR/$DATE"

# Резервное копирование конфигурации
./run.sh config backup "$BACKUP_DIR/$DATE/config_backup.tar.gz"

# Резервное копирование базы данных
docker exec llm_router_db_1 pg_dump -U llmproxy litellm > "$BACKUP_DIR/$DATE/database_backup.sql"

# Резервное копирование логов
tar -czf "$BACKUP_DIR/$DATE/logs_backup.tar.gz" logs/

# Очистка старых бэкапов (старше 30 дней)
find "$BACKUP_DIR" -type d -mtime +30 -exec rm -rf {} \;

echo "Backup completed: $BACKUP_DIR/$DATE"
```

#### Восстановление из резервной копии

```bash
# Восстановление конфигурации
./run.sh config restore /backups/llm_router/20240115_120000/config_backup.tar.gz

# Восстановление базы данных
docker exec -i llm_router_db_1 psql -U llmproxy litellm < /backups/llm_router/20240115_120000/database_backup.sql

# Перезапуск сервисов
./run.sh docker restart
```

### Обновление системы

#### Обновление компонентов

```bash
# Обновление Docker образов
./run.sh docker build --no-cache

# Обновление зависимостей Python
./run.sh update dependencies

# Обновление конфигурации
./run.sh update config

# Применение миграций БД
./run.sh analytics migrate up
```

#### Процедура обновления

1. **Подготовка**
   ```bash
   # Создание резервной копии
   ./run.sh config backup
   
   # Проверка текущего состояния
   ./run.sh monitor health
   ```

2. **Обновление**
   ```bash
   # Остановка сервисов
   ./run.sh docker down
   
   # Обновление кода
   git pull origin main
   
   # Пересборка образов
   ./run.sh docker build --no-cache
   ```

3. **Миграция данных**
   ```bash
   # Применение миграций
   ./run.sh analytics migrate up
   
   # Проверка миграций
   ./run.sh analytics migrate status
   ```

4. **Запуск и проверка**
   ```bash
   # Запуск сервисов
   ./run.sh docker up -d
   
   # Проверка работоспособности
   ./run.sh test integration
   ./run.sh monitor health
   ```

## Конфигурация для продакшена

### Оптимизация производительности

```yaml
# litellm.config.yaml - продакшн настройки
router_settings:
  routing_strategy: "llm-time-predictor"
  routing_strategy_args:
    hybrid_weight: 0.25          # Больший вес ML предсказаниям
    cache_ttl: 600               # Увеличенное время кэширования
    max_concurrent_requests: 500  # Увеличенный лимит запросов
    request_timeout: 30          # Таймаут запросов
    retry_attempts: 3            # Количество повторов
    circuit_breaker_threshold: 10 # Порог для circuit breaker

general_settings:
  database_url: "os.environ/DATABASE_URL"
  store_model_in_db: true
  max_budget: 1000.0
  budget_duration: "1d"
  
litellm_settings:
  drop_params: true
  set_verbose: false             # Отключение verbose в продакшене
  json_logs: true
  log_raw_request_response: false # Безопасность данных
  success_callback: ["prometheus", "analytics"]
  failure_callback: ["prometheus", "analytics"]
```

### Мониторинг производительности

```bash
# Настройка алертов
./run.sh monitor thresholds set response_time 5000  # 5 секунд
./run.sh monitor thresholds set error_rate 0.05     # 5% ошибок
./run.sh monitor thresholds set cpu_usage 0.80      # 80% CPU
./run.sh monitor thresholds set memory_usage 0.85   # 85% памяти

# Мониторинг в реальном времени
./run.sh monitor performance --interval 30  # Каждые 30 секунд
```

### Безопасность и аутентификация

```bash
# Настройка SSL/TLS
export SSL_CERT_PATH="/etc/ssl/certs/llm_router.crt"
export SSL_KEY_PATH="/etc/ssl/private/llm_router.key"

# Настройка firewall
sudo ufw allow 4000/tcp  # LiteLLM API
sudo ufw allow 9090/tcp  # Prometheus (только для мониторинга)
sudo ufw deny 5432/tcp   # PostgreSQL (только внутренний доступ)
sudo ufw deny 6379/tcp   # Redis (только внутренний доступ)

# Ротация ключей
./run.sh update credentials rotate-keys
```

---

*Следующий раздел: [Руководство по аналитике](ANALYTICS_GUIDE.md)*