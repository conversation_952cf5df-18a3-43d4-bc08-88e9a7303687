# Руководство по аналитике

Данное руководство описывает систему аналитики LLM Router, включая настройку, миграции базы данных, создание отчетов и устранение неполадок.

## Обзор системы аналитики

### Назначение системы

Система аналитики LLM Router предназначена для:

- **Сбора данных о маршрутизации** - логирование всех решений роутера
- **Анализа производительности** - оценка точности предсказаний и эффективности балансировки
- **Мониторинга системы** - отслеживание здоровья компонентов и интеграций
- **Оптимизации алгоритмов** - данные для улучшения ML моделей и стратегий маршрутизации

### Архитектура аналитики

```mermaid
graph TB
    subgraph "Источники данных"
        Router[Router Decisions]
        Predictor[ML Predictor Results]
        GPUStack[GPUStack Metrics]
        LiteLLM[LiteLLM Logs]
    end
    
    subgraph "Сбор данных"
        Logger[Analytics Logger]
        Queue[Async Queue]
        Builder[Data Builder]
    end
    
    subgraph "Хранилище"
        PostgreSQL[(PostgreSQL)]
        Tables[Analytics Tables]
    end
    
    subgraph "Анализ и отчеты"
        Reports[Report Generator]
        Metrics[Metrics Calculator]
        Export[Data Export]
    end
    
    Router --> Logger
    Predictor --> Logger
    GPUStack --> Logger
    LiteLLM --> Logger
    
    Logger --> Queue
    Queue --> Builder
    Builder --> PostgreSQL
    PostgreSQL --> Tables
    
    Tables --> Reports
    Tables --> Metrics
    Tables --> Export
    
    style Logger fill:#e8f5e8
    style PostgreSQL fill:#e1f5fe
    style Reports fill:#fff3e0
```

## Настройка аналитики

### Включение системы аналитики

В файле `.env`:

```bash
# Основные настройки аналитики
ANALYTICS_ENABLED=true
USE_ANALYTICS_MIGRATE=true

# Настройки производительности
ANALYTICS_BATCH_SIZE=100
ANALYTICS_FLUSH_INTERVAL=30
ANALYTICS_MAX_QUEUE_SIZE=10000

# Настройки базы данных
DATABASE_URL=********************************************/litellm
ANALYTICS_DB_POOL_SIZE=10
ANALYTICS_DB_MAX_OVERFLOW=20

# Настройки логирования
ANALYTICS_LOG_LEVEL=INFO
ANALYTICS_LOG_REQUESTS=true
ANALYTICS_LOG_RESPONSES=false  # Безопасность данных
```

### Конфигурация в litellm.config.yaml

```yaml
general_settings:
  database_url: "os.environ/DATABASE_URL"
  store_model_in_db: true
  
litellm_settings:
  success_callback: ["analytics"]
  failure_callback: ["analytics"]
  
router_settings:
  routing_strategy: "llm-time-predictor"
  routing_strategy_args:
    analytics_enabled: true
    log_routing_decisions: true
    log_performance_metrics: true
```

### Первоначальная настройка

```bash
# Проверка подключения к базе данных
./run.sh config test-database

# Применение миграций
./run.sh analytics migrate up

# Проверка статуса миграций
./run.sh analytics migrate status

# Проверка здоровья системы аналитики
./run.sh analytics health check
```

## Миграции базы данных

### Структура базы данных

Система аналитики использует следующие основные таблицы:

#### Таблица routing_decisions

```sql
CREATE TABLE routing_decisions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    request_id VARCHAR(255) NOT NULL,
    
    -- Информация о запросе
    request_type VARCHAR(50),
    request_length INTEGER,
    request_complexity FLOAT,
    model_name VARCHAR(255),
    
    -- Решение маршрутизации
    selected_deployment VARCHAR(255) NOT NULL,
    routing_strategy VARCHAR(100),
    decision_time_ms INTEGER,
    
    -- ML предсказания
    ml_prediction_time FLOAT,
    ml_confidence FLOAT,
    predictor_response_time_ms INTEGER,
    
    -- Гибридная оценка
    hybrid_score FLOAT,
    load_penalty FLOAT,
    hybrid_weight FLOAT,
    
    -- Альтернативы
    alternatives JSONB,
    
    -- Результат
    actual_response_time FLOAT,
    success BOOLEAN,
    error_message TEXT,
    
    -- Метаданные
    metadata JSONB,
    
    INDEX idx_routing_decisions_created_at ON routing_decisions(created_at),
    INDEX idx_routing_decisions_request_id ON routing_decisions(request_id),
    INDEX idx_routing_decisions_deployment ON routing_decisions(selected_deployment),
    INDEX idx_routing_decisions_model ON routing_decisions(model_name)
);
```

#### Таблица deployment_metrics

```sql
CREATE TABLE deployment_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    measured_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deployment_name VARCHAR(255) NOT NULL,
    
    -- Метрики производительности
    avg_response_time FLOAT,
    min_response_time FLOAT,
    max_response_time FLOAT,
    p95_response_time FLOAT,
    p99_response_time FLOAT,
    
    -- Метрики нагрузки
    active_requests INTEGER,
    total_requests INTEGER,
    success_rate FLOAT,
    error_rate FLOAT,
    
    -- GPU метрики (если доступны)
    gpu_utilization FLOAT,
    gpu_memory_used FLOAT,
    gpu_memory_total FLOAT,
    gpu_temperature FLOAT,
    
    -- Метаданные оборудования
    hardware_specs JSONB,
    model_info JSONB,
    
    INDEX idx_deployment_metrics_measured_at ON deployment_metrics(measured_at),
    INDEX idx_deployment_metrics_deployment ON deployment_metrics(deployment_name)
);
```

#### Таблица prediction_accuracy

```sql
CREATE TABLE prediction_accuracy (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    request_id VARCHAR(255) NOT NULL,
    deployment_name VARCHAR(255) NOT NULL,
    
    -- Предсказание vs реальность
    predicted_time FLOAT NOT NULL,
    actual_time FLOAT,
    accuracy_error FLOAT,
    accuracy_percentage FLOAT,
    
    -- Контекст предсказания
    request_features JSONB,
    model_version VARCHAR(100),
    predictor_confidence FLOAT,
    
    INDEX idx_prediction_accuracy_created_at ON prediction_accuracy(created_at),
    INDEX idx_prediction_accuracy_deployment ON prediction_accuracy(deployment_name)
);
```

### Управление миграциями

#### Применение миграций

```bash
# Просмотр доступных миграций
./run.sh analytics migrate list

# Применение всех новых миграций
./run.sh analytics migrate up

# Применение конкретной миграции
./run.sh analytics migrate up --target 20240115_001_add_prediction_accuracy

# Проверка статуса миграций
./run.sh analytics migrate status
```

#### Откат миграций

```bash
# Откат последней миграции
./run.sh analytics migrate down

# Откат до конкретной миграции
./run.sh analytics migrate down --target 20240110_001_initial_schema

# Откат всех миграций (осторожно!)
./run.sh analytics migrate down --all
```

#### Создание новых миграций

```bash
# Создание новой миграции
./run.sh analytics migrate create add_new_metrics_table

# Редактирование созданной миграции
# Файл будет создан в analytics/migrations/
```

Пример миграции:

```sql
-- analytics/migrations/20240115_002_add_cache_metrics.sql
-- Up migration
CREATE TABLE cache_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    cache_type VARCHAR(50) NOT NULL,
    hit_rate FLOAT,
    miss_rate FLOAT,
    total_requests INTEGER,
    avg_response_time_ms FLOAT,
    
    INDEX idx_cache_metrics_created_at ON cache_metrics(created_at),
    INDEX idx_cache_metrics_type ON cache_metrics(cache_type)
);

-- Down migration
-- DROP TABLE cache_metrics;
```

### Резервное копирование данных

```bash
# Создание резервной копии аналитических данных
./run.sh analytics backup create

# Создание резервной копии с указанием периода
./run.sh analytics backup create --from "2024-01-01" --to "2024-01-31"

# Восстановление из резервной копии
./run.sh analytics backup restore backup_20240115_120000.sql

# Очистка старых данных (старше 90 дней)
./run.sh analytics cleanup --older-than 90
```

## Отчеты и запросы

### Стандартные отчеты

#### Отчет по производительности маршрутизации

```bash
# Генерация отчета за последние 24 часа
./run.sh analytics reports routing-performance --period 24h

# Отчет за конкретный период
./run.sh analytics reports routing-performance --from "2024-01-01" --to "2024-01-31"

# Отчет по конкретному deployment
./run.sh analytics reports routing-performance --deployment gpu-h100-1
```

Пример отчета:

```
=== Отчет по производительности маршрутизации ===
Период: 2024-01-15 00:00:00 - 2024-01-15 23:59:59
Общее количество запросов: 15,847

Топ deployments по количеству запросов:
1. gpu-h100-1:     5,234 (33.0%) - Avg: 2.3s, P95: 4.1s
2. gpu-a6000-1:    4,891 (30.9%) - Avg: 3.1s, P95: 5.2s
3. gpu-rtx4090-1:  3,456 (21.8%) - Avg: 4.2s, P95: 7.1s
4. cpu-large-1:    2,266 (14.3%) - Avg: 8.7s, P95: 15.2s

Точность ML предсказаний:
- Средняя ошибка: 15.2%
- Медианная ошибка: 12.1%
- Предсказания в пределах 20%: 78.5%
- Предсказания в пределах 50%: 94.2%

Эффективность гибридной балансировки:
- Hybrid weight: 0.30
- Улучшение по сравнению с random: +23.4%
- Улучшение по сравнению с least-busy: +12.1%
```

#### Отчет по точности предсказаний

```bash
# Анализ точности ML предсказаний
./run.sh analytics reports prediction-accuracy --period 7d

# Сравнение точности по типам запросов
./run.sh analytics reports prediction-accuracy --group-by request_type

# Анализ точности по deployments
./run.sh analytics reports prediction-accuracy --group-by deployment
```

#### Отчет по использованию ресурсов

```bash
# Отчет по использованию GPU
./run.sh analytics reports resource-usage --resource gpu

# Отчет по нагрузке на deployments
./run.sh analytics reports load-distribution --period 24h

# Отчет по эффективности кэширования
./run.sh analytics reports cache-efficiency
```

### Пользовательские запросы

#### SQL запросы для анализа

```sql
-- Топ 10 самых медленных запросов за последний день
SELECT 
    request_id,
    model_name,
    selected_deployment,
    actual_response_time,
    ml_prediction_time,
    ABS(actual_response_time - ml_prediction_time) as prediction_error
FROM routing_decisions 
WHERE created_at >= NOW() - INTERVAL '1 day'
    AND actual_response_time IS NOT NULL
ORDER BY actual_response_time DESC 
LIMIT 10;

-- Анализ точности предсказаний по типам запросов
SELECT 
    request_type,
    COUNT(*) as total_requests,
    AVG(ABS(actual_response_time - ml_prediction_time) / actual_response_time * 100) as avg_error_percent,
    PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY ABS(actual_response_time - ml_prediction_time) / actual_response_time * 100) as median_error_percent
FROM routing_decisions 
WHERE created_at >= NOW() - INTERVAL '7 days'
    AND actual_response_time IS NOT NULL 
    AND ml_prediction_time IS NOT NULL
    AND actual_response_time > 0
GROUP BY request_type
ORDER BY avg_error_percent;

-- Эффективность балансировки нагрузки
SELECT 
    selected_deployment,
    COUNT(*) as request_count,
    AVG(actual_response_time) as avg_response_time,
    PERCENTILE_CONT(0.95) WITHIN GROUP (ORDER BY actual_response_time) as p95_response_time,
    AVG(load_penalty) as avg_load_penalty
FROM routing_decisions 
WHERE created_at >= NOW() - INTERVAL '1 day'
    AND actual_response_time IS NOT NULL
GROUP BY selected_deployment
ORDER BY request_count DESC;
```

#### Выполнение пользовательских запросов

```bash
# Выполнение SQL запроса из файла
./run.sh analytics query --file custom_analysis.sql

# Выполнение запроса с выводом в CSV
./run.sh analytics query --file analysis.sql --format csv --output results.csv

# Интерактивный режим SQL
./run.sh analytics query --interactive
```

### Экспорт данных

```bash
# Экспорт всех данных за период
./run.sh analytics data export --from "2024-01-01" --to "2024-01-31" --format json

# Экспорт конкретной таблицы
./run.sh analytics data export --table routing_decisions --format csv

# Экспорт с фильтрацией
./run.sh analytics data export --table routing_decisions --where "success = true" --format parquet
```

## Мониторинг аналитики

### Проверка здоровья системы

```bash
# Общая проверка здоровья
./run.sh analytics health check

# Детальная диагностика
./run.sh analytics health detailed

# Проверка подключения к БД
./run.sh analytics health database

# Проверка очереди обработки
./run.sh analytics health queue
```

Пример вывода:

```
=== Статус системы аналитики ===
✓ Подключение к базе данных: OK
✓ Миграции: Актуальные (версия 20240115_003)
✓ Очередь обработки: 23 элементов (норма)
✓ Производительность записи: 1,247 записей/сек
⚠ Размер базы данных: 2.3 GB (рекомендуется очистка)
✓ Индексы: Оптимизированы
✓ Резервное копирование: Последнее 2 часа назад

Рекомендации:
- Выполнить очистку данных старше 90 дней
- Проверить настройки автоматического резервного копирования
```

### Метрики производительности

```bash
# Статистика производительности
./run.sh analytics stats performance

# Статистика использования
./run.sh analytics stats usage

# Статистика точности
./run.sh analytics stats accuracy
```

### Алерты и уведомления

Настройка алертов в `.env`:

```bash
# Настройки алертов
ANALYTICS_ALERTS_ENABLED=true
ANALYTICS_ALERT_WEBHOOK=https://your-webhook-url.com/alerts

# Пороговые значения
ANALYTICS_ALERT_QUEUE_SIZE=5000
ANALYTICS_ALERT_ERROR_RATE=0.05
ANALYTICS_ALERT_DB_SIZE_GB=5.0
ANALYTICS_ALERT_PREDICTION_ERROR=0.30
```

Типы алертов:

1. **Переполнение очереди** - когда очередь превышает пороговое значение
2. **Высокий уровень ошибок** - когда процент ошибок превышает норму
3. **Проблемы с БД** - недоступность или медленная работа базы данных
4. **Низкая точность предсказаний** - когда точность ML падает ниже порога

## Устранение неполадок

### Частые проблемы

#### Проблема: Данные не записываются в базу

**Диагностика:**
```bash
./run.sh analytics health queue
./run.sh analytics health database
./run.sh docker logs litellm | grep analytics
```

**Решения:**
1. Проверить подключение к базе данных
2. Проверить настройки ANALYTICS_ENABLED
3. Проверить размер очереди и производительность
4. Перезапустить сервис аналитики

#### Проблема: Медленная работа запросов

**Диагностика:**
```bash
./run.sh analytics stats performance
./run.sh analytics query --file "EXPLAIN ANALYZE SELECT * FROM routing_decisions LIMIT 1000;"
```

**Решения:**
1. Проверить индексы базы данных
2. Очистить старые данные
3. Оптимизировать запросы
4. Увеличить ресурсы базы данных

#### Проблема: Переполнение очереди

**Диагностика:**
```bash
./run.sh analytics health queue
./run.sh debug status
```

**Решения:**
1. Увеличить ANALYTICS_BATCH_SIZE
2. Уменьшить ANALYTICS_FLUSH_INTERVAL
3. Увеличить ресурсы базы данных
4. Временно отключить аналитику

### Команды отладки

```bash
# Отладка очереди аналитики
./run.sh debug analytics queue

# Отладка производительности БД
./run.sh debug analytics database

# Отладка миграций
./run.sh debug analytics migrations

# Проверка целостности данных
./run.sh analytics validate data-integrity
```

### Восстановление после сбоев

#### Восстановление очереди

```bash
# Очистка заблокированной очереди
./run.sh analytics queue clear

# Перезапуск обработчика очереди
./run.sh analytics queue restart

# Повторная обработка неудачных записей
./run.sh analytics queue retry-failed
```

#### Восстановление базы данных

```bash
# Проверка целостности БД
./run.sh analytics database check-integrity

# Восстановление индексов
./run.sh analytics database rebuild-indexes

# Восстановление из резервной копии
./run.sh analytics backup restore latest
```

## Оптимизация производительности

### Настройки производительности

```bash
# Оптимизация для высокой нагрузки
ANALYTICS_BATCH_SIZE=500
ANALYTICS_FLUSH_INTERVAL=10
ANALYTICS_DB_POOL_SIZE=20
ANALYTICS_MAX_QUEUE_SIZE=50000

# Оптимизация для экономии ресурсов
ANALYTICS_BATCH_SIZE=50
ANALYTICS_FLUSH_INTERVAL=60
ANALYTICS_DB_POOL_SIZE=5
ANALYTICS_MAX_QUEUE_SIZE=5000
```

### Партиционирование таблиц

Для больших объемов данных рекомендуется партиционирование:

```sql
-- Партиционирование по дате для routing_decisions
CREATE TABLE routing_decisions_y2024m01 PARTITION OF routing_decisions
FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');

CREATE TABLE routing_decisions_y2024m02 PARTITION OF routing_decisions
FOR VALUES FROM ('2024-02-01') TO ('2024-03-01');
```

### Архивирование данных

```bash
# Автоматическое архивирование данных старше 6 месяцев
./run.sh analytics archive --older-than 180 --compress

# Восстановление архивных данных
./run.sh analytics archive restore archive_2023_q4.tar.gz
```

---

*Следующий раздел: [Руководство по тестированию](TESTING_GUIDE.md)*