# Руководство по аналитике LLM Router

Данное руководство описывает систему аналитики LLM Router, включая настройку, миграции базы данных, создание отчетов и устранение неполадок.

## Обзор системы аналитики

### Возможности системы

Система аналитики для детального логирования решений роутинга и производительности LLM запросов, интегрированная с гибридным алгоритмом балансировки нагрузки.

- **Детальное логирование роутинга**: Полная трассировка hybrid scoring с ML предсказаниями, least-busy данными и GPUStack интеграцией
- **Анализ производительности**: Точность ML предсказаний, реальное время ответа, токены, сравнение предсказанного vs фактического времени
- **Endpoint анализ**: Детальные данные по каждому endpoint'у в процессе выбора (hybrid scores, ranking, selection rationale)
- **Справочные данные**: Автоматическое ведение нормализованных справочников моделей и деплойментов
- **Асинхронное логирование**: Неблокирующая запись в PostgreSQL через async queue для высокой производительности
- **Отказоустойчивость**: Ошибки логирования не влияют на основной поток запросов, graceful degradation к mock режиму

### Назначение системы

Система аналитики LLM Router предназначена для:

- **Сбора данных о маршрутизации** - логирование всех решений роутера
- **Анализа производительности** - оценка точности предсказаний и эффективности балансировки
- **Мониторинга системы** - отслеживание здоровья компонентов и интеграций
- **Оптимизации алгоритмов** - данные для улучшения ML моделей и стратегий маршрутизации

### Архитектура аналитики

```mermaid
graph TB
    subgraph "Источники данных"
        Router[Router Decisions]
        Predictor[ML Predictor Results]
        GPUStack[GPUStack Metrics]
        LiteLLM[LiteLLM Logs]
    end
    
    subgraph "Сбор данных"
        Logger[Analytics Logger]
        Queue[Async Queue]
        Builder[Data Builder]
    end
    
    subgraph "Хранилище"
        PostgreSQL[(PostgreSQL)]
        Tables[Analytics Tables]
    end
    
    subgraph "Анализ и отчеты"
        Reports[Report Generator]
        Metrics[Metrics Calculator]
        Export[Data Export]
    end
    
    Router --> Logger
    Predictor --> Logger
    GPUStack --> Logger
    LiteLLM --> Logger
    
    Logger --> Queue
    Queue --> Builder
    Builder --> PostgreSQL
    PostgreSQL --> Tables
    
    Tables --> Reports
    Tables --> Metrics
    Tables --> Export
    
    style Logger fill:#e8f5e8
    style PostgreSQL fill:#e1f5fe
    style Reports fill:#fff3e0
```

## Настройка аналитики

### Быстрый старт

#### 1. Установка зависимостей

```bash
# PostgreSQL для базы данных
pip install asyncpg

# Prisma для управления схемой (опционально)
npm install -g prisma
pip install prisma
```

#### 2. Настройка переменных окружения

```bash
# Основные настройки
export DATABASE_URL="postgresql://user:password@localhost:5432/analytics"
export ANALYTICS_ENABLED=true

# Дополнительные настройки (опционально)
export ANALYTICS_USE_ASYNC_QUEUE=true
export ANALYTICS_QUEUE_MAX_SIZE=1000
export ANALYTICS_USE_SQL=true
```

#### 3. Инициализация базы данных

```bash
# Автоматическая настройка
cd llm-router/analytics
python migration_utils.py setup --force

# Или шаг за шагом
python migration_utils.py migrate --name init_analytics
python migration_utils.py generate
```

#### 4. Интеграция с LiteLLM

```python
import litellm
from analytics import setup_analytics_integration

# Инициализация аналитики
analytics_logger = await setup_analytics_integration()

# Автоматическая регистрация callbacks
# litellm.success_callback.append(analytics_logger) - автоматически
# litellm.failure_callback.append(analytics_logger) - автоматически
```

#### 5. Автоматическая интеграция с Custom Router

Система аналитики автоматически интегрируется с существующим `LLMTimePredictorRoutingStrategy` через metadata:

```python
# В custom_router.py уже встроена поддержка аналитики
class LLMTimePredictorRoutingStrategy(CustomRoutingStrategyBase):
    async def get_available_deployment(self, model, messages=None, input=None, request_kwargs=None):
        # Hybrid scoring analysis
        predictor_results = await self._analyze_with_predictor(...)
        least_busy_data = self._get_least_busy_data(...)
        selected_deployment = self._select_optimal_deployment(...)
        
        # Автоматическое сохранение данных для аналитики
        router_analysis_data = {
            "predictor_results": predictor_results,
            "hybrid_scores": hybrid_scores,
            "selected_deployment_id": selected_deployment.model_info.id,
            "endpoint_analysis": detailed_endpoint_data
        }
        
        # Данные сохраняются в metadata для callbacks
        add_router_analysis_to_metadata(request_kwargs, router_analysis_data)
        
        return selected_deployment
```

### Включение системы аналитики

В файле `.env`:

```bash
# Основные настройки аналитики
ANALYTICS_ENABLED=true
USE_ANALYTICS_MIGRATE=true

# Настройки производительности
ANALYTICS_BATCH_SIZE=100
ANALYTICS_FLUSH_INTERVAL=30
ANALYTICS_MAX_QUEUE_SIZE=10000

# Настройки базы данных
DATABASE_URL=********************************************/litellm
ANALYTICS_DB_POOL_SIZE=10
ANALYTICS_DB_MAX_OVERFLOW=20

# Настройки логирования
ANALYTICS_LOG_LEVEL=INFO
ANALYTICS_LOG_REQUESTS=true
ANALYTICS_LOG_RESPONSES=false  # Безопасность данных
```

### Конфигурация

#### Переменные окружения

| Переменная | Значение по умолчанию | Описание |
|------------|----------------------|----------|
| `ANALYTICS_ENABLED` | `true` | Включить/выключить аналитику |
| `DATABASE_URL` | - | URL подключения к PostgreSQL |
| `ANALYTICS_USE_ASYNC_QUEUE` | `true` | Использовать асинхронную очередь |
| `ANALYTICS_QUEUE_MAX_SIZE` | `1000` | Максимальный размер очереди |
| `ANALYTICS_USE_SQL` | `true` | Использовать SQL операции |
| `ANALYTICS_DB_TIMEOUT` | `30` | Таймаут подключения к БД (секунды) |
| `ANALYTICS_DB_MAX_CONNECTIONS` | `10` | Максимальное количество соединений |
| `ANALYTICS_FALLBACK_TO_MINIMAL` | `true` | Переход на минимальное логирование при ошибках |
| `ANALYTICS_RETRY_ATTEMPTS` | `3` | Количество попыток при ошибке |
| `ANALYTICS_LOG_LEVEL` | `INFO` | Уровень логирования |

#### Режимы работы

**1. Полный режим** (с базой данных):
```python
# DATABASE_URL установлен
analytics_logger = RouterAnalyticsLogger(enabled=True)
```

**2. Mock режим** (без базы данных):
```python
# DATABASE_URL не установлен
analytics_logger = RouterAnalyticsLogger(enabled=True)  # Логирует в консоль
```

**3. Отключенный режим**:
```python
analytics_logger = RouterAnalyticsLogger(enabled=False)  # Ничего не делает
```

### Конфигурация в litellm.config.yaml

```yaml
general_settings:
  database_url: "os.environ/DATABASE_URL"
  store_model_in_db: true
  
litellm_settings:
  success_callback: ["analytics"]
  failure_callback: ["analytics"]
  
router_settings:
  routing_strategy: "llm-time-predictor"
  routing_strategy_args:
    analytics_enabled: true
    log_routing_decisions: true
    log_performance_metrics: true
```

### Первоначальная настройка

```bash
# Проверка подключения к базе данных
./run.sh config test-database

# Применение миграций
./run.sh analytics migrate up

# Проверка статуса миграций
./run.sh analytics migrate status

# Проверка здоровья системы аналитики
./run.sh analytics health check
```

## Миграции базы данных

### Краткая справка - где что выполняется

**Основной принцип:** Миграции на хосте, SQL операции в Docker.

| Действие | Команда | Где |
|----------|---------|-----|
| Создать базовую точку | `./run.sh analytics migrate baseline --host --yes` | Хост |
| Применить миграции | `./run.sh analytics migrate deploy --host` | Хост |
| Генерация типов схемы | `./run.sh analytics database generate` | Docker |
| Проверить работу | `./run.sh analytics test sql` | Docker |

**Быстрое восстановление после сброса LiteLLM:**
```bash
./run.sh analytics migrate reset --force && \
./run.sh analytics migrate baseline --host --yes && \
./run.sh analytics migrate deploy --host && \
./run.sh analytics database generate
```

### Критическая угроза: Удаление таблиц Analytics

Это самая серьезная проблема, с которой вы столкнетесь.

**Проблема:** При каждом перезапуске контейнера **LiteLLM автоматически удаляет все таблицы Analytics**.

**Причина:** В стартовом скрипте LiteLLM выполняется команда `prisma db push --accept-data-loss`. Эта команда принудительно синхронизирует схему базы данных со схемой `schema.prisma` самого LiteLLM. Поскольку в схеме LiteLLM нет таблиц Analytics, Prisma их удаляет.

**Симптомы:**
- Ваш сервис аналитики перестает работать.
- При проверке статуса миграций Analytics вы видите сообщение `Database schema is up to date!`.
- Это происходит потому, что записи о применении миграций остаются в таблице `_prisma_migrations`, но сами таблицы физически удалены из БД.

**Решение:** Необходимо вручную откатить записи о миграциях и применить их заново.

### Архитектура и корень проблемы

Конфликты возникают из-за специфической архитектуры проекта:

- **Общая база данных:** И LiteLLM (около 30 таблиц), и Analytics (6 таблиц) используют одну и ту же базу данных PostgreSQL.
- **Раздельные схемы Prisma:** У каждого модуля своя папка с файлом `schema.prisma` и своя история миграций в таблице `_prisma_migrations`.

```
База данных PostgreSQL: "litellm"
├── Таблицы LiteLLM (управляются схемой LiteLLM)
│   ├── LiteLLM_UserTable
│   └── ...
│
└── Таблицы Analytics (управляются схемой Analytics)
    ├── LiteLLM_Models
    └── ...
```

### Ключевые Analytics таблицы

1. **LiteLLM_Models** - модели ИИ и их параметры
2. **LiteLLM_Deployments** - развертывания моделей и конфигурации 
3. **LiteLLM_PromptLogs** - логи входящих запросов
4. **LiteLLM_PredictorLogs** - логи работы ML предиктора
5. **LiteLLM_ResponseLogs** - логи ответов от моделей
6. **LiteLLM_EndpointAnalysis** - анализ производительности эндпоинтов

### Стратегия решения: Механизм Baseline

**Baseline** — это специальная "нулевая" миграция, которая говорит Prisma: "Не обращай внимания на уже существующие таблицы. Считай, что они уже были созданы, и начинай свою работу поверх них".

**Как это работает:**
1. Мы создаем SQL-снапшот текущего состояния базы данных (всех таблиц LiteLLM).
2. Этот SQL-файл помещается в первую папку миграций Analytics (например, `000000000000_baseline`).
3. Мы помечаем эту baseline-миграцию как уже примененную с помощью команды `prisma migrate resolve`.

### Пошаговые инструкции

#### Первичная настройка Analytics (новая база)

Выполните эти шаги, если вы настраиваете модуль Analytics в первый раз на работающей системе с LiteLLM.

**Шаг 1: Убедитесь, что LiteLLM работает и создал свои таблицы.**

```bash
# Проверяем, что контейнер запущен
docker ps | grep litellm

# Проверяем наличие таблиц в БД
psql "$DATABASE_URL_HOST" -c "SELECT count(*) FROM information_schema.tables WHERE table_schema = 'public' AND table_name LIKE 'LiteLLM_%';"
# Ожидаемый результат: число около 30
```

**Шаг 2: Создайте baseline-миграцию (НА ХОСТЕ).**

```bash
./run.sh analytics migrate baseline --host --yes
```

**Шаг 3: Примените миграции Analytics (НА ХОСТЕ).**

```bash
./run.sh analytics migrate deploy --host
```

**Шаг 4: Сгенерируйте типы схемы (В DOCKER).**

```bash
./run.sh analytics database generate
```

**Шаг 5: Проверьте результат.**

```bash
# Проверяем статус миграций (НА ХОСТЕ)
./run.sh analytics migrate status --host
# Ожидаемый вывод: "Database schema is up to date!"

# Проверяем работу SQL операций (В DOCKER)
./run.sh analytics test sql
```

#### Восстановление после перезапуска LiteLLM

**Проблема:** LiteLLM периодически пересоздает всю базу данных, удаляя таблицы Analytics.

**Решение:** Быстрое восстановление без потери конфигурации.

**Шаг 1: Сбросьте историю миграций (НА ХОСТЕ).**

```bash
./run.sh analytics migrate reset --force
```

**Шаг 2: Восстановите схему базы данных (НА ХОСТЕ).**

```bash
# Создаем baseline из текущих таблиц LiteLLM
./run.sh analytics migrate baseline --host --yes

# Применяем миграции для восстановления таблиц Analytics
./run.sh analytics migrate deploy --host
```

**Шаг 3: Обновите типы схемы (В DOCKER).**

```bash
# Генерируем типы схемы
./run.sh analytics database generate
```

#### Обновление схемы Analytics (добавление новых таблиц/полей)

**Когда использовать:** При добавлении новых таблиц или изменении существующих в схеме Analytics.

**Шаг 1: Измените файл схемы.**

Отредактируйте файл `analytics/schema.prisma`, добавив новую модель или изменив существующую.

**Шаг 2: Создайте новую миграцию (НА ХОСТЕ).**

```bash
# Замените "add_my_new_table" на осмысленное имя
./run.sh analytics migrate create --name add_my_new_table --host
```

**Шаг 3: Примените новую миграцию (НА ХОСТЕ).**

```bash
./run.sh analytics migrate deploy --host
```

**Шаг 4: Обновите типы схемы (В DOCKER).**

```bash
# Генерируем новые типы с обновленными моделями
./run.sh analytics database generate
```

### Управление миграциями

#### CLI команды

```bash
# Статус миграций
python -m analytics.migration_utils status

# Применить миграции
python -m analytics.migration_utils migrate --name add_new_feature

# Сброс БД и применение всех миграций
python -m analytics.migration_utils reset --force

# Генерация типов из схемы
python -m analytics.migration_utils generate

# Полная настройка
python -m analytics.migration_utils setup --force
```

#### Программное управление

```python
from analytics.migration_utils import migration_manager

# Проверка статуса
await migration_manager.migrate_status()

# Применение миграций
await migration_manager.migrate_deploy()

# Генерация клиента
await migration_manager.generate_client()
```

### Резервное копирование данных

```bash
# Создание резервной копии аналитических данных
./run.sh analytics backup create

# Создание резервной копии с указанием периода
./run.sh analytics backup create --from "2024-01-01" --to "2024-01-31"

# Восстановление из резервной копии
./run.sh analytics backup restore backup_20240115_120000.sql

# Очистка старых данных (старше 90 дней)
./run.sh analytics cleanup --older-than 90
```

## Отчеты и запросы

### Система аналитических отчетов

Система аналитических отчетов предоставляет две категории отчетов для всестороннего анализа работы LLM-Router:

#### Аналитические отчеты (analytics reports)

Специализированные отчеты для анализа производительности и качества роутинга:

| Отчет | Назначение | Ключевые метрики |
|-------|------------|------------------|
| **Summary** | Системный обзор | Общая производительность, качество, рекомендации |
| **Decisions** | Анализ роутинга | Процесс выбора deployment, ранжирование, эффективность |
| **Accuracy** | Точность предсказаний | ML accuracy по моделям и deployment'ам |
| **Performance** | Производительность | Времена отклика, перцентили, сбои |
| **Quality** | Качество роутинга | Эффективность стратегий выбора deployment |

#### Отчеты по таблицам (tables reports)

Детальные отчеты по состоянию и содержанию таблиц базы данных:

| Отчет | Назначение | Информация |
|-------|------------|------------|
| **Models** | Анализ моделей | Список активных моделей, статистика использования |
| **Deployments** | Состояние deployment'ов | Активные deployment'ы, их конфигурация |
| **Prompts** | Логи промптов | Статистика запросов, популярные модели |
| **Predictors** | Логи предиктора | Производительность ML предсказаний |
| **Responses** | Логи ответов | Статистика ответов, время выполнения |
| **Endpoints** | Анализ endpoint'ов | Использование API endpoint'ов |
| **Overview** | Общий обзор | Сводная информация по всем таблицам |
| **Freshness** | Свежесть данных | Актуальность данных в таблицах |

### Основные команды отчетов

```bash
# Системный обзор (самый важный отчет)
./run.sh analytics reports summary

# Обзор всех таблиц
./run.sh analytics tables overview

# Список всех команд
./run.sh analytics reports help
./run.sh analytics tables help

# Справка по конкретной команде
./run.sh analytics reports summary --help
./run.sh analytics tables models --help
```

### Типы отчетов

#### 1. Summary Report - Сводный отчет системы

**Команда**: `./run.sh analytics reports summary`

**Назначение**: Главный индикатор здоровья системы. Предоставляет ключевые метрики и автоматические рекомендации.

**Что анализирует**:
- Общая производительность системы (запросы, успешность, времена отклика)
- Качество ML предсказаний (средняя точность)
- Автоматические рекомендации по оптимизации

**Кейсы использования**:
- **Ежедневный мониторинг**: Быстрая оценка состояния системы
- **Incident response**: Первый отчет при расследовании проблем
- **Executive reporting**: Высокоуровневые метрики для руководства
- **SLA monitoring**: Контроль соблюдения целевых показателей

#### 2. Decisions Report - Анализ решений роутера

**Команда**: `./run.sh analytics reports decisions`

**Назначение**: Детальный анализ процесса принятия решений роутером. Показывает как и почему выбираются конкретные deployment'ы.

**Два режима работы**:

**Общий режим** (по умолчанию):
```bash
./run.sh analytics reports decisions --limit=20
```

**Режим анализа конкретного запроса**:
```bash
./run.sh analytics reports decisions --request-id=abc123
```

#### 3. Accuracy Report - Анализ точности предсказаний

**Команда**: `./run.sh analytics reports accuracy`

**Назначение**: Оценка качества ML моделей. Анализирует насколько точно predictor предсказывает времена отклика.

```bash
# Общий анализ точности
./run.sh analytics reports accuracy

# Только проблемные случаи (точность < 80%)
./run.sh analytics reports accuracy --threshold=0.8

# Анализ конкретного deployment'а
./run.sh analytics reports accuracy --deployment=deployment-1
```

#### 4. Performance Report - Анализ производительности

**Команда**: `./run.sh analytics reports performance`

**Назначение**: Глубокий анализ производительности системы. Статистика времени отклика и выявление bottleneck'ов.

```bash
# Общий анализ производительности
./run.sh analytics reports performance

# Только медленные запросы
./run.sh analytics reports performance --slow-only

# Анализ сбоев
./run.sh analytics reports performance --failed-only
```

#### 5. Quality Report - Оценка качества роутинга

**Команда**: `./run.sh analytics reports quality`

**Назначение**: Сравнительный анализ эффективности разных стратегий роутинга (Hybrid Scoring vs Least Busy vs ML Only).

### Анализ данных

Система аналитики предоставляет детальные данные для анализа качества роутинга через PostgreSQL базу данных. 

#### Основные возможности анализа

**Принятие решений роутером:**
- Анализ каждого запроса с детализацией всех рассмотренных endpoints
- Hybrid scores и ранжирование для каждого deployment
- Причины выбора конкретного endpoint
- Сравнение предсказанного и фактического времени ответа

**Производительность системы:**
- Точность ML предсказаний по deployment'ам и времени  
- Эффективность hybrid scoring алгоритма
- Тренды нагрузки и производительности по часам/дням
- Сравнение различных стратегий балансировки нагрузки

**Оптимизация роутинга:**
- Анализ конкуренции между endpoints в каждом запросе
- Выявление субоптимальных решений роутера
- Распределение нагрузки между deployment'ами
- Качество предсказаний по провайдерам и моделям

### Пользовательские запросы

#### SQL запросы для анализа

```sql
-- Топ 10 самых медленных запросов за последний день
SELECT 
    request_id,
    model_name,
    selected_deployment,
    actual_response_time,
    ml_prediction_time,
    ABS(actual_response_time - ml_prediction_time) as prediction_error
FROM routing_decisions 
WHERE created_at >= NOW() - INTERVAL '1 day'
    AND actual_response_time IS NOT NULL
ORDER BY actual_response_time DESC 
LIMIT 10;

-- Анализ точности предсказаний по типам запросов
SELECT 
    request_type,
    COUNT(*) as total_requests,
    AVG(ABS(actual_response_time - ml_prediction_time) / actual_response_time * 100) as avg_error_percent,
    PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY ABS(actual_response_time - ml_prediction_time) / actual_response_time * 100) as median_error_percent
FROM routing_decisions 
WHERE created_at >= NOW() - INTERVAL '7 days'
    AND actual_response_time IS NOT NULL 
    AND ml_prediction_time IS NOT NULL
    AND actual_response_time > 0
GROUP BY request_type
ORDER BY avg_error_percent;

-- Эффективность балансировки нагрузки
SELECT 
    selected_deployment,
    COUNT(*) as request_count,
    AVG(actual_response_time) as avg_response_time,
    PERCENTILE_CONT(0.95) WITHIN GROUP (ORDER BY actual_response_time) as p95_response_time,
    AVG(load_penalty) as avg_load_penalty
FROM routing_decisions 
WHERE created_at >= NOW() - INTERVAL '1 day'
    AND actual_response_time IS NOT NULL
GROUP BY selected_deployment
ORDER BY request_count DESC;
```

#### Выполнение пользовательских запросов

```bash
# Выполнение SQL запроса из файла
./run.sh analytics query --file custom_analysis.sql

# Выполнение запроса с выводом в CSV
./run.sh analytics query --file analysis.sql --format csv --output results.csv

# Интерактивный режим SQL
./run.sh analytics query --interactive
```

### Экспорт данных

```bash
# Экспорт всех данных за период
./run.sh analytics data export --from "2024-01-01" --to "2024-01-31" --format json

# Экспорт конкретной таблицы
./run.sh analytics data export --table routing_decisions --format csv

# Экспорт с фильтрацией
./run.sh analytics data export --table routing_decisions --where "success = true" --format parquet
```

## Мониторинг аналитики

### Проверка здоровья системы

```bash
# Общая проверка здоровья
./run.sh analytics health check

# Детальная диагностика
./run.sh analytics health detailed

# Проверка подключения к БД
./run.sh analytics health database

# Проверка очереди обработки
./run.sh analytics health queue
```

Пример вывода:

```
=== Статус системы аналитики ===
✓ Подключение к базе данных: OK
✓ Миграции: Актуальные (версия 20240115_003)
✓ Очередь обработки: 23 элементов (норма)
✓ Производительность записи: 1,247 записей/сек
⚠ Размер базы данных: 2.3 GB (рекомендуется очистка)
✓ Индексы: Оптимизированы
✓ Резервное копирование: Последнее 2 часа назад

Рекомендации:
- Выполнить очистку данных старше 90 дней
- Проверить настройки автоматического резервного копирования
```

### Метрики производительности

```bash
# Статистика производительности
./run.sh analytics stats performance

# Статистика использования
./run.sh analytics stats usage

# Статистика точности
./run.sh analytics stats accuracy
```

### Алерты и уведомления

Настройка алертов в `.env`:

```bash
# Настройки алертов
ANALYTICS_ALERTS_ENABLED=true
ANALYTICS_ALERT_WEBHOOK=https://your-webhook-url.com/alerts

# Пороговые значения
ANALYTICS_ALERT_QUEUE_SIZE=5000
ANALYTICS_ALERT_ERROR_RATE=0.05
ANALYTICS_ALERT_DB_SIZE_GB=5.0
ANALYTICS_ALERT_PREDICTION_ERROR=0.30
```

Типы алертов:

1. **Переполнение очереди** - когда очередь превышает пороговое значение
2. **Высокий уровень ошибок** - когда процент ошибок превышает норму
3. **Проблемы с БД** - недоступность или медленная работа базы данных
4. **Низкая точность предсказаний** - когда точность ML падает ниже порога

## Устранение неполадок

### Частые проблемы миграций

#### Проблема: "Database schema is not empty"

**Ошибка**: `Error: P3005: The database schema is not empty`

**Причина:** Вы пытаетесь применить миграции к непустой базе без baseline.

**Решение:** Создайте baseline: `./run.sh analytics migrate baseline --host --yes`.

#### Проблема: "relation does not exist"

**Ошибка**: `Error: relation "..." does not exist`

**Причина:** Таблица, к которой обращается миграция, не существует. Вероятно, baseline был создан неправильно или таблицы LiteLLM еще не созданы.

**Решение:** 
1. Убедитесь, что LiteLLM запущен и создал таблицы
2. Пересоздайте baseline

#### Проблема: "Migration already applied"

**Ошибка**: `Error: Migration with name ... already applied.`

**Причина:** Миграция уже помечена как примененная в `_prisma_migrations`.

**Решение:** Если вам нужно применить ее заново, сначала отметьте ее как отмененную: `./run.sh analytics migrate resolve --rolled-back <migration_name> --host`, а затем выполните `deploy`.

### Частые проблемы системы

#### Проблема: Данные не записываются в базу

**Диагностика:**
```bash
./run.sh analytics health queue
./run.sh analytics health database
./run.sh docker logs litellm | grep analytics
```

**Решения:**
1. Проверить подключение к базе данных
2. Проверить настройки ANALYTICS_ENABLED
3. Проверить размер очереди и производительность
4. Перезапустить сервис аналитики

#### Проблема: Медленная работа запросов

**Диагностика:**
```bash
./run.sh analytics stats performance
./run.sh analytics query --file "EXPLAIN ANALYZE SELECT * FROM routing_decisions LIMIT 1000;"
```

**Решения:**
1. Проверить индексы базы данных
2. Очистить старые данные
3. Оптимизировать запросы
4. Увеличить ресурсы базы данных

#### Проблема: Переполнение очереди

**Диагностика:**
```bash
./run.sh analytics health queue
./run.sh debug status
```

**Решения:**
1. Увеличить ANALYTICS_BATCH_SIZE
2. Уменьшить ANALYTICS_FLUSH_INTERVAL
3. Увеличить ресурсы базы данных
4. Временно отключить аналитику

### Безопасность команды reset

**Вопрос:** НЕ удаляет ли команда `./run.sh analytics migrate reset --force` лишнего?

**Короткий ответ:** Команда полностью безопасна для таблиц LiteLLM.

Она удаляет **только** таблицы, определенные в схеме `analytics/schema.prisma`.

**Что именно делает команда:**
- **УДАЛЯЕТ** только 6 таблиц Analytics (`LiteLLM_Models`, `LiteLLM_Deployments` и т.д.).
- **НЕ ТРОГАЕТ** ~30 таблиц LiteLLM (`LiteLLM_UserTable`, `LiteLLM_SpendLogs` и т.д.).
- Очищает историю миграций Analytics в таблице `_prisma_migrations`.

### Мониторинг целостности

Поскольку проблема с удалением таблиц является регулярной, критически важно настроить автоматическую проверку и восстановление.

Создайте скрипт `check_analytics_integrity.sh` и добавьте его в `cron`:

```bash
#!/bin/bash
# Ожидаемое количество таблиц Analytics
EXPECTED_TABLE_COUNT=6

# Получаем реальное количество таблиц
ACTUAL_TABLE_COUNT=$(psql "$DATABASE_URL_HOST" -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_name IN ('LiteLLM_Models', 'LiteLLM_Deployments', 'LiteLLM_PromptLogs', 'LiteLLM_PredictorLogs', 'LiteLLM_ResponseLogs', 'LiteLLM_EndpointAnalysis');" | tr -d ' ')

if [[ "$ACTUAL_TABLE_COUNT" != "$EXPECTED_TABLE_COUNT" ]]; then
    echo "$(date): CRITICAL - Analytics tables missing! Found $ACTUAL_TABLE_COUNT of $EXPECTED_TABLE_COUNT. Auto-restoring..."
    cd "$PROJECT_DIR"
    # Выполняем полное восстановление
    ./run.sh analytics migrate reset --force && \
    ./run.sh analytics migrate baseline --host --yes && \
    ./run.sh analytics migrate deploy --host
    echo "$(date): Restore complete."
fi
```

**Добавление в cron (проверка каждые 5 минут):**
```bash
*/5 * * * * /path/to/check_analytics_integrity.sh >> /var/log/analytics_monitor.log 2>&1
```

### Отказоустойчивость

#### Принципы безопасности

1. **Неблокирующие операции**: Все записи в БД асинхронные
2. **Graceful degradation**: При ошибках БД переход на mock режим
3. **Изоляция ошибок**: Проблемы с аналитикой не влияют на роутинг
4. **Retry логика**: Автоматические повторные попытки

#### Обработка ошибок

```python
try:
    await analytics_logger.log_analytics_data(...)
except Exception as e:
    # Ошибка логируется, но не поднимается
    logger.error(f"Analytics error: {e}")
    # Основной поток продолжает работу
```

### Примеры использования

#### Базовая интеграция

```python
from analytics import RouterAnalyticsLogger

# Создание логгера
logger = RouterAnalyticsLogger(enabled=True)

# Интеграция с LiteLLM (автоматически)
# или явно:
# litellm.success_callback.append(logger)
```

#### Продвинутая интеграция

```python
from analytics.integration_example import ExampleCustomRouter

# Создание роутера с аналитикой
router = ExampleCustomRouter()
await router.initialize()

# Использование как обычного роутера
deployment = await router.get_available_deployment(
    model="gpt-3.5-turbo",
    messages=[{"role": "user", "content": "Hello!"}],
    request_kwargs={"litellm_call_id": "req-123"}
)
```

#### Кастомная обработка

```python
# Создание собственных данных аналитики
router_data = create_router_analysis_data(
    request_id="custom-req-456",
    model_name="custom-model",
    predictor_results={
        "selected_deployment_id": "best-deployment",
        "predicted_time": 1.5,
        "selection_method": "custom_logic"
    }
)

# Добавление в metadata
add_router_analysis_to_metadata(request_kwargs, router_data)
```

### Команды отладки

```bash
# Отладка очереди аналитики
./run.sh debug analytics queue

# Отладка производительности БД
./run.sh debug analytics database

# Отладка миграций
./run.sh debug analytics migrations

# Проверка целостности данных
./run.sh analytics validate data-integrity
```

### Восстановление после сбоев

#### Восстановление очереди

```bash
# Очистка заблокированной очереди
./run.sh analytics queue clear

# Перезапуск обработчика очереди
./run.sh analytics queue restart

# Повторная обработка неудачных записей
./run.sh analytics queue retry-failed
```

#### Восстановление базы данных

```bash
# Проверка целостности БД
./run.sh analytics database check-integrity

# Восстановление индексов
./run.sh analytics database rebuild-indexes

# Восстановление из резервной копии
./run.sh analytics backup restore latest
```

### Мониторинг и диагностика

#### Проверка работоспособности

```python
from analytics.config import config
from analytics.db_client import get_analytics_db_client

# Проверка конфигурации
print(config.to_dict())

# Тест подключения к БД
client = get_analytics_db_client()
await client.initialize()
```

#### Логирование

Все компоненты используют структурированное логирование с префиксами:

- `ANALYTICS:` - Основные события аналитики
- `DB_CLIENT:` - События базы данных  
- `MIGRATIONS:` - События миграций
- `INTEGRATION:` - События интеграции

## Оптимизация производительности

### Настройки производительности

```bash
# Оптимизация для высокой нагрузки
ANALYTICS_BATCH_SIZE=500
ANALYTICS_FLUSH_INTERVAL=10
ANALYTICS_DB_POOL_SIZE=20
ANALYTICS_MAX_QUEUE_SIZE=50000

# Оптимизация для экономии ресурсов
ANALYTICS_BATCH_SIZE=50
ANALYTICS_FLUSH_INTERVAL=60
ANALYTICS_DB_POOL_SIZE=5
ANALYTICS_MAX_QUEUE_SIZE=5000
```

### Партиционирование таблиц

Для больших объемов данных рекомендуется партиционирование:

```sql
-- Партиционирование по дате для routing_decisions
CREATE TABLE routing_decisions_y2024m01 PARTITION OF routing_decisions
FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');

CREATE TABLE routing_decisions_y2024m02 PARTITION OF routing_decisions
FOR VALUES FROM ('2024-02-01') TO ('2024-03-01');
```

### Архивирование данных

```bash
# Автоматическое архивирование данных старше 6 месяцев
./run.sh analytics archive --older-than 180 --compress

# Восстановление архивных данных
./run.sh analytics archive restore archive_2023_q4.tar.gz
```

### Справочник команд миграций

Все команды запускаются через `./run.sh analytics migrate ... --host`.

| Команда | Описание |
| :--- | :--- |
| `status` | Показывает статус миграций: какие применены, какие ожидают. |
| `deploy` | Применяет все ожидающие миграции. |
| `baseline --yes` | Создает и помечает baseline-миграцию на основе текущей БД. |
| `create --name <n>` | Создает новый файл миграции на основе изменений в `schema.prisma`. |
| `reset --force` | **(ОПАСНО)** Сбрасывает БД **только для схемы Analytics**. Безопасно для таблиц LiteLLM. |
| `resolve --applied <n>` | Вручную помечает миграцию как примененную. |
| `resolve --rolled-back <n>` | Вручную помечает миграцию как отмененную. |

### Команды ORM и базы данных

| Команда | Docker режим | Host режим | Описание |
| :--- | :--- | :--- | :--- |
| `generate` | `./run.sh analytics database generate` | `./run.sh analytics database generate --host` | **Генерирует типы для Analytics** на основе `schema.prisma`. Результат в `analytics/generated/`. |
| `fetch` | `./run.sh analytics database fetch` | Не поддерживается | **Скачивает Prisma Query Engine binaries** для Docker контейнера. Необходимо для схемных операций. |
| `status` | `./run.sh analytics database status` | `./run.sh analytics database status --host` | Тестирует подключение к базе данных. |
| `schema` | `./run.sh analytics database schema` | `./run.sh analytics database schema --host` | Проверяет синтаксис схемы `analytics/schema.prisma`. |
| `tables` | `./run.sh analytics database tables` | `./run.sh analytics database tables --host` | Показывает список таблиц в базе данных. |

## Связанные документы

- [Архитектура системы](ARCHITECTURE.md) - Полная диаграмма архитектуры всей системы
- [Руководство по операциям](OPERATIONS_GUIDE.md) - Команды управления системой
- [Руководство по тестированию](TESTING_GUIDE.md) - Тестирование аналитических компонентов
- [Справочник API](API_REFERENCE.md) - API для работы с аналитикой
- [Устранение неполадок](TROUBLESHOOTING.md) - Решение проблем с аналитикой

---

*Следующий раздел: [Руководство по тестированию](TESTING_GUIDE.md)*