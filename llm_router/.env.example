# Настройки для подключения к LiteLLM API
LITELLM_API_BASE=http://localhost:4000
LITELLM_MASTER_KEY=sk-1234
LITELLM_SALT_KEY=sk-1234
LITELLM_LOG=DEBUG

# Настройки для GPUStack
GPUSTACK_URL=http://localhost:80
GPUSTACK_KEY='gpustack_8f00805fb5acb81f_a0485b214b3f357cdf6088a5b3dc62b9'
GPUSTACK_INTERNAL=http://host.docker.internal:80

GPUSTACK_TOKEN='5ecf7945a9eada730c6bd15bbdd95310'
GPUSTACK_PASSWORD='mED^Z2Pr3qfQ'

# Настройки для сервиса предиктора времени ответа
PREDICT_URL=http://localhost:8007
PREDICT_URL_INTERNAL=http://host.docker.internal:8007
PREDICT_TIMEOUT=5

# Настройки базы данных
DATABASE_URL="********************************************/litellm"
DATABASE_URL_HOST="postgresql://llmproxy:dbpassword9090@localhost:5432/litellm"

# Настройки миграции litellm
DISABLE_SCHEMA_UPDATE=True

# Параметры миграции аналитики
USE_ANALYTICS_MIGRATE=True
ANALYTICS_MIGRATION_DIR=
DISABLE_ANALYTICS_SCHEMA_UPDATE=
ANALYTICS_ENABLED=true
