#!/usr/bin/env python3
"""
Test external predictor service functionality.
"""
from pathlib import Path
import asyncio
import logging
import os
import sys

from llm_router.config.service_config import ServiceConfig
from llm_router.logging_utils import get_logger
from llm_router.predictor.predictor_client import PredictorClient
import requests


sys.path.append(str(Path(__file__).parent.parent))
sys.path.insert(0, str(Path(__file__).parent.parent.parent))
logger = get_logger(__name__)


def test_predictor_client():
    """Test predictor client functionality."""
    config = ServiceConfig()
    predict_url = config.get_service_url("predict")
    logger.info(f"TEST: Testing predictor service at: {predict_url}")
    logger.debug(
        f"TEST: Environment PREDICT_URL: {os.getenv('PREDICT_URL', 'not set')}"
    )
    client = PredictorClient(base_url=predict_url)
    logger.info("TEST: Starting predictor health check...")
    if client.health_check():
        logger.info("TEST: PREDICTOR: Health check successful")
        logger.info("TEST: PREDICTOR: Service is healthy and responding")
        logger.info("TEST: Testing prediction functionality...")
        try:
            predicted_time = client.predict(
                task_type="chat",
                prompt="Hello, this is a test prompt for the predictor service.",
                hardware="gpu_a100",
                model="chat_instruct",
                instances=1,
                requests=1,
            )
            logger.info(
                f"TEST: PREDICTOR: Chat prediction successful: {predicted_time:.3f}s"
            )
        except Exception as e:
            logger.error(f"TEST: PREDICTOR: Chat prediction failed: {e}")
        try:
            predicted_time = client.predict(
                task_type="code",
                prompt="Write a Python function to calculate fibonacci numbers",
                hardware="gpu_v100",
                model="chat_coder",
                instances=1,
                requests=1,
            )
            logger.info(
                f"TEST: PREDICTOR: Code prediction successful: {predicted_time:.3f}s"
            )
        except Exception as e:
            logger.error(f"TEST: PREDICTOR: Code prediction failed: {e}")
    else:
        logger.error("TEST: PREDICTOR: Health check failed - service not available")
        return False
    client.close()
    logger.info("TEST: PREDICTOR: Test completed")
    return True


def main():
    """Main test function."""
    import argparse

    parser = argparse.ArgumentParser(description="Test predictor service")
    parser.add_argument("--predict-url", help="Predictor service URL")
    parser.add_argument(
        "--test-functionality",
        action="store_true",
        help="Test prediction functionality (requires working service)",
    )
    args = parser.parse_args()
    if args.predict_url:
        os.environ["PREDICT_URL"] = args.predict_url
    logger.info("TEST: Starting predictor service tests")
    try:
        config = ServiceConfig()
        if config.is_service_available("predict"):
            logger.info("TEST: PREDICTOR: Service connectivity confirmed")
            if args.test_functionality:
                success = test_predictor_client()
                if success:
                    logger.info("TEST: PREDICTOR: All tests passed!")
                    return 0
                else:
                    logger.error("TEST: PREDICTOR: Some tests failed!")
                    return 1
            else:
                logger.info(
                    "TEST: PREDICTOR: Connectivity test passed (use --test-functionality for full test)"
                )
                return 0
        else:
            logger.error("TEST: PREDICTOR: Service not available")
            return 1
    except ImportError as e:
        logger.warning(f"TEST: PREDICTOR: Could not import service config: {e}")
        return 1
    except Exception as e:
        logger.error(f"TEST: PREDICTOR: Unexpected error: {e}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
