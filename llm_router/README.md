# LLM Router с интеллектуальной маршрутизацией и гибридной балансировкой нагрузки

> **[Полная документация проекта](DOCUMENTATION.md)** | **[Архитектура системы](docs/PROJECT_ARCHITECTURE.md)** | **[Техническая документация](docs/DOCUMENTATION_INDEX.md)** | **[GPUStack Integration](docs/GPUSTACK_INTEGRATION_README.md)** | **[Система аналитики](docs/ANALYTICS_INTEGRATION.md)**

## 🔧 Отладка и поддержка

> **[🚨 Проблема Least-Busy Load Balancing - Краткое резюме](docs/LEAST_BUSY_QUICK_SUMMARY.md)** | **[📋 Полное руководство по отладке](docs/LEAST_BUSY_DEBUGGING_GUIDE.md)**

---

## Зачем это нужно?

**Проблема**: LiteLLM Router распределяет запросы случайно или по простым алгоритмам (round-robin, least_busy), не учитывая:
- Реальную производительность каждого endpoint'а для конкретного типа запроса
- Сложность конкретного запроса и его характеристики
- Характеристики железа (GPU vs CPU, память, архитектура)
- Гибридную балансировку нагрузки ML + real-time tracking

**Наше решение**: Интеллектуальный роутер с гибридной балансировкой нагрузки, который:
- Анализирует каждый запрос (длина, сложность, тип задачи)
- Предсказывает время ответа для каждого endpoint'а через HTTP API
- Комбинирует ML-предсказания с real-time данными о загруженности
- Выбирает оптимальный endpoint с учетом hybrid scoring algorithm
- Автоматически адаптируется к изменениям в производительности
- **Ведет детальную аналитику** всех решений роутинга с сохранением в базу данных

## Результат

**До**: Случайное распределение → неравномерная нагрузка → медленные ответы → отсутствие данных  
**После**: Гибридная интеллектуальная маршрутизация → оптимальная балансировка → быстрые ответы → **полная аналитика**

### Реальные преимущества с различными конфигурациями

**Пример из нашей конфигурации:**
- **3xH100** с `Qwen2.5-72B-Instruct-AWQ` (72B параметров) - для сложных задач
- **A6000** с `Qwen2.5-Coder-32B-Instruct-AWQ` - оптимизировано для средних нагрузок  
- **gpu_a100** с `chat_coder` - специализировано для кода
- **gpu_rtx4090** (3 инстанса) с `Qwen2.5-Coder-14B-Instruct` - для быстрых ответов

Роутер автоматически выбирает лучший endpoint на основе:
- Типа задачи (chat vs code)
- Размера и сложности промпта  
- Текущей нагрузки на каждое железо
- Характеристик конкретного GPU

## Архитектура

Подробные диаграммы архитектуры системы см. в **[docs/PROJECT_ARCHITECTURE.md](docs/PROJECT_ARCHITECTURE.md)**.

### Ключевые компоненты:

1. **`custom_router.py`** - Основная ML-стратегия маршрутизации
   - Интегрируется с LiteLLM Proxy через CustomRoutingStrategyBase
   - Использует гибридный алгоритм балансировки нагрузки
   - Подключается к внешнему predictor service через HTTP API
   - Анализирует входящие запросы и выбирает оптимальный endpoint

2. **`predictor_client.py`** - HTTP клиент для predictor service
   - Асинхронные запросы к внешнему ML-сервису предсказания времени
   - Поддержка различных типов задач (chat, code)
   - Обработка ошибок и fallback механизмы
   - Подробное логирование для отладки

3. **`deployment_selector.py`** - Логика выбора оптимального deployment'а
   - Гибридный scoring: ML predictions + least-busy data
   - Интеграция с LiteLLM least-busy tracking system
   - Балансировка между скоростью и нагрузкой
   - Fallback стратегии при недоступности данных

4. **`predictor_integration.py`** - Интеграция с ML predictor service
   - Извлечение параметров для ML-предсказаний
   - Определение типа задачи (chat/code) из контента запроса
   - Обработка различных форматов запросов (messages, input text)
   - Кэширование предсказаний для производительности

5. **`least_busy_monitor.py`** - Мониторинг least-busy tracking
   - Интеграция с LiteLLM callback system
   - Real-time отслеживание активных запросов по deployments
   - Debug logging для диагностики load balancing
   - Совместимость с LiteLLM Proxy архитектурой

6. **`litellm_patch.py`** - Патч для интеграции с LiteLLM
   - Автоматическое внедрение custom routing strategy в LiteLLM Router
   - Перехватывает инициализацию Router и настраивает custom routing
   - Использует константы из `constants.py` для настройки роутера
   - Обеспечивает бесшовную интеграцию без модификации кода LiteLLM
   - Автоматически определяет тип задачи по имени модели

7. **GPUStack Integration** - Интеграция для улучшения ML predictor
   - **`gpustack_integration.py`** - получение физических данных о моделях и GPU
   - **`gpustack_physical_machines_cache.py`** - кэширование с 96%+ efficiency
   - **`predictor_integration.py`** - передача physical model paths в ML predictor
   - **Результат**: Более точные ML предсказания благодаря физическим данным

8. **Analytics System** - Система детального логирования и аналитики
   - **`analytics/router_analytics_logger.py`** - асинхронное логирование решений роутинга
   - **Расширенная схема БД** - детальные таблицы для анализа производительности
   - **Metadata integration** - передача данных через LiteLLM callbacks
   - **Результат**: Полная трассировка и анализ качества роутинга

## Гибридная балансировка нагрузки

### Принцип работы

Система использует формулу hybrid scoring:
```
hybrid_score = (1 - weight) × ml_prediction + weight × load_penalty
```

где:
- **ml_prediction** - время ответа, предсказанное ML-моделью (в секундах)
- **load_penalty** - штраф за текущую нагрузку на deployment
- **weight** - вес балансировки (по умолчанию 0.3, настраивается в конфигурации)

### Компоненты scoring

**ML Prediction (70% веса по умолчанию):**
- Анализ типа задачи (chat vs code)
- Учет характеристик железа (GPU/CPU типы)
- Предсказание времени на основе длины и сложности промпта
- Внешний predictor service через HTTP API

**Load Penalty (30% веса по умолчанию):**
- Real-time количество активных запросов на deployment
- Данные из LiteLLM least-busy tracking system
- Автоматическое обновление через callback system
- Штраф пропорционален текущей нагрузке

**GPUStack Physical Data (для ML predictor):**
- Реальная информация о GPU: "NVIDIA GeForce RTX 4090 Laptop GPU"
- Физические пути моделей: передача без префикса `/models/`
- Cache efficiency: 96%+ hit rate с детальной статистикой
- **Назначение**: Улучшение точности ML предсказаний

### Упрощенная настройка балансировки

Система использует фиксированные константы для упрощения конфигурации:

```python
# В constants.py - фиксированные настройки системы
LOAD_BALANCING_WEIGHT = 0.3  # 30% вес для load penalty, 70% для ML prediction
DEFAULT_TASK_TYPE = "chat"
GPUSTACK_ENABLED = True
GPUSTACK_CACHE_TTL = 30  # секунды

# Автоматическое определение типа задачи по имени модели
MODEL_NAME_TO_TASK_TYPE_MAPPING = {
    "chat_instruct": "chat",
    "chat_coder": "code"
}
```

```yaml
# В router_litellm/litellm.config.yaml - упрощенная конфигурация
general_settings:
  debug_level: DEBUG
  set_verbose: true
  timeout: 30

litellm_settings:
  cache: true
  cache_params:
    type: local

model_list:
  - model_name: chat_instruct              # Автоматически определяется как "chat"
    litellm_params:
      model: litellm_proxy/qwen2.5-1.5b-instruct-q4km-1
      api_base: http://host.docker.internal:80/v1
      api_key: gpustack_your_api_key_here
    model_info:
      id: instruct-endpoint-1
      path: /models/Qwen/Qwen2.5-1.5B-Instruct-GGUF/qwen2.5-1.5b-instruct-q4_k_m.gguf
      
  - model_name: chat_coder                 # Автоматически определяется как "code"
    litellm_params:
      model: litellm_proxy/qwen2.5-1.5b-instruct-q4km-1
      api_base: http://host.docker.internal:80/v1
      api_key: gpustack_your_api_key_here
    model_info:
      id: coder-endpoint-1
      path: /models/Qwen/Qwen2.5-1.5B-Instruct-GGUF/qwen2.5-1.5b-instruct-q4_k_m.gguf
```

**Важно:** Система больше не использует `router_settings` или `managed_models` в конфигурации. Все настройки роутинга управляются через константы в `constants.py`, а тип задачи определяется автоматически по имени модели.

## Быстрый старт с Docker (Рекомендуется)

### Шаг 1: Подготовка файлов

Создайте файл `.env` с настройками:

```bash
cat > .env << EOF
# LiteLLM Authentication
LITELLM_MASTER_KEY=sk-1234
LITELLM_SALT_KEY=sk-1234
LITELLM_LOG=DEBUG
PYTHONUNBUFFERED=1

# Predictor Service Configuration
PREDICT_URL=http://localhost:8008
PREDICT_TIMEOUT=5

# Optional: GPUStack integration
GPUSTACK_KEY=your-gpustack-api-key
GPUSTACK_TOKEN=your-gpustack-token
EOF
```

**Важно**: Замените `sk-1234` на ваши реальные ключи!

### Настройки кеширования

Система использует **Local Cache** (in-memory) для хранения данных least-busy tracking:

```yaml
# В router_litellm/litellm.config.yaml
litellm_settings:
  cache: true
  cache_params:
    type: local  # Используется local cache
```

**Преимущества Local Cache:**
- Быстрее доступ к данным (без сетевых вызовов)
- Упрощенное развертывание (не требует Redis сервера)
- Достаточно для single-instance deployments
- Автоматическое управление памятью LiteLLM

**GPUStack Cache для ML Predictor:**
Система использует отдельный кэш для физических данных, передаваемых в ML predictor:
- **TTL**: 30 секунд для model paths и GPU info
- **High efficiency**: 96%+ cache hit rate в production
- **Rate limiting**: автоматическое управление частотой запросов к GPUStack
- **Purpose**: Быстрый доступ к физическим данным для улучшения ML предсказаний

### Шаг 2: Конфигурация моделей

Отредактируйте `router_litellm/litellm.config.yaml`:

```yaml
general_settings:
  debug_level: DEBUG
  set_verbose: true
  timeout: 30

litellm_settings:
  cache: true
  cache_params:
    type: local

# LiteLLM model list - ваши endpoint'ы с реальными моделями
model_list:
  - model_name: "chat_instruct"
    litellm_params:
      model: "litellm_proxy/qwen2.5-1.5b-instruct-q4km-1"
      api_base: "http://host.docker.internal:80/v1"
      api_key: "gpustack_your_api_key_here"
    model_info:
      id: "instruct-endpoint-1"  # Уникальный ID для роутера
      path: "/models/Qwen/Qwen2.5-1.5B-Instruct-GGUF/qwen2.5-1.5b-instruct-q4_k_m.gguf"
      
  - model_name: "chat_instruct"  # Та же модель, другой endpoint
    litellm_params:
      model: "litellm_proxy/qwen2.5-1.5b-instruct-q4km-2"
      api_base: "http://host.docker.internal:80/v1"
      api_key: "gpustack_your_api_key_here"
    model_info:
      id: "instruct-endpoint-2"
      path: "/models/Qwen/Qwen2.5-1.5B-Instruct-GGUF/qwen2.5-1.5b-instruct-q4_k_m.gguf"

  - model_name: "chat_coder"     # Отдельная модель для кода
    litellm_params:
      model: "litellm_proxy/qwen2.5-1.5b-instruct-q4km-1"
      api_base: "http://host.docker.internal:80/v1"
      api_key: "gpustack_your_api_key_here"
    model_info:
      id: "coder-endpoint-1"
      path: "/models/Qwen/Qwen2.5-1.5B-Instruct-GGUF/qwen2.5-1.5b-instruct-q4_k_m.gguf"
```

**Примечание:** Система автоматически определяет тип задачи по имени модели (`chat_instruct` → "chat", `chat_coder` → "code") и применяет соответствующие настройки роутинга из констант.

### Шаг 3: Запуск с Docker Compose

```bash
# Перейдите в директорию проекта
cd filin-router

# Запустите роутер с Docker Compose
docker-compose up --build

# Или в фоновом режиме
docker-compose up --build -d

# Просмотр логов в реальном времени
docker-compose logs -f litellm
# Или используя имя контейнера:
# docker logs -f filin-litellm-router
```

### Шаг 4: Проверка работы

```bash
# Проверьте health endpoint
curl http://localhost:4000/health

# Проверьте подключение к predictor service
curl http://localhost:8008/health

# Отправьте тестовый запрос с реальной моделью
curl -X POST http://localhost:4000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer sk-1234" \
  -d '{
    "model": "chat_instruct",
    "messages": [{"role": "user", "content": "Напиши функцию на Python для сортировки массива"}]
  }'

# Тест специализированной модели для кода
curl -X POST http://localhost:4000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer sk-1234" \
  -d '{
    "model": "chat_coder",
    "messages": [{"role": "user", "content": "Оптимизируй этот алгоритм сортировки для больших данных"}]
  }'
```

### Шаг 5: Мониторинг интеллектуальной маршрутизации

Проверьте логи для отслеживания работы гибридного алгоритма:

```bash
# Смотрите логи в реальном времени
docker-compose logs -f litellm
# Или используя имя контейнера напрямую:
# docker logs -f filin-litellm-router

# Ключевые события в логах:
[INIT] Routing strategy initialization completed successfully
[PREDICTOR] Connected to service: {'status': 'healthy', ...}
[LEAST_BUSY] Successfully enabled least-busy tracking for custom router

# При каждом запросе увидите hybrid scoring:
[ROUTING] SMART ROUTING REQUEST
[PREDICTOR] Prediction successful - 1.234s for chat task
[LEAST_BUSY] Current load for endpoint-1: 2 requests
[HYBRID] ML prediction: 1.234s, Load penalty: 0.6s, Final score: 1.044s
[SELECTION] Chosen deployment: endpoint-2 with hybrid score: 0.987s
[ROUTING] ROUTING DECISION COMPLETE!
```

## Как работает интеллектуальная маршрутизация

### 1. Анализ входящего запроса
```python
# Router анализирует тип задачи и извлекает prompt
prompt = "Напиши функцию на Python для вычисления факториала"
task_type = "code"  # Автоматически определяется по content
```

### 2. Получение доступных endpoint'ов
```python
# LiteLLM предоставляет список здоровых endpoint'ов
healthy_endpoints = [
    {"model_info": {"id": "instruct-endpoint-1"}, ...},
    {"model_info": {"id": "instruct-endpoint-2"}, ...},
]
```

### 3. Гибридный scoring для каждого endpoint'а
```python
for endpoint in healthy_endpoints:
    # ML-предсказание через HTTP API с реальными параметрами
    ml_prediction = predictor_client.predict(
        task_type="code",
        prompt=prompt,
        hardware="3xH100",                     # Реальные характеристики железа
        model="Qwen2.5-72B-Instruct-AWQ"      # Конкретная модель
    )  # Результат: 1.234s
    
    # Real-time нагрузка из least-busy tracking
    active_requests = least_busy_tracker.get_current_load(endpoint_id)  # 2 запроса
    load_penalty = active_requests * 0.3  # 0.6s
    
    # Гибридный score
    hybrid_score = 0.7 * ml_prediction + 0.3 * load_penalty
    # = 0.7 * 1.234 + 0.3 * 0.6 = 1.044s
```

### 4. Выбор оптимального endpoint'а
```python
# Сортировка по hybrid score и выбор лучшего
selected_endpoint = min(scores, key=lambda x: x.hybrid_score)
# Результат: endpoint с минимальным гибридным score
```

### Примеры реального роутинга

**Сценарий 1: Простой вопрос**
```
Запрос: "Как дела?"
→ Система выберет: fast-endpoint (gpu_rtx4090, 3 инстанса)
→ Обоснование: Простая задача, быстрое железо с множественными инстансами
```

**Сценарий 2: Сложная задача кодирования**
```
Запрос: "Напиши оптимизированный алгоритм сортировки для миллиарда записей"
→ Система выберет: coder-endpoint-1 (gpu_a100, chat_coder, task_type=code)
→ Обоснование: Код задача + специализированный endpoint для кода
```

**Сценарий 3: Комплексный анализ**
```
Запрос: "Проанализируй этот длинный текст и дай детальные рекомендации..."
→ Система выберет: instruct-endpoint-3 (3xH100, Qwen2.5-72B-Instruct-AWQ)
→ Обоснование: Сложная задача + самая мощная конфигурация (72B модель)
```

**Сценарий 4: Высокая нагрузка**
```
Ситуация: Все мощные endpoints загружены
→ Система выберет: instruct-endpoint-2 (A6000, меньше активных запросов)
→ Обоснование: Hybrid scoring учитывает текущую нагрузку
```

## Управление конфигурацией

### Автоматическое обновление API ключей

```bash
# Обновите GPUSTACK_KEY в .env файле
echo "GPUSTACK_KEY=your_new_api_key_here" >> .env

# Автоматически обновите все api_key в router_litellm/litellm.config.yaml
./run.sh config update

# Перезапустите сервисы
./run.sh docker services restart
```

### Обновление GPUStack токена

```bash
# Автоматически получить токен из контейнера и обновить .env
./run.sh gpustack token update
```

### Валидация системы

```bash
# Полная проверка системы
./run.sh config validation check-system

# Тестирование роутинга
./run.sh test endpoint

# Проверка базы данных
./run.sh config validation check-db
```

### Система аналитики

Система аналитики обеспечивает детальное логирование решений роутинга и анализ производительности.

**Команды управления:** См. [docs/COMMANDS_DOCUMENTATION.md](docs/COMMANDS_DOCUMENTATION.md) и [docs/COMMANDS_QUICK_REFERENCE.md](docs/COMMANDS_QUICK_REFERENCE.md)

**Возможности аналитики:**
- Детальное логирование hybrid scoring решений
- Анализ точности ML предсказаний vs реальное время ответа  
- Трассировка endpoint анализа с ranking и selection rationale
- Асинхронное логирование в PostgreSQL для высокой производительности
- Отказоустойчивость: ошибки аналитики не влияют на роутинг

**Документация:** [docs/ANALYTICS_INTEGRATION.md](docs/ANALYTICS_INTEGRATION.md) | [docs/ANALYTICS_MIGRATIONS.md](docs/ANALYTICS_MIGRATIONS.md)

## Мониторинг и отладка

### Логи гибридной маршрутизации

Каждое решение роутера подробно логируется с префиксами:

```
[ROUTING] - Общие решения маршрутизации
[PREDICTOR] - Взаимодействие с ML-сервисом  
[LEAST_BUSY] - Real-time tracking нагрузки
[HYBRID] - Гибридный scoring algorithm
[SELECTION] - Выбор оптимального endpoint'а
[FALLBACK] - Fallback стратегии при ошибках
```

### Prometheus метрики (опционально)

```bash
# Откройте Prometheus UI
open http://localhost:9090

# Доступные метрики:
- litellm_requests_total
- litellm_request_duration_seconds  
- litellm_router_predictions_total
- litellm_hybrid_scores_histogram
```

### Отладка проблем

#### Predictor service недоступен
```bash
# Проверьте подключение
curl http://localhost:8008/health

# В логах должно быть:
[PREDICTOR] Connected to service: {'status': 'healthy', ...}
[PREDICTOR] Connection error: Connection refused  # Если недоступен
```

#### Least-busy tracking не работает
```bash
# Проверьте Local cache
./run.sh config validation check-db

# В логах должно быть:
[LEAST_BUSY] Successfully enabled least-busy tracking for custom router
[LEAST_BUSY] Current load for endpoint-1: N requests
```

#### Hybrid scoring проблемы
```bash
# Проверьте настройки констант
grep "LOAD_BALANCING_WEIGHT" constants.py

# В логах должно быть:
[CONFIG] load_balancing_weight = 0.3
[HYBRID] ML prediction: X.Xs, Load penalty: Y.Ys, Final score: Z.Zs
```

## Альтернативные способы запуска

### Разработка с Python

```bash
# Установите зависимости
pip install litellm requests

# Запустите роутер
python start_litellm_with_router.py

# С конкретным конфигом
python start_litellm_with_router.py --config router_litellm/litellm.config.yaml
```

### Интеграция с существующим LiteLLM

Для интеграции роутера с существующим LiteLLM deployment достаточно импортировать `litellm_patch`:

```python
# В начале вашего скрипта, ПЕРЕД импортом litellm
import sys
sys.path.insert(0, '/path/to/filin-router')
import litellm_patch  # Автоматически применяет custom routing strategy

# Затем продолжайте с вашим обычным кодом LiteLLM
from litellm.proxy.proxy_cli import run_server
```

**Как работает интеграция:**

1. **Автоматическое внедрение:** `litellm_patch.py` автоматически патчит `litellm.Router.__init__`
2. **Обнаружение конфигурации:** При создании Router система использует константы из `constants.py`
3. **Автоматическое определение типа задачи:** По имени модели определяется тип задачи
4. **Создание strategy:** Автоматически создает и подключает `LLMTimePredictorRoutingStrategy`

**Поддерживаемые источники конфигурации:**
- YAML файлы конфигурации LiteLLM (model_list, general_settings, litellm_settings)
- Константы системы (constants.py)
- Environment variables
- Runtime параметры

**Совместимость:** Патч сохраняет полную совместимость с существующими LiteLLM deployments - система автоматически активируется при наличии подходящих моделей.

## Преимущества нового подхода

1. **Упрощенная конфигурация**: Константы вместо сложных YAML настроек
2. **Автоматическое определение**: Тип задачи определяется по имени модели
3. **Оптимальная производительность**: Комбинация ML-предсказаний и real-time данных
4. **Адаптивность**: Автоматическое реагирование на изменения нагрузки
5. **Отказоустойчивость**: Множественные fallback механизмы
6. **Прозрачность**: Подробные логи каждого решения
7. **Простота развертывания**: Минимальные изменения в LiteLLM setup
8. **Масштабируемость**: Поддержка любого количества endpoint'ов
9. **Предсказуемость**: Фиксированные настройки исключают конфликты конфигурации

## Сравнение с базовой маршрутизацией

| Характеристика | Стандартный LiteLLM | Наш Гибридный Router |
|---|---|---|
| **Алгоритм выбора** | Round-robin/случайно | Hybrid ML + least-busy |
| **Анализ запроса** | Нет | Да (тип, длина, сложность) |
| **Учет железа** | Нет | Да (через GPUStack API) |
| **Real-time нагрузка** | Опционально | Интегрированно |
| **ML-предсказания** | Нет | Да (внешний HTTP API) |
| **Гибридный scoring** | Нет | Да (фиксированные веса) |
| **Fallback стратегии** | Базовые | Многоуровневые |
| **Настройка** | YAML конфигурация | Константы в коде |
| **Определение типа задачи** | Ручная настройка | Автоматически по имени |
| **Производительность** | Случайная | Оптимизированная |

---

## Что изменилось в последней версии

### 🎉 Упрощенная архитектура (v2.0)
- **Убраны** сложные `router_settings` и `managed_models` из YAML
- **Добавлены** фиксированные константы в `constants.py` для предсказуемого поведения
- **Автоматическое** определение типа задачи по имени модели
- **Упрощенное** развертывание - достаточно настроить только `model_list`

### 🚀 Улучшенная производительность
- Local cache для быстрого доступа к данным
- Оптимизированная GPUStack интеграция с кешированием
- Асинхронная система аналитики
- Улучшенные fallback механизмы

### 🛠️ Современные инструменты
- Обновленные команды `./run.sh` для управления системой
- Интегрированная валидация и мониторинг
- Улучшенная диагностика и отладка
- PostgreSQL для надежного хранения данных

## GPUStack Integration Environment Variables

Для полной интеграции с GPUStack добавьте в `.env` файл:

```bash
# GPUStack API Configuration (обязательно)
GPUSTACK_KEY=your-gpustack-api-key          # API ключ
GPUSTACK_URL=http://gpustack-server:80      # URL GPUStack API

# Alternative Authentication (опционально)
GPUSTACK_TOKEN=your-token                   # Token-based auth
GPUSTACK_PASSWORD=your-password             # Password-based auth

# Docker Networking (для контейнеров)
GPUSTACK_INTERNAL=http://host.docker.internal:80  # Internal Docker URL

# Cache Configuration (опционально)
GPUSTACK_CACHE_TTL=300                      # TTL кэша в секундах (default: 300)
GPUSTACK_RATE_LIMIT=30                      # Rate limiting в секундах (default: 30)

# Logging (опционально)
GPUSTACK_LOG_LEVEL=INFO                     # Уровень логирования
GPUSTACK_DEBUG=false                        # Debug режим
```

**Проверка интеграции:**
```bash
# Тестирование подключения к GPUStack
./run.sh gpustack info workers

# Мониторинг cache efficiency
docker logs filin-litellm-router 2>&1 | grep "CACHE:" | tail -5
```

## Справочник команд управления

- **[docs/COMMANDS_DOCUMENTATION.md](docs/COMMANDS_DOCUMENTATION.md)** - **Полная документация всех команд `./run.sh`**
  - Подробное описание 44+ команд по категориям
  - Примеры использования и параметры
  - Техническая информация о Docker контейнерах и скриптах

- **[docs/COMMANDS_QUICK_REFERENCE.md](docs/COMMANDS_QUICK_REFERENCE.md)** - **Краткий справочник команд**
  - Быстрые команды для повседневной работы
  - Организация по функциональным группам
  - Основные операции: Docker, Config, GPUStack, Test, Debug

## Дополнительная документация

- **[DOCUMENTATION.md](DOCUMENTATION.md)** - Полная документация проекта
- **[docs/GPUSTACK_INTEGRATION_README.md](docs/GPUSTACK_INTEGRATION_README.md)** - GPUStack Integration (детальная документация)
- **[docs/ANALYTICS_INTEGRATION.md](docs/ANALYTICS_INTEGRATION.md)** - Система аналитики и детальное логирование
- **[docs/SQL_ANALYTICS_QUERIES.md](docs/SQL_ANALYTICS_QUERIES.md)** - SQL запросы для аналитики
- **[docs/](docs/)** - Техническая документация
- **[docs/HYBRID_LOAD_BALANCING_BEHAVIOR.md](docs/HYBRID_LOAD_BALANCING_BEHAVIOR.md)** - Детали гибридного алгоритма
- **[docs/README_TESTING.md](docs/README_TESTING.md)** - Руководство по тестированию
- **[docs/CUSTOM_ROUTER_SETTINGS_STORAGE.md](docs/CUSTOM_ROUTER_SETTINGS_STORAGE.md)** - Кастомные настройки 